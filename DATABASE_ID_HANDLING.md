# Database Structure & ID Handling Guide

## Overview

This document outlines the database structure, ID field types, and proper handling patterns for cross-table queries in the MailPallet application.

## Critical ID Type Differences

### UUID-Based Primary Keys (string)
These tables use UUID strings as their primary key:

- `mp_admins.id`: string (UUID) - Direct mapping to Supabase auth.users.id
- `mp_ecommerce_categories.id`: string (UUID)
- `mp_ecommerce_exchange_rates.id`: string (UUID)
- `mp_ecommerce_orders.id`: string (UUID)
- `mp_ecommerce_products.id`: string (UUID)

### Integer-Based Primary Keys (number)
These tables use auto-incrementing integers as their primary key:

- `mp_users.id`: number (auto-incrementing) - Internal database ID
- `users_parcels_details.id`: number (auto-incrementing)
- `users_address.user_id`: number
- `consolidation_parcel_requests.id`: number
- `mailpallet_locations.id`: number
- `mp_shipping_methods.id`: number
- `mp_shipping_routes.id`: number

## Cross-Table Reference Patterns

### Admin-User Context Switching
When dealing with both admin and user contexts, use **email** as the universal identifier:
- `mp_admins.email`: string
- `mp_users.email`: string
- `users_address.EmailAddress`: string

### User-Related Foreign Keys
These fields reference `mp_users.id` (integer):
- `mp_ecommerce_orders.user_id`: number | null → `mp_users.id`
- `users_parcels_details.user_id`: number | null → `mp_users.id`
- `users_address.user_id`: number → `mp_users.id`
- `consolidation_parcel_requests.user_id`: number → `mp_users.id`

### Auth Integration Fields
These UUID fields link to Supabase auth.users.id:
- `mp_users.uuid`: string | null → auth.users.id
- `users_parcels_details.uuid`: string | null → auth.users.id
- `users_address.uuid`: string | null → auth.users.id
- `consolidation_parcel_requests.uuid`: string | null → auth.users.id

## Common Query Patterns

### 1. Admin Authentication Check
```typescript
// ✅ Correct - mp_admins.id is UUID
const { data: admin, error: adminError } = await supabase
  .from("mp_admins")
  .select("id")
  .eq("id", authUser.id) // authUser.id is UUID from auth
  .single();
```

### 2. User Orders Query (Most Critical)
```typescript
// ❌ WRONG - This will fail!
query = query.eq("user_id", authUser.id); // authUser.id is UUID, user_id expects integer

// ✅ CORRECT - Convert auth UUID to database integer ID
const { data: userData, error: userError } = await supabase
  .from("mp_users")
  .select("id")
  .eq("uuid", authUser.id)
  .single();

if (userData) {
  query = query.eq("user_id", userData.id); // userData.id is integer
}
```

### 3. User Profile Query
```typescript
// ✅ Correct - mp_users.uuid links to auth
const { data, error } = await supabase
  .from("mp_users")
  .select("*")
  .eq("uuid", authUser.id)
  .single();
```

### 4. Parcel Ownership Check
```typescript
// ✅ Correct - using uuid field for auth linkage
const { data: parcel, error } = await supabase
  .from("users_parcels_details")
  .select("uuid, user_id")
  .eq("tracking_id", trackingId)
  .single();

if (parcel && parcel.uuid === authUser.id) {
  // User owns this parcel
}
```

### 5. Cross-Table Join with Address
```typescript
// ✅ Correct - both use integer user_id
const { data: userData, error: userError } = await supabase
  .from("mp_users")
  .select("id")
  .eq("uuid", authUser.id)
  .single();

const { data: addressData, error: addressError } = await supabase
  .from("users_address")
  .select("*")
  .eq("user_id", userData.id) // Both are integers
  .single();
```

## Fixed Issues

### 1. Parcels Insert Field Name (FIXED)
**Issue**: Used `user_uuid` instead of `uuid`
```typescript
// ❌ Before
.insert([{ ...parcelData, user_uuid: userUuid, user_id: userId }])

// ✅ After
.insert([{ ...parcelData, uuid: userUuid, user_id: userId }])
```

### 2. Orders List User ID Mismatch (FIXED)
**Issue**: Directly used auth UUID where integer expected
```typescript
// ❌ Before
if (!isAdmin) {
  userId = authUser.id; // UUID assigned to field expecting integer
}
query = query.eq("user_id", userId);

// ✅ After
if (!isAdmin) {
  const { data: userData } = await supabase
    .from("mp_users")
    .select("id")
    .eq("uuid", authUser.id)
    .single();
  userId = userData.id.toString();
}
query = query.eq("user_id", parseInt(userId, 10));
```

## Validation Checklist

When writing database queries, always verify:

1. **Admin queries**: Use `mp_admins.id` (UUID) = `auth.users.id` (UUID)
2. **User profile queries**: Use `mp_users.uuid` (UUID) = `auth.users.id` (UUID)
3. **Order/Parcel ownership**: Convert auth UUID to `mp_users.id` (integer) for foreign keys
4. **Cross-table joins**: Ensure ID types match (integer-to-integer, UUID-to-UUID)
5. **Email queries**: Safe to use across all user-related tables

## Security Implications

### ✅ Safe Patterns
- Using email for cross-context user identification
- Converting auth UUID to database integer for foreign key relationships
- Validating ownership before allowing data access

### ⚠️ Risk Patterns
- Direct UUID-to-integer comparisons (will always fail)
- Assuming all ID fields use the same type
- Missing user ownership validation in multi-tenant scenarios

## Performance Notes

- Integer joins are more efficient than UUID joins
- Email lookups should be indexed for performance
- Consider caching user ID mappings for frequently accessed data

## Future Considerations

If database structure changes are possible:
1. Standardize on either UUID or integer across all tables
2. Add proper foreign key constraints
3. Consider composite indexes for frequently joined fields

For now, work within the existing structure using the patterns documented above.