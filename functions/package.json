{"name": "functions", "scripts": {"build": "tsc", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"cors": "^2.8.5", "firebase-admin": "^11.8.0", "firebase-functions": "^5.1.0", "resend": "^1.0.0"}, "devDependencies": {"typescript": "^4.9.0"}, "private": true}