"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendEmail = void 0;
const resend_1 = require("resend");
const admin = require("firebase-admin");
const https_1 = require("firebase-functions/v2/https");
const cors = require("cors");
// Initialize Firebase Admin
admin.initializeApp();
// Configure CORS
const corsHandler = cors({ origin: true });
exports.sendEmail = (0, https_1.onRequest)({ secrets: ["RESEND_API_KEY"] }, async (request, response) => {
    return corsHandler(request, response, async () => {
        try {
            if (request.method !== "POST") {
                response.status(405).json({ error: "Method not allowed" });
                return;
            }
            const resend = new resend_1.Resend(process.env.RESEND_API_KEY);
            const { to, subject, html } = request.body;
            if (!to || !subject || !html) {
                response.status(400).json({ error: "Missing required fields" });
                return;
            }
            const data = await resend.emails.send({
                from: "MailPallet <<EMAIL>>",
                to: [to],
                subject,
                html,
            });
            response.json(data);
        }
        catch (error) {
            console.error("Error sending email:", error);
            response.status(500).json({ error: "Failed to send email" });
        }
    });
});
//# sourceMappingURL=index.js.map