import * as cors from 'cors';
import { Resend } from "resend";
import * as admin from "firebase-admin";
import { Request, Response } from 'express';
import { onRequest } from 'firebase-functions/v2/https';

// Initialize Firebase Admin
admin.initializeApp();

// Configure CORS
const corsHandler = cors({ origin: true });

export const sendEmail = onRequest({ secrets: ["RESEND_API_KEY"] }, async (request: Request, response: Response) => {
  return corsHandler(request, response, async () => {
    try {
      if (request.method !== "POST") {
        response.status(405).json({ error: "Method not allowed" });
        return;
      }

      const resend = new Resend(process.env.RESEND_API_KEY);
      const { to, subject, html } = request.body;

      if (!to || !subject || !html) {
        response.status(400).json({ error: "Missing required fields" });
        return;
      }

      const data = await resend.emails.send({
        from: "MailPallet <<EMAIL>>",
        to: [to],
        subject,
        html,
      });

      response.json(data);
    } catch (error) {
      console.error("Error sending email:", error);
      response.status(500).json({ error: "Failed to send email" });
    }
  });
});
