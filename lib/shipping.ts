// MailPallet-WebApp/lib/shipping.ts
/**
 * Shipping helpers - Best-Fit Decreasing packer with oversize checks
 *
 * Replaces the previous greedy packer with a Best-Fit Decreasing (BFD) style
 * algorithm. Behavior:
 * - Expand items by quantity into unit-level entries for packing.
 * - Validate each unit: missing/invalid dimensions or weight will throw.
 * - If any single unit is too large for the largest available tier, the packer
 *   throws a JS Error describing the offending unit (volume & weight).
 * - Units are sorted by volume (descending) and placed into the parcel whose
 *   resulting parcel-tier price is smallest (best-fit). If no existing parcel
 *   can accept the unit, a new parcel is created (the unit must fit into the
 *   largest tier or an error would have been thrown earlier).
 *
 * This module remains pure/synchronous and depends on tier definitions found in
 * `lib/constants.ts`.
 */

import {
  SHIPPING_TIERS,
  ShippingTier,
  findShippingTier,
  computeShippingBaseGbp as computeShippingBaseGbpFromConstants,
  computeShippingForQuantityBaseGbp as computeShippingForQuantityBaseGbpFromConstants,
} from "./constants";

/**
 * Public types
 */
export type DimensionsCm = {
  length: number;
  width: number;
  height: number;
};

export type ShippableItem = {
  id?: string;
  dimensions_cm?: DimensionsCm | null;
  weight_kg?: number | null;
  quantity?: number; // defaults to 1
};

/**
 * Compute cubic volume (cm^3) from L×W×H.
 * Returns a number >= 0. If dimensions are missing/invalid returns 0.
 */
export function volumeFromDimensions(dim?: DimensionsCm | null): number {
  if (!dim) return 0;
  const { length, width, height } = dim;
  if (
    typeof length !== "number" ||
    typeof width !== "number" ||
    typeof height !== "number" ||
    !isFinite(length) ||
    !isFinite(width) ||
    !isFinite(height) ||
    length <= 0 ||
    width <= 0 ||
    height <= 0
  ) {
    return 0;
  }
  return length * width * height;
}

/**
 * Safely parse weight in kg (defaults to 0 if missing/invalid).
 */
export function weightFromValue(weightKg?: number | null): number {
  if (typeof weightKg !== "number" || !isFinite(weightKg) || weightKg < 0)
    return 0;
  return weightKg;
}

/**
 * Select the shipping tier for a single item (volume cm³, weight kg).
 * Returns the matching ShippingTier or null if the data is invalid or doesn't
 * match any tier.
 */
export function selectTierForItem(
  dimensions: DimensionsCm | null | undefined,
  weightKg: number | null | undefined,
): ShippingTier | null {
  const vol = volumeFromDimensions(dimensions ?? null);
  const wt = weightFromValue(weightKg ?? null);
  if (vol <= 0 || wt <= 0) return null;
  return findShippingTier(vol, wt);
}

/**
 * Compute base price (GBP) for a single item (no quantity multiplication).
 * Returns 0 if data missing/invalid.
 */
export function computeItemShippingBaseGbp(item: ShippableItem): number {
  const vol = volumeFromDimensions(item.dimensions_cm ?? null);
  const wt = weightFromValue(item.weight_kg ?? null);
  if (vol <= 0 || wt <= 0) return 0;
  return computeShippingBaseGbpFromConstants(vol, wt);
}

/**
 * Compute base price * quantity using constants helper.
 */
export function computeItemShippingForQuantityBaseGbp(
  item: ShippableItem,
): number {
  const qty =
    typeof item.quantity === "number" && item.quantity > 0 ? item.quantity : 1;
  const vol = volumeFromDimensions(item.dimensions_cm ?? null);
  const wt = weightFromValue(item.weight_kg ?? null);
  if (vol <= 0 || wt <= 0 || qty <= 0) return 0;
  return computeShippingForQuantityBaseGbpFromConstants(vol, wt, qty);
}

/**
 * Compute shipping for a collection of items.
 * By default: simple sum(per-item base * qty).
 * If packTogether = true: treat as one parcel with aggregated vol/weight.
 */
export function computeAggregateShippingBaseGbp(
  items: ShippableItem[],
  options?: { packTogether?: boolean },
): number {
  if (!Array.isArray(items) || items.length === 0) return 0;
  const packTogether = options?.packTogether ?? false;
  if (!packTogether) {
    return items.reduce(
      (sum, it) => sum + computeItemShippingForQuantityBaseGbp(it),
      0,
    );
  }
  // aggregate and compute single parcel
  let totalVol = 0;
  let totalWeight = 0;
  for (const it of items) {
    const qty =
      typeof it.quantity === "number" && it.quantity > 0 ? it.quantity : 1;
    totalVol += volumeFromDimensions(it.dimensions_cm ?? null) * qty;
    totalWeight += weightFromValue(it.weight_kg ?? null) * qty;
  }
  if (totalVol <= 0 || totalWeight <= 0) return 0;
  return computeShippingBaseGbpFromConstants(totalVol, totalWeight);
}

/**
 * Error thrown when a single unit is too large to fit in any tier (oversize).
 * We throw a plain Error (JS Error) with a descriptive message as requested.
 */

/**
 * Best-Fit Decreasing packer
 *
 * Algorithm:
 * - Expand units by quantity (each unit has quantity = 1).
 * - Validate units: if a unit has invalid dims/weight -> throw Error.
 * - If a single unit does not fit in the largest tier -> throw Error.
 * - Sort units by volume descending.
 * - For each unit, find the best parcel to place it into:
 *    * Evaluate existing parcels: for each parcel, compute newVol/newWt if unit were added.
 *    * Determine whether newVol/newWt can be covered by any tier; if yes compute
 *      the base price for that tier.
 *    * Choose the parcel that leads to the minimum resulting parcel base price
 *      (best-fit, preferring to avoid opening new parcels).
 * - If no existing parcel can accept the unit, start a new parcel (the unit has
 *   already been validated to fit the largest tier).
 *
 * Returns:
 *  {
 *    parcels: [ { items, totalVolumeCm3, totalWeightKg, tier, basePriceGbp } ],
 *    totalPriceGbp
 *  }
 *
 * Throws: JS Error for invalid or oversize single units.
 */
export function packItemsIntoParcels(items: ShippableItem[]): {
  parcels: Array<{
    items: ShippableItem[]; // units (each entry quantity === 1)
    totalVolumeCm3: number;
    totalWeightKg: number;
    tier: ShippingTier | null;
    basePriceGbp: number;
  }>;
  totalPriceGbp: number;
} {
  if (!Array.isArray(items)) {
    return { parcels: [], totalPriceGbp: 0 };
  }

  // Pre-calc largest tier limits for oversize checks.
  const largestTier = SHIPPING_TIERS[SHIPPING_TIERS.length - 1];

  // Expand by quantity into unit-level entries
  const units: ShippableItem[] = [];
  for (const it of items) {
    const qty =
      typeof it.quantity === "number" && it.quantity > 0 ? it.quantity : 1;
    for (let i = 0; i < qty; i++) {
      units.push({
        id: it.id,
        dimensions_cm: it.dimensions_cm ?? null,
        weight_kg: it.weight_kg ?? null,
        quantity: 1,
      });
    }
  }

  // Validate units and check oversize (throw if invalid or too large)
  for (const u of units) {
    const vol = volumeFromDimensions(u.dimensions_cm ?? null);
    const wt = weightFromValue(u.weight_kg ?? null);

    if (vol <= 0 || wt <= 0) {
      const idPart = u.id ? ` id=${u.id}` : "";
      throw new Error(
        `Invalid dimensions or weight for unit${idPart}. volume_cm3=${vol}, weight_kg=${wt}`,
      );
    }

    // If unit exceeds largest tier bounds -> throw
    if (vol > largestTier.maxVolumeCm3 || wt > largestTier.maxWeightKg) {
      const idPart = u.id ? ` id=${u.id}` : "";
      throw new Error(
        `Unit too large for available shipping tiers${idPart}. volume_cm3=${vol} (max ${largestTier.maxVolumeCm3}), weight_kg=${wt} (max ${largestTier.maxWeightKg})`,
      );
    }
  }

  // Sort units by volume descending (Best-Fit Decreasing variant uses sorted input)
  units.sort(
    (a, b) =>
      volumeFromDimensions(b.dimensions_cm ?? null) -
      volumeFromDimensions(a.dimensions_cm ?? null),
  );

  type Parcel = {
    units: ShippableItem[];
    totalVolume: number;
    totalWeight: number;
  };

  const parcels: Parcel[] = [];

  // Helper: given volume & weight, find best matching tier (the first tier that fits)
  // Since tiers are ordered from smallest to largest in constants, findShippingTier returns appropriate tier.
  const findTierFor = (volume: number, weight: number): ShippingTier | null =>
    findShippingTier(volume, weight);

  // Place each unit using Best-Fit heuristic: choose the parcel which yields the minimum
  // resulting base price (so we prefer to add to parcels that keep price low).
  for (const u of units) {
    const uVol = volumeFromDimensions(u.dimensions_cm ?? null);
    const uWt = weightFromValue(u.weight_kg ?? null);

    let bestParcelIndex: number | null = null;
    let bestParcelPrice: number | null = null;

    // Evaluate placing into existing parcels
    for (let i = 0; i < parcels.length; i++) {
      const p = parcels[i];
      const newVol = p.totalVolume + uVol;
      const newWt = p.totalWeight + uWt;
      const tier = findTierFor(newVol, newWt);
      if (tier) {
        const priceIfPlaced = tier.priceGbp;
        if (bestParcelPrice === null || priceIfPlaced < bestParcelPrice) {
          bestParcelPrice = priceIfPlaced;
          bestParcelIndex = i;
        }
      }
    }

    if (bestParcelIndex !== null) {
      // Place into chosen parcel
      const p = parcels[bestParcelIndex];
      p.units.push(u);
      p.totalVolume += uVol;
      p.totalWeight += uWt;
      continue;
    }

    // No existing parcel could accept the unit; create a new parcel (unit fits largest tier due to validation)
    parcels.push({
      units: [u],
      totalVolume: uVol,
      totalWeight: uWt,
    });
  }

  // Convert parcel internals to summaries with tier & base price
  const parcelSummaries = parcels.map((p) => {
    const tier = findTierFor(p.totalVolume, p.totalWeight);
    const basePrice = tier ? tier.priceGbp : largestTier.priceGbp; // fallback (shouldn't happen because we validated)
    return {
      items: p.units,
      totalVolumeCm3: p.totalVolume,
      totalWeightKg: p.totalWeight,
      tier,
      basePriceGbp: basePrice,
    };
  });

  const totalPriceGbp = parcelSummaries.reduce((s, p) => s + p.basePriceGbp, 0);

  return {
    parcels: parcelSummaries,
    totalPriceGbp,
  };
}

/**
 * Convenience: derive a tier name for UI.
 */
export function tierNameFor(
  dimensions: DimensionsCm | null | undefined,
  weightKg?: number | null,
): string {
  const tier = selectTierForItem(dimensions ?? null, weightKg ?? null);
  return tier ? tier.name : "Oversize / Unavailable";
}
