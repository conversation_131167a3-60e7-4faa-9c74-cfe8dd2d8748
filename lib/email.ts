import { OrderItem } from "@/data/models/order.model";
import { unified } from "unified";
import remarkParse from "remark-parse";
import remarkRehype from "remark-rehype";
import rehypeStringify from "rehype-stringify";


export const getEmailHtmlContent = (firstName: string, lastName: string, trackingId: string) => {
  return `
  <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>New Parcel Received</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f4f4f4; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
                .header { background-color: #101c50; color: white; padding: 30px; text-align: center; }
                .content { padding: 30px; }
                .footer { background-color: #f8f8f8; color: #666; padding: 20px; text-align: center; font-size: 14px; }
                h1 { margin: 0; font-size: 28px; }
                .icon { font-size: 20px; margin-right: 10px; vertical-align: middle; }
                .info-row { margin-bottom: 20px; }
                .button { display: inline-block; background-color: #101c50; color: white !important; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 20px; }
                a { color: #101c50; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>MailPallet UK</h1>
                </div>
                <div class="content">
                    <p>Dear ${firstName} ${lastName},</p>
                    <div class="info-row">
                        <span class="icon">📦</span> We have received your parcel.
                    </div>
                    <div class="info-row">
                        <span class="icon">🔢</span> <strong>Tracking ID:</strong> ${trackingId}
                    </div>
                    <div class="info-row">
                        <span class="icon">🔍</span> You can view your parcel details in your "My Parcels" section within your Dashboard.
                    </div>
                    <div class="info-row">
                        <span class="icon">⏰</span> Please note: Parcels held for longer than 30 days from the receipt date will incur additional charges.
                    </div>
                    <p>Thank you for choosing MailPallet UK for your shipping needs.</p>
                    <a href="https://mailpallet.com" class="button">View My Dashboard</a>
                </div>
                <div class="footer">
                    <p>© ${new Date().getFullYear()} MailPallet UK. All rights reserved.</p>
                    <p>Visit our website: <a href="https://mailpallet.com">mailpallet.com</a></p>
                </div>
            </div>
        </body>
        </html>
`;
};

export const getParcelActionNotification = (firstName: string | undefined, lastName: string | undefined, action: string, trackingId: string) => {
    return `
    <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Parcel Action Notification</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f4f4f4; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
                .header { background-color: #101c50; color: white; padding: 30px; text-align: center; }
                .content { padding: 30px; }
                .footer { background-color: #f8f8f8; color: #666; padding: 20px; text-align: center; font-size: 14px; }
                h1 { margin: 0; font-size: 28px; }
                .icon { font-size: 20px; margin-right: 10px; vertical-align: middle; }
                .info-row { margin-bottom: 20px; }
                .button { display: inline-block; background-color: #101c50; color: white !important; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 20px; }
                a { color: #101c50; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>MailPallet UK</h1>
                </div>
                <div class="content">
                    <p>Dear ${firstName} ${lastName},</p>
                    <div class="info-row">
                        <span class="icon">⚠️</span> ${action} the parcel with tracking ID ${trackingId}.
                    </div>
                    <div class="info-row">
                    <p>Thank you for choosing MailPallet UK for your shipping needs.</p>
                    <a href="https://mailpallet.com" class="button">View My Dashboard</a>
                </div>
                <div class="footer">
                    <p>© ${new Date().getFullYear()} MailPallet UK. All rights reserved.</p>
                    <p>Visit our website: <a href="https://mailpallet.com">mailpallet.com</a></p>
                </div>
            </div>
        </body>
        </html>
    `;
}

export const getOrderSuccessful = (firstName: string, lastName: string, items: OrderItem[]) => {
  // Helper to format money consistently
  const formatMoney = (value: number, currency?: string) => {
    try {
      if (typeof Intl !== "undefined" && currency) {
        return new Intl.NumberFormat("en-GB", { style: "currency", currency }).format(value);
      }
    } catch (e) {
      // fallback
    }
    return `${currency ? currency + " " : ""}${value.toFixed(2)}`;
  };

  // Helper to convert markdown to HTML
  const markdownToHtml = (markdown: string): string => {
    if (!markdown) return '';
    try {
      const file = unified()
        .use(remarkParse)
        .use(remarkRehype)
        .use(rehypeStringify)
        .processSync(markdown);
      return String(file);
    } catch (error) {
      // Fallback to plain text if markdown conversion fails
      return markdown;
    }
  };

  const itemsHtml = items.map((item, idx) => {
    const p = item.product_snapshot || ({} as any);
    const v = item.variant_snapshot;
    // Variant option key-values to HTML
    const variantOptionsHtml = v && v.option_values
      ? Object.entries(v.option_values).map(([k, val]) => `<div style="font-size:13px;color:#555;"><strong>${k}:</strong> ${val}</div>`).join("")
      : "";

    const imageHtml = v && v.image && v.image.url
      ? `<div style="margin-top:8px;"><img src="${v.image.url}" alt="${p.title || ''}" style="max-width:120px;border-radius:6px;"></div>`
      : "";

    const productDetails = `
      <div style="padding:12px;border:1px solid #eee;border-radius:6px;margin-bottom:12px;display:flex;gap:12px;">
        <div style="flex:0 0 120px;align-self:flex-start;">
          ${imageHtml}
        </div>
        <div style="flex:1;">
          <div style="font-size:16px;color:#101c50;font-weight:600;margin-bottom:6px;">${p.title || "Product"}</div>
          <div style="font-size:13px;color:#666;margin-bottom:6px;line-height:1.4;">${p.description ? markdownToHtml(p.description.length > 200 ? p.description.slice(0, 200) + '…' : p.description) : ''}</div>
          <div style="font-size:13px;color:#555;margin-bottom:6px;">
            <strong>SKU:</strong> ${v && v.sku ? v.sku : (p.sku || 'N/A')}
            ${p.condition ? ` • <strong>Condition:</strong> ${p.condition}` : ''}
          </div>
          ${variantOptionsHtml}
          <div style="margin-top:8px;font-size:14px;color:#333;">
            <strong>Quantity:</strong> ${item.quantity} •
            <strong>Unit Price:</strong> ${formatMoney(item.unit_price, item.currency)} •
            <strong>Line Total:</strong> ${formatMoney(item.line_total, item.currency)}
          </div>
        </div>
      </div>
    `;

    return productDetails;
  }).join("");

  const orderSubtotal = items.reduce((s, it) => s + (it.line_total || 0), 0);

  return `
  <!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Order Successful</title>
    <style>
      body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f4f4f4; margin: 0; padding: 0; }
      .container { max-width: 700px; margin: 20px auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
      .header { background-color: #101c50; color: white; padding: 30px; text-align: center; }
      .content { padding: 30px; }
      .footer { background-color: #f8f8f8; color: #666; padding: 20px; text-align: center; font-size: 14px; }
      h1 { margin: 0; font-size: 28px; }
      .order-summary { border-top: 1px solid #eee; padding-top: 18px; margin-top: 12px; }
      .total-row { font-size: 16px; font-weight: 700; color: #101c50; margin-top: 12px; }
      a.button { display: inline-block; background-color: #101c50; color: white !important; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 20px; }
      .muted { color: #777; font-size: 13px; }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>MailPallet UK</h1>
      </div>
      <div class="content">
        <p>Dear ${firstName} ${lastName},</p>
        <p>Thank you — your order has been successfully placed. Below are the details of the items in your order:</p>

        <div class="order-items">
          ${itemsHtml || '<div class="muted">No items found in this order.</div>'}
        </div>

        <div class="order-summary">
          <div style="display:flex;justify-content:space-between;align-items:center;">
            <div class="muted">Subtotal</div>
            <div>${formatMoney(orderSubtotal, items[0]?.currency)}</div>
          </div>
          <div class="muted" style="margin-top:8px;">Shipping and discounts (if any) will be shown on your invoice in your account.</div>
          <div class="total-row" style="display:flex;justify-content:space-between;">
            <div>Total</div>
            <div>${formatMoney(orderSubtotal, items[0]?.currency)}</div>
          </div>
        </div>

        <p style="margin-top:18px;">You can view your full order and track shipping in your Dashboard.</p>
        <a href="https://mailpallet.com" class="button">View My Dashboard</a>
      </div>

      <div class="footer">
        <p>© ${new Date().getFullYear()} MailPallet UK. All rights reserved.</p>
        <p>Visit our website: <a href="https://mailpallet.com">mailpallet.com</a></p>
      </div>
    </div>
  </body>
  </html>
  `;
}
