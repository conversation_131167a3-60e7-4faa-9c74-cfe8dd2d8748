/**
 * MailPallet - Currency helpers
 * File: lib/currency.ts
 *
 * Intl-based currency formatting helpers used across the shop pages.
 *
 * Exports:
 *  - formatCurrencyIntl(amount, currencyCode, locale?, opts?)
 *  - getCurrencyFractionDigits(currencyCode)
 *  - roundToCurrency(amount, currencyCode)
 *  - formatCurrencyRounded(amount, currencyCode, locale?)
 *
 * Notes:
 *  - Uses `Intl.NumberFormat` where available; falls back to a conservative
 *    string fallback when Intl is not available.
 */

import { CURRENCY_SYMBOLS } from "@/lib/constants";

type IntlFormatOptions = Intl.NumberFormatOptions;

/**
 * Resolve a best-effort locale for formatting.
 * Prefer provided locale; otherwise use browser `navigator.language` when available,
 * and fall back to `'en-GB'`.
 */
function resolveLocale(locale?: string): string {
  if (locale && typeof locale === "string") return locale;
  if (typeof navigator !== "undefined" && navigator.language) {
    return navigator.language;
  }
  return "en-GB";
}

/**
 * Safely create an Intl.NumberFormat for currency.
 * Returns null if Intl is not available or construction fails.
 */
function createCurrencyFormatter(
  currencyCode: string,
  locale?: string,
  opts?: IntlFormatOptions,
): Intl.NumberFormat | null {
  try {
    if (typeof Intl === "undefined" || typeof Intl.NumberFormat === "undefined")
      return null;
    const resolvedLocale = resolveLocale(locale);
    return new Intl.NumberFormat(resolvedLocale, {
      style: "currency",
      currency: currencyCode,
      currencyDisplay: opts?.currencyDisplay || "symbol",
      maximumFractionDigits: opts?.maximumFractionDigits,
      minimumFractionDigits: opts?.minimumFractionDigits,
      ...opts,
    });
  } catch (e) {
    // If Intl is unavailable or throws, return null and let callers fallback.
    return null;
  }
}

/**
 * Get the number of decimal fraction digits typically used for a currency.
 * Uses Intl.NumberFormat.resolvedOptions() to read `maximumFractionDigits`.
 * Falls back to 2 when information is not available.
 */
export function getCurrencyFractionDigits(currencyCode: string): number {
  try {
    const fmt = createCurrencyFormatter(currencyCode);
    if (!fmt) return 2;
    const opts = fmt.resolvedOptions();
    if (typeof opts.maximumFractionDigits === "number") {
      return opts.maximumFractionDigits;
    }
    return 2;
  } catch {
    return 2;
  }
}

/**
 * Round a numeric amount to the correct number of fraction digits for the currency.
 * Returns a Number (not a formatted string).
 *
 * Example:
 *  roundToCurrency(12.3456, 'USD') -> 12.35
 */
export function roundToCurrency(amount: number, currencyCode: string): number {
  const digits = getCurrencyFractionDigits(currencyCode);
  const factor = Math.pow(10, digits);
  // Use a robust rounding approach
  return Math.round((amount + Number.EPSILON) * factor) / factor;
}

/**
 * Round a numeric value to a given number of decimal digits.
 * This is a small utility that generalizes the rounding logic used for currencies.
 *
 * Example:
 *  roundToDigits(12.3456, 2) -> 12.35
 */
export function roundToDigits(amount: number, digits: number): number {
  const factor = Math.pow(10, digits);
  return Math.round((amount + Number.EPSILON) * factor) / factor;
}

/**
 * Format a currency amount using Intl.NumberFormat.
 * - `amount` is a numeric value (expected already converted into the target currency).
 * - `currencyCode` is an ISO 4217 code like 'GBP', 'USD', 'GHS'.
 * - `locale` optionally overrides the locale (defaults to browser locale or 'en-GB').
 * - `opts` are additional Intl.NumberFormat options to pass through.
 *
 * Returns a localized currency string, or a safe fallback string if Intl isn't available.
 */
export function formatCurrencyIntl(
  amount: number,
  currencyCode: string,
  locale?: string,
  opts?: Partial<IntlFormatOptions>,
): string {
  try {
    const fmt = createCurrencyFormatter(
      currencyCode,
      locale,
      opts as IntlFormatOptions,
    );
    if (fmt) {
      return fmt.format(amount);
    }
    // Fallback: show currency code and a 2-decimal formatted number
    return `${currencyCode} ${amount.toFixed(2)}`;
  } catch {
    return `${currencyCode} ${amount.toFixed(2)}`;
  }
}

/**
 * Convenience: round then format for display.
 * Uses the currency's fraction digits for rounding, then formats with Intl.
 *
 * Example:
 *  formatCurrencyRounded(12.3456, 'USD') -> "$12.35"
 */
export function formatCurrencyRounded(
  amount: number,
  currencyCode: string,
  locale?: string,
): string {
  const rounded = roundToCurrency(amount, currencyCode);
  return formatCurrencyIntl(rounded, currencyCode, locale);
}

/**
 * Format a numeric amount by rounding to a specified number of digits and
 * formatting with Intl.NumberFormat. This helper does not query the currency's
 * canonical fraction digits; it uses the explicit `digits` parameter provided
 * by the caller. It also instructs Intl to use the same number of fraction
 * digits for consistent display.
 *
 * Example:
 *  formatCurrencyWithDigits(12.3456, 'USD', 2) -> "$12.35"
 */
export function formatCurrencyWithDigits(
  amount: number,
  currencyCode: string,
  digits: number,
  locale?: string,
): string {
  // Use the general rounding helper to round to the requested digits.
  const rounded = roundToDigits(amount, digits);

  // Prefer the preferred-symbol formatter which preserves locale ordering while
  // ensuring we use a readable currency symbol (falls back to Intl or fallback map).
  try {
    return formatCurrencyWithPreferredSymbol(rounded, currencyCode, locale, {
      minimumFractionDigits: digits,
      maximumFractionDigits: digits,
    });
  } catch {
    // Fallback: prefer the canonical CURRENCY_SYMBOLS map when available,
    // otherwise fall back to the ISO currency code.
    const symbol =
      (CURRENCY_SYMBOLS && CURRENCY_SYMBOLS[currencyCode]) || currencyCode;
    return `${symbol} ${rounded.toFixed(digits)}`;
  }
}

/**
 * Small utility to provide a human-readable fallback symbol map for environments
 * where Intl is unavailable and the caller prefers a symbol rather than the code.
 * This is intentionally minimal — Intl is preferred in modern environments.
 */
/*
  Legacy fallback symbol map removed.

  We now use the canonical `CURRENCY_SYMBOLS` map exported from
  `lib/constants.ts` as the single source of truth for currency glyphs
  (e.g. "NGN" -> "₦", "GHS" -> "₵"). If a symbol is not present in
  `CURRENCY_SYMBOLS`, the code falls back to using the ISO currency code
  (e.g. "XYZ") so the output remains deterministic.
*/

/**
 * A fallback formatter that mimics the earlier simple formatCurrency behavior.
 * This is used only if Intl is unavailable and caller explicitly asks for fallback usage.
 */
export function formatCurrencyFallback(
  amount: number,
  currencyCode: string,
): string {
  // Keep the original fallback visual behavior (2 decimal places)
  const digits = 2;
  const rounded = roundToDigits(amount, digits);
  const symbol =
    (CURRENCY_SYMBOLS && CURRENCY_SYMBOLS[currencyCode]) || currencyCode;
  return `${symbol} ${rounded.toFixed(digits)}`;
}

/**
 * Format a currency amount preferring localized Intl formatting but ensuring
 * a readable local symbol is used when Intl emits a 3-letter currency code
 * (for example some environments may render 'NGN' instead of '₦').
 *
 * Implementation notes:
 * - Uses Intl.NumberFormat.formatToParts to preserve locale ordering and spacing.
 * - If the `currency` part looks like a 3-letter code, we replace it with a
 *   preferred symbol from `currency-symbol-map` or the fallback map.
 * - Falls back to `formatCurrencyFallback` if Intl is not available or any error occurs.
 */
export function formatCurrencyWithPreferredSymbol(
  amount: number,
  currencyCode: string,
  locale?: string,
  opts?: Partial<IntlFormatOptions>,
): string {
  try {
    const fmt = createCurrencyFormatter(currencyCode, locale, {
      ...(opts || {}),
      currencyDisplay: "symbol",
    });

    if (!fmt || typeof (fmt as any).formatToParts !== "function") {
      return formatCurrencyFallback(amount, currencyCode);
    }

    const parts = (fmt as Intl.NumberFormat).formatToParts(amount);
    const currencyIndex = parts.findIndex((p) => p.type === "currency");

    if (currencyIndex === -1) {
      // No explicit currency part - return the Intl formatted string
      return fmt.format(amount);
    }

    const currencyPart = parts[currencyIndex];
    const partValue = (currencyPart.value || "").trim();

    // Heuristic: if Intl returned an uppercase 3-letter sequence (e.g. "NGN"),
    // replace it with a preferred symbol.
    const looksLikeCode = /^[A-Z]{3}$/.test(partValue.toUpperCase());

    if (looksLikeCode) {
      // Prefer symbol from the canonical CURRENCY_SYMBOLS map; if not present,
      // fall back to the ISO currency code itself so output remains readable.
      const symbol =
        (CURRENCY_SYMBOLS && CURRENCY_SYMBOLS[currencyCode]) || currencyCode;
      parts[currencyIndex] = { type: "currency", value: symbol };
      return parts.map((p) => p.value).join("");
    }

    // Intl already provided a symbol or suitable value — rejoin and return.
    return parts.map((p) => p.value).join("");
  } catch {
    // Any unexpected error -> conservative fallback
    return formatCurrencyFallback(amount, currencyCode);
  }
}

/**
 * Primary formatter (preferred everywhere in the app)
 *
 * This formatter uses the preferred-symbol logic (which preserves locale ordering
 * via formatToParts but substitutes known currency glyphs when Intl emits a
 * three-letter code). Exported as the default "main" formatter for UI use.
 */
export function formatCurrency(
  amount: number,
  currencyCode: string,
  locale?: string,
  opts?: Partial<IntlFormatOptions>,
): string {
  return formatCurrencyWithPreferredSymbol(amount, currencyCode, locale, opts);
}

/**
 * Strict Intl formatter (no symbol substitution).
 *
 * Export the existing Intl-first formatter under a distinct name so callers
 * who explicitly want native Intl behaviour can use it.
 */
export const formatCurrencyIntlStrict = formatCurrencyIntl;

export default {
  // Primary (recommended) formatter
  formatCurrency,
  // Expose strict Intl behaviour (named for clarity)
  formatCurrencyIntl: formatCurrencyIntlStrict,
  // Legacy / utility exports retained for compatibility
  formatCurrencyRounded,
  roundToCurrency,
  roundToDigits,
  getCurrencyFractionDigits,
  formatCurrencyFallback,
  formatCurrencyWithPreferredSymbol,
};
