'use client';

import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import { CartState, CartAction, CartProduct } from './types';
import { Product } from '@/data/models/product.model';

const CART_STORAGE_KEY = '__mp_cart';

// Load initial state from localStorage
const loadInitialState = (): CartState => {
  if (typeof window === 'undefined') return { items: [] };
  
  try {
    const savedCart = localStorage.getItem(CART_STORAGE_KEY);
    return savedCart ? JSON.parse(savedCart) : { items: [] };
  } catch (error) {
    console.error('Error loading cart from localStorage:', error);
    return { items: [] };
  }
};

// Save state to localStorage
const saveState = (state: CartState) => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(state));
  } catch (error) {
    console.error('Error saving cart to localStorage:', error);
  }
};

// Helper function to create a unique cart item key
function getCartItemKey(productId: string, selectedVariant?: any): string {
  if (selectedVariant?.variant_id) {
    return `${productId}_${selectedVariant.variant_id}`;
  }
  return productId;
}

// Helper function to check if two cart items are the same
function isSameCartItem(item: CartProduct, payload: Omit<CartProduct, 'quantity'>): boolean {
  const itemKey = getCartItemKey(item.id, item.selectedVariant);
  const payloadKey = getCartItemKey(payload.id, payload.selectedVariant);
  return itemKey === payloadKey;
}

// Cart reducer
function cartReducer(state: CartState, action: CartAction): CartState {
  switch (action.type) {
    case 'ADD_ITEM': {
      const existingItem = state.items.find(item => isSameCartItem(item, action.payload));

      if (existingItem) {
        // If item already exists (same product + same variant), increase quantity
        return {
          ...state,
          items: state.items.map(item =>
            isSameCartItem(item, action.payload)
              ? { ...item, quantity: item.quantity + 1 }
              : item
          ),
        };
      } else {
        // If new item (different product or different variant), add with quantity 1
        return {
          ...state,
          items: [...state.items, { ...action.payload, quantity: 1 }],
        };
      }
    }
    
    case 'REMOVE_ITEM': {
      return {
        ...state,
        items: state.items.filter(item => {
          const itemKey = getCartItemKey(item.id, item.selectedVariant);
          return itemKey !== action.payload;
        }),
      };
    }
    
    case 'UPDATE_QUANTITY': {
      const { cartItemKey, quantity } = action.payload;

      if (quantity <= 0) {
        // Remove item if quantity is 0 or less
        return {
          ...state,
          items: state.items.filter(item => {
            const itemKey = getCartItemKey(item.id, item.selectedVariant);
            return itemKey !== cartItemKey;
          }),
        };
      }

      return {
        ...state,
        items: state.items.map(item => {
          const itemKey = getCartItemKey(item.id, item.selectedVariant);
          return itemKey === cartItemKey ? { ...item, quantity } : item;
        }),
      };
    }
    
    case 'CLEAR_CART': {
      return {
        ...state,
        items: [],
      };
    }
    
    default:
      return state;
  }
}

// Create contexts
const CartContext = createContext<CartState | undefined>(undefined);
const CartDispatchContext = createContext<React.Dispatch<CartAction> | undefined>(undefined);

// Provider component
interface CartProviderProps {
  children: ReactNode;
}

export function CartProvider({ children }: CartProviderProps) {
  const [state, dispatch] = useReducer(cartReducer, null, loadInitialState);

  // Persist state changes to localStorage
  useEffect(() => {
    saveState(state);
  }, [state]);

  return (
    <CartContext.Provider value={state}>
      <CartDispatchContext.Provider value={dispatch}>
        {children}
      </CartDispatchContext.Provider>
    </CartContext.Provider>
  );
}

// Custom hooks
export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}

export function useCartDispatch() {
  const context = useContext(CartDispatchContext);
  if (context === undefined) {
    throw new Error('useCartDispatch must be used within a CartProvider');
  }
  return context;
}

// Helper functions
export function addToCart(dispatch: React.Dispatch<CartAction>, cartProduct: Omit<CartProduct, 'quantity'>) {
  dispatch({ type: 'ADD_ITEM', payload: cartProduct });
}

export function removeFromCart(dispatch: React.Dispatch<CartAction>, cartItem: CartProduct) {
  const cartItemKey = getCartItemKey(cartItem.id, cartItem.selectedVariant);
  dispatch({ type: 'REMOVE_ITEM', payload: cartItemKey });
}

export function updateQuantity(dispatch: React.Dispatch<CartAction>, cartItem: CartProduct, quantity: number) {
  const cartItemKey = getCartItemKey(cartItem.id, cartItem.selectedVariant);
  dispatch({ type: 'UPDATE_QUANTITY', payload: { cartItemKey, quantity } });
}

export function clearCart(dispatch: React.Dispatch<CartAction>) {
  dispatch({ type: 'CLEAR_CART' });
}