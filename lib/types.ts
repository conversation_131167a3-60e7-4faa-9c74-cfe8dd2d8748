import { Product, ProductVariantItem } from '@/data/models/product.model';

export interface CartProduct extends Omit<Product, 'variants'> {
  quantity: number;
  selectedVariant?: ProductVariantItem;
  selectedVariantOptions?: Record<string, string>;
}

export type CartAction =
  | { type: 'ADD_ITEM'; payload: Omit<CartProduct, 'quantity'> }
  | { type: 'REMOVE_ITEM'; payload: string } // cart item key (product_id or product_id_variant_id)
  | { type: 'UPDATE_QUANTITY'; payload: { cartItemKey: string; quantity: number } }
  | { type: 'CLEAR_CART' };

export interface CartState {
  items: CartProduct[];
}