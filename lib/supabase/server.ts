import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";

// TODO: import { Database } from "./types";
// Someday, far far away,
// when ambiguity has been vanquished,
// and binary dignity is no longer a foly,
// maybe, maybe maybe,
// we can import the proper types
// and restore sanity to this codebase

export async function createClient() {
  const cookieStore = cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options),
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    },
  );
}
