import { clsx, type ClassValue } from "clsx";
import { format } from "date-fns/format";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(dateTime: Date | null | undefined): string | null {
  try {
    if (!dateTime) return null;
    return format(dateTime, "MMMM d, yyyy");
  } catch (e) {
    return null;
  }
}

export function isDevEnvironment() {
  return window.location.href.includes("localhost");
}

/**
 * Generates a 12-character alphanumeric code for consolidated parcels.
 * Uses uppercase letters (A-Z) and numbers (0-9) for better readability.
 * Includes timestamp-based elements to ensure uniqueness.
 * Format: XXXX-XXXX-XXXX where X is alphanumeric
 * @returns A 12-character string with hyphens (total 14 characters)
 */
export function generateConsolidateParcelCode(): string {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  const timestamp = Date.now().toString(36).toUpperCase();
  const result: string[] = [];

  // Use first 4 characters from timestamp for first segment
  result.push(timestamp.slice(0, 4));

  // Generate 4 random characters for second segment
  for (let i = 0; i < 4; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result.push(characters[randomIndex]);
  }

  // Generate 4 random characters for third segment
  for (let i = 0; i < 4; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result.push(characters[randomIndex]);
  }

  // Join segments with hyphens
  return `${result.slice(0, 4).join("")}-${result.slice(4, 8).join("")}-${result.slice(8, 12).join("")}`;
}


export function convertToPascalCasing(value: string): string {
  return value
    .split("_") // Split the string by underscores
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // Capitalize the first letter of each word
    .join(" "); // Join the words back together with spaces
};