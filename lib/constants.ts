const countryList = [
  { label: "Botswana", value: "Botswana" },
  { label: "Egypt", value: "Egypt" },
  { label: "Ghana", value: "Ghana" },
  { label: "India", value: "India" },
  { label: "Indonesia", value: "Indonesia" },
  { label: "Kenya", value: "Kenya" },
  { label: "Kuwait", value: "Kuwait" },
  { label: "Liberia", value: "Liberia" },
  { label: "Malawi", value: "Malawi" },
  { label: "Malaysia", value: "Malaysia" },
  { label: "Namibia", value: "Namibia" },
  { label: "Nigeria", value: "Nigeria" },
  { label: "Pakistan", value: "Pakistan" },
  { label: "Philippines", value: "Philippines" },
  { label: "Rwanda", value: "Rwanda" },
  { label: "Saudi Arabia", value: "Saudi Arabia" },
  { label: "Sierra Leone", value: "Sierra Leone" },
  { label: "Singapore", value: "Singapore" },
  { label: "South Africa", value: "South Africa" },
  { label: "South Sudan", value: "South Sudan" },
  { label: "Tanzania", value: "Tanzania" },
  { label: "Thailand", value: "Thailand" },
  { label: "UAE", value: "UAE" },
  { label: "United Kingdom", value: "United Kingdom" },
  { label: "Zambia", value: "Zambia" },
  { label: "Zimbabwe", value: "Zimbabwe" },
] as const;
//For shipping rates.
const shipping_rate_countries = {
  DoorToDoor: ["Ghana", "Nigeria"],
  RestOfAfrica: [
    "Botswana",
    "Egypt",
    "Kenya",
    "Liberia",
    "Malawi",
    "Namibia",
    "Rwanda",
    "Sierra Leone",
    "South Africa",
    "South Sudan",
    "Tanzania",
    "Zambia",
    "Zimbabwe",
  ],
} as const;

/**
 * SHIPPING TIERS
 *
 * New shipping model: shipping cost is computed from item volume (L×W×H in cm,
 * yielding cm³) and weight (kg). The UI and checkout should call the helper
 * functions below to pick a tier and price in GBP.
 *
 * Tiers (as provided):
 * - Extra Small: up to 10,000 cm³, max 2 kg — From £30.99
 * - Small:       up to 25,000 cm³, max 5 kg — From £50.99
 * - Medium:      up to 50,000 cm³, max 10 kg — From £85.99
 * - Large:       up to 75,000 cm³, max 15 kg — From £119.99
 * - Extra Large: up to 100,000 cm³, max 20 kg — From £165.99
 *
 * Note: these are "from" prices (base rate per parcel). Business rules such as
 * additional handling, insurance, or combined-parcel packing may be applied
 * at a higher level.
 */
export type ShippingTier = {
  id: "mini" | "small" | "medium" | "large" | "xLarge" | "xxLarge";
  name: string;
  maxVolumeCm3: number; // inclusive upper bound for this tier in cubic centimeters
  maxWeightKg: number; // inclusive upper bound in kilograms
  priceGbp: number; // base price in GBP for this tier
  ghPriceGbp: number; // base price in GBP for this tier in Ghana
  ngPriceGbp: number; // base price in GBP for this tier in Nigeria
};

export const SHIPPING_TIERS: ShippingTier[] = [
  {
    id: "mini",
    name: "Mini",
    maxVolumeCm3: 10_000,
    maxWeightKg: 1,
    priceGbp: 9,
    ghPriceGbp: 9,
    ngPriceGbp: 9,
  },
  {
    id: "small",
    name: "Small",
    maxVolumeCm3: 10_000,
    maxWeightKg: 2,
    priceGbp: 15,
    ghPriceGbp: 15,
    ngPriceGbp: 15,
  },
  {
    id: "medium",
    name: "Medium",
    maxVolumeCm3: 25_000,
    maxWeightKg: 5,
    priceGbp: 30,
    ghPriceGbp: 30,
    ngPriceGbp: 30,
  },
  {
    id: "large",
    name: "Large",
    maxVolumeCm3: 50_000,
    maxWeightKg: 10,
    priceGbp: 50,
    ghPriceGbp: 50,
    ngPriceGbp: 50,
  },
  {
    id: "xLarge",
    name: "XL",
    maxVolumeCm3: 75_000,
    maxWeightKg: 15,
    priceGbp: 100,
    ghPriceGbp: 100,
    ngPriceGbp: 100,
  },
  {
    id: "xxLarge",
    name: "XXL",
    maxVolumeCm3: 100_000,
    maxWeightKg: 20,
    priceGbp: 150,
    ghPriceGbp: 150,
    ngPriceGbp: 150,
  },
];

/**
 * Helpers (exported) for computing shipping tier / price from dimensions & weight.
 *
 * - `findShippingTier` returns the first tier that can contain the provided
 *   volume and weight (tiers are ordered from smallest to largest).
 * - `computeShippingBaseGbp` returns the base GBP price for the tier. If the
 *   item exceeds the largest tier, it returns the extra_large price as a
 *   fallback — upper-level logic can decide to reject or split shipments.
 *
 * These helpers are intentionally pure and synchronous so they can be used
 * in render-time calculations or in server-side code.
 */
export function findShippingTier(
  volumeCm3: number,
  weightKg: number,
): ShippingTier | null {
  if (typeof volumeCm3 !== "number" || typeof weightKg !== "number")
    return null;
  for (const tier of SHIPPING_TIERS) {
    if (volumeCm3 <= tier.maxVolumeCm3 && weightKg <= tier.maxWeightKg) {
      return tier;
    }
  }
  return null;
}

/**
 * Compute the base shipping cost in GBP for a single parcel with the given
 * volume (cm³) and weight (kg). Returns a number (GBP).
 *
 * If no tier fits (oversize/heavy), this returns the largest tier price as a
 * conservative fallback. Callers may choose to treat that as an error instead.
 */
export function computeShippingBaseGbp(
  volumeCm3: number,
  weightKg: number,
): number {
  const tier = findShippingTier(volumeCm3, weightKg);
  if (tier) return tier.priceGbp;
  // fallback: return price of the largest tier
  return SHIPPING_TIERS[SHIPPING_TIERS.length - 1].priceGbp;
}

/**
 * Helper to compute total shipping cost for `quantity` items where each item
 * has `volumeCm3` and `weightKg`. By default this multiplies base price * quantity.
 * Higher-level logic may choose to pack multiple items into a single parcel and
 * re-run the tier lookup on aggregated volume/weight instead.
 */
export function computeShippingForQuantityBaseGbp(
  volumeCm3: number,
  weightKg: number,
  quantity: number,
): number {
  if (!quantity || quantity <= 0) return 0;
  const singlePrice = computeShippingBaseGbp(volumeCm3, weightKg);
  return singlePrice * quantity;
}

const country_currencies = {
  BWP: "Botswana",
  EGP: "Egypt",
  GHS: "Ghana",
  INR: "India",
  IDR: "Indonesia",
  KES: "Kenya",
  KWD: "Kuwait",
  LRD: "Liberia",
  MWK: "Malawi",
  MYR: "Malaysia",
  NAD: "Namibia",
  NGN: "Nigeria",
  PKR: "Pakistan",
  PHP: "Philippines",
  RWF: "Rwanda",
  SAR: "Saudi Arabia",
  SLL: "Sierra Leone",
  SGD: "Singapore",
  ZAR: "South Africa",
  SSP: "South Sudan",
  TZS: "Tanzania",
  THB: "Thailand",
  AED: "UAE",
  GBP: "United Kingdom",
  ZMW: "Zambia",
  ZWL: "Zimbabwe",
} as const;

/**
 * currency_country_code
 * Map currency code -> ISO 3166-1 alpha-2 country code.
 * This allows deriving emoji flags for currencies consistently.
 *
 * NOTE: A currency does not always map 1:1 to a single country, but for display
 * purposes this mapping chooses a representative country for the currency code.
 */
const currency_country_code: Record<string, string> = {
  GBP: "GB",
  GHS: "GH",
  NGN: "NG",
  USD: "US",
  ZAR: "ZA",
  EGP: "EG",
  KES: "KE",
  INR: "IN",
  SGD: "SG",
  AED: "AE",
  SAR: "SA",
  PHP: "PH",
  PKR: "PK",
  MYR: "MY",
  LRD: "LR",
  BWP: "BW",
  NAD: "NA",
  RWF: "RW",
  MWK: "MW",
  ZMW: "ZM",
  ZWL: "ZW",
  IDR: "ID",
  THB: "TH",
  TZS: "TZ",
  SLL: "SL",
  SSP: "SS",
  // Add additional mappings as needed to cover more currency codes present in country_currencies
} as const;

const CURRENCY_SYMBOLS: Record<string, string> = {
  AED: "د.إ",
  AFN: "؋",
  ALL: "L",
  AMD: "֏",
  ANG: "ƒ",
  AOA: "Kz",
  ARS: "$",
  AUD: "$",
  AWG: "ƒ",
  AZN: "₼",
  BAM: "KM",
  BBD: "$",
  BDT: "৳",
  BGN: "лв",
  BHD: ".د.ب",
  BIF: "FBu",
  BMD: "$",
  BND: "$",
  BOB: "$b",
  BOV: "BOV",
  BRL: "R$",
  BSD: "$",
  BTC: "₿",
  BTN: "Nu.",
  BWP: "P",
  BYN: "Br",
  BYR: "Br",
  BZD: "BZ$",
  CAD: "$",
  CDF: "FC",
  CHE: "CHE",
  CHF: "CHF",
  CHW: "CHW",
  CLF: "CLF",
  CLP: "$",
  CNH: "¥",
  CNY: "¥",
  COP: "$",
  COU: "COU",
  CRC: "₡",
  CUC: "$",
  CUP: "₱",
  CVE: "$",
  CZK: "Kč",
  DJF: "Fdj",
  DKK: "kr",
  DOP: "RD$",
  DZD: "دج",
  EEK: "kr",
  EGP: "£",
  ERN: "Nfk",
  ETB: "Br",
  ETH: "Ξ",
  EUR: "€",
  FJD: "$",
  FKP: "£",
  GBP: "£",
  GEL: "₾",
  GGP: "£",
  GHC: "GH₵",
  GHS: "₵",
  GIP: "£",
  GMD: "D",
  GNF: "FG",
  GTQ: "Q",
  GYD: "$",
  HKD: "$",
  HNL: "L",
  HRK: "kn",
  HTG: "G",
  HUF: "Ft",
  IDR: "Rp",
  ILS: "₪",
  IMP: "£",
  INR: "₹",
  IQD: "ع.د",
  IRR: "﷼",
  ISK: "kr",
  JEP: "£",
  JMD: "J$",
  JOD: "JD",
  JPY: "¥",
  KES: "KSh",
  KGS: "лв",
  KHR: "៛",
  KMF: "CF",
  KPW: "₩",
  KRW: "₩",
  KWD: "KD",
  KYD: "$",
  KZT: "₸",
  LAK: "₭",
  LBP: "£",
  LKR: "₨",
  LRD: "$",
  LSL: "M",
  LTC: "Ł",
  LTL: "Lt",
  LVL: "Ls",
  LYD: "LD",
  MAD: "MAD",
  MDL: "lei",
  MGA: "Ar",
  MKD: "ден",
  MMK: "K",
  MNT: "₮",
  MOP: "MOP$",
  MRO: "UM",
  MRU: "UM",
  MUR: "₨",
  MVR: "Rf",
  MWK: "MK",
  MXN: "$",
  MXV: "MXV",
  MYR: "RM",
  MZN: "MT",
  NAD: "$",
  NGN: "₦",
  NIO: "C$",
  NOK: "kr",
  NPR: "₨",
  NZD: "$",
  OMR: "﷼",
  PAB: "B/.",
  PEN: "S/.",
  PGK: "K",
  PHP: "₱",
  PKR: "₨",
  PLN: "zł",
  PYG: "Gs",
  QAR: "﷼",
  RMB: "￥",
  RON: "lei",
  RSD: "Дин.",
  RUB: "₽",
  RWF: "R₣",
  SAR: "﷼",
  SBD: "$",
  SCR: "₨",
  SDG: "ج.س.",
  SEK: "kr",
  SGD: "S$",
  SHP: "£",
  SLL: "Le",
  SOS: "S",
  SRD: "$",
  SSP: "£",
  STD: "Db",
  STN: "Db",
  SVC: "$",
  SYP: "£",
  SZL: "E",
  THB: "฿",
  TJS: "SM",
  TMT: "T",
  TND: "د.ت",
  TOP: "T$",
  TRL: "₤",
  TRY: "₺",
  TTD: "TT$",
  TVD: "$",
  TWD: "NT$",
  TZS: "TSh",
  UAH: "₴",
  UGX: "USh",
  USD: "$",
  UYI: "UYI",
  UYU: "$U",
  UYW: "UYW",
  UZS: "лв",
  VEF: "Bs",
  VES: "Bs.S",
  VND: "₫",
  VUV: "VT",
  WST: "WS$",
  XAF: "FCFA",
  XBT: "Ƀ",
  XCD: "$",
  XOF: "CFA",
  XPF: "₣",
  XSU: "Sucre",
  XUA: "XUA",
  YER: "﷼",
  ZAR: "R",
  ZMW: "ZK",
  ZWD: "Z$",
  ZWL: "$",
} as const;

/**
 * countryCodeToFlag
 * Convert an ISO 3166-1 alpha-2 country code (e.g. "GB", "US", "GH") into
 * the corresponding regional indicator symbol emoji flag.
 *
 * Returns a globe emoji "🌐" as fallback if conversion is not possible.
 */
function countryCodeToFlag(countryCode?: string | null): string {
  if (!countryCode || typeof countryCode !== "string") return "🌐";
  const code = countryCode.toUpperCase();
  if (code.length !== 2) return "🌐";
  const A = 0x1f1e6; // regional indicator symbol letter A
  const first = A + (code.charCodeAt(0) - 65);
  const second = A + (code.charCodeAt(1) - 65);
  try {
    return String.fromCodePoint(first, second);
  } catch (e) {
    return "🌐";
  }
}

/**
 * currencyCodeToFlag
 * Convenience: take a currency code (e.g. "GHS") and return a flag emoji by
 * mapping to a representative country code then converting to emoji.
 */
function currencyCodeToFlag(currencyCode?: string | null): string {
  if (!currencyCode || typeof currencyCode !== "string") return "🌐";
  const code = currencyCode.toUpperCase();
  const country = (currency_country_code as Record<string, string>)[code];
  if (country) return countryCodeToFlag(country);
  return "🌐";
}

const categories = [
  {
    id: "45fa1451-ebc1-4a59-93c0-828c789a9ca4",
    name: "Baby & Kids",
    parent_id: null,
  },
  {
    id: "59829231-2cd4-4bba-94f3-fc1b08722555",
    name: "Baby Essentials",
    parent_id: "45fa1451-ebc1-4a59-93c0-828c789a9ca4",
  },
  {
    id: "94d6ba3a-cc75-4d24-b19f-d0ffedb41a63",
    name: "Baby Clothing",
    parent_id: "45fa1451-ebc1-4a59-93c0-828c789a9ca4",
  },
  {
    id: "9c0c5643-7827-46f8-98e9-6f9e7fe07b59",
    name: "Bath & Health",
    parent_id: "45fa1451-ebc1-4a59-93c0-828c789a9ca4",
  },
  {
    id: "9fd31912-ba2d-42bb-ab3f-5e4294d24277",
    name: "Diapering & Potty Training",
    parent_id: "45fa1451-ebc1-4a59-93c0-828c789a9ca4",
  },
  {
    id: "4551d2a8-f7a8-4348-a995-ed497b556f7c",
    name: "Feeding & Nursing",
    parent_id: "45fa1451-ebc1-4a59-93c0-828c789a9ca4",
  },
  {
    id: "0ceedb17-c7ba-47e9-991d-d9ca4b786346",
    name: "Nurseries & Furnitures",
    parent_id: "45fa1451-ebc1-4a59-93c0-828c789a9ca4",
  },
  {
    id: "6e68f257-89fc-41f0-bb10-681d52dacea8",
    name: "Speciality & Lifestyle",
    parent_id: "45fa1451-ebc1-4a59-93c0-828c789a9ca4",
  },
  {
    id: "0b7fea88-9fdf-4dca-9213-48d99307f617",
    name: "Toys & Learning",
    parent_id: "45fa1451-ebc1-4a59-93c0-828c789a9ca4",
  },

  {
    id: "6242369a-7f1f-49ba-9bc7-aae34088ec25",
    name: "Electronics",
    parent_id: null,
  },
  {
    id: "6d22682a-18bc-454f-87f4-eba76a23b65d",
    name: "Audio",
    parent_id: "6242369a-7f1f-49ba-9bc7-aae34088ec25",
  },
  {
    id: "778884b9-2528-456c-9572-68b516f6783c",
    name: "Chargers & Stations",
    parent_id: "6242369a-7f1f-49ba-9bc7-aae34088ec25",
  },
  {
    id: "7788ef86-4098-482f-94d9-7730a8df4b02",
    name: "Game Consoles",
    parent_id: "6242369a-7f1f-49ba-9bc7-aae34088ec25",
  },
  {
    id: "6e7426d4-9be1-4e04-a118-ad0342271924",
    name: "Laptops",
    parent_id: "6242369a-7f1f-49ba-9bc7-aae34088ec25",
  },
  {
    id: "f112482e-6813-470b-9258-ea6f139dd743",
    name: "Monitors",
    parent_id: "6242369a-7f1f-49ba-9bc7-aae34088ec25",
  },
  {
    id: "d63e0019-be01-444c-a90b-0b49ea608280",
    name: "Phones",
    parent_id: "6242369a-7f1f-49ba-9bc7-aae34088ec25",
  },
  {
    id: "7c89fcb2-e141-4171-b3b0-f194c82e98a4",
    name: "Peripherals",
    parent_id: "6242369a-7f1f-49ba-9bc7-aae34088ec25",
  },
  {
    id: "616266ae-280e-4825-acf9-7e6fc36bb998",
    name: "PowerBanks",
    parent_id: "6242369a-7f1f-49ba-9bc7-aae34088ec25",
  },
  {
    id: "24baace4-d1e5-4646-a245-b5845d9804a7",
    name: "PC Components",
    parent_id: "6242369a-7f1f-49ba-9bc7-aae34088ec25",
  },
  {
    id: "c5256798-ea1e-4c58-b0dd-b2bee60c11ef",
    name: "Smartwatches",
    parent_id: "6242369a-7f1f-49ba-9bc7-aae34088ec25",
  },
  {
    id: "2b786cc9-9939-4930-a976-89aab9cbb3eb",
    name: "Tablets",
    parent_id: "6242369a-7f1f-49ba-9bc7-aae34088ec25",
  },

  {
    id: "bdc66b83-ba65-4a79-9c07-41f3cb19f917",
    name: "Fashion & Apparel",
    parent_id: null,
  },
  {
    id: "56024a06-9de1-4975-8b1e-39cfcc334e68",
    name: "Boy’s Clothing",
    parent_id: "bdc66b83-ba65-4a79-9c07-41f3cb19f917",
  },
  {
    id: "22d3d39a-4c69-4708-b077-18be22b54d2a",
    name: "Clothing Accessories",
    parent_id: "bdc66b83-ba65-4a79-9c07-41f3cb19f917",
  },
  {
    id: "ca84b87d-f857-4e40-9a3d-fc69604412ec",
    name: "Girls Clothing",
    parent_id: "bdc66b83-ba65-4a79-9c07-41f3cb19f917",
  },
  {
    id: "06264c53-6741-44e6-9b96-13685445364c",
    name: "Men’s Clothing",
    parent_id: "bdc66b83-ba65-4a79-9c07-41f3cb19f917",
  },
  {
    id: "2323830e-6f5e-4309-9dee-c5dbdda86b6e",
    name: "Women’s Clothing",
    parent_id: "bdc66b83-ba65-4a79-9c07-41f3cb19f917",
  },
  {
    id: "3ac87838-3584-4a24-b617-f33137095e49",
    name: "Watches",
    parent_id: "bdc66b83-ba65-4a79-9c07-41f3cb19f917",
  },

  {
    id: "a57ab1fa-9b5e-4062-a1ee-0a77ca3fd421",
    name: "Groceries & Gourmet Foods",
    parent_id: null,
  },
  {
    id: "473d2175-3779-4e40-8365-9f94bc64cdbf",
    name: "Beverages",
    parent_id: "a57ab1fa-9b5e-4062-a1ee-0a77ca3fd421",
  },
  {
    id: "bac5d5ca-2321-4684-adbf-5bbf70b05e84",
    name: "Gourmet & Special Foods",
    parent_id: "a57ab1fa-9b5e-4062-a1ee-0a77ca3fd421",
  },
  {
    id: "4e7f818d-bb68-4617-ba4e-b83aa5517c51",
    name: "Pantry Staples",
    parent_id: "a57ab1fa-9b5e-4062-a1ee-0a77ca3fd421",
  },
  {
    id: "4e9f3d3e-0571-4389-9715-4766f93ecde5",
    name: "Snacks & Confectionery",
    parent_id: "a57ab1fa-9b5e-4062-a1ee-0a77ca3fd421",
  },

  {
    id: "63aec868-692c-40ce-8add-2b34726a3f50",
    name: "Health & Beauty",
    parent_id: null,
  },
  {
    id: "cff051ac-bb00-4ca5-8aec-adc16545f523",
    name: "Beauty and cosmetics",
    parent_id: "63aec868-692c-40ce-8add-2b34726a3f50",
  },
  {
    id: "8f7556f4-4264-47aa-a1d8-3a48b9afb84a",
    name: "Hair Care",
    parent_id: "63aec868-692c-40ce-8add-2b34726a3f50",
  },
  {
    id: "ae689780-bdd9-46d0-a303-abdf6fe0208f",
    name: "Health and Wellness",
    parent_id: "63aec868-692c-40ce-8add-2b34726a3f50",
  },
  {
    id: "8f409c91-ab4f-4cd2-b4fc-bf54245f6e0f",
    name: "Nicotine Pouches",
    parent_id: "63aec868-692c-40ce-8add-2b34726a3f50",
  },
  {
    id: "a1e6cc03-340c-4d34-9eeb-56f17f99023f",
    name: "Personal care and Hygiene",
    parent_id: "63aec868-692c-40ce-8add-2b34726a3f50",
  },
  {
    id: "d44aebcb-8d20-4cbb-932b-732e88ff4b84",
    name: "Speciality and Lifestyle",
    parent_id: "63aec868-692c-40ce-8add-2b34726a3f50",
  },
  {
    id: "647e3516-a706-4ac6-8582-3184121728b6",
    name: "Skincare",
    parent_id: "63aec868-692c-40ce-8add-2b34726a3f50",
  },

  {
    id: "9ea4f81d-1d07-4ecf-9c2a-ca1c6b854b81",
    name: "Home & Kitchen",
    parent_id: null,
  },
  {
    id: "84da656d-c380-4041-9e7f-a118b4d3e771",
    name: "Bedding & Bath",
    parent_id: "9ea4f81d-1d07-4ecf-9c2a-ca1c6b854b81",
  },
  {
    id: "0148c863-9f28-47b0-8e09-4fb7317eefbd",
    name: "Furniture",
    parent_id: "9ea4f81d-1d07-4ecf-9c2a-ca1c6b854b81",
  },
  {
    id: "fb283837-78a1-424c-9d0a-f7a847d4cd08",
    name: "Home Decor",
    parent_id: "9ea4f81d-1d07-4ecf-9c2a-ca1c6b854b81",
  },
  {
    id: "6676d403-76a2-4669-b33f-637e9c1ef57f",
    name: "Household Essentials",
    parent_id: "9ea4f81d-1d07-4ecf-9c2a-ca1c6b854b81",
  },
  {
    id: "77c3ab03-72bd-4f41-a8e2-fceb7a08c5a1",
    name: "Kitchen & Dinning",
    parent_id: "9ea4f81d-1d07-4ecf-9c2a-ca1c6b854b81",
  },
  {
    id: "e758b5f9-b30c-4d01-a2af-fddc05d35a1c",
    name: "Outdoor and Garden",
    parent_id: "9ea4f81d-1d07-4ecf-9c2a-ca1c6b854b81",
  },
];

export {
  countryList,
  country_currencies,
  categories,
  shipping_rate_countries,
  currency_country_code,
  countryCodeToFlag,
  currencyCodeToFlag,
  CURRENCY_SYMBOLS,
};
