import { createClient } from "@/lib/supabase/server";
import { NextResponse } from "next/server";

export interface AuthUser {
  id: string;
  email: string;
}

export interface AuthResult {
  user: AuthUser | null;
  error: string | null;
}

export interface AdminAuthResult {
  user: AuthUser | null;
  admin: any | null;
  error: string | null;
}

export interface AccessResult {
  canAccess: boolean;
  error: string | null;
  isAdmin: boolean;
}

/**
 * Get the currently authenticated user from Supabase Auth
 */
export async function getAuthenticatedUser(): Promise<AuthResult> {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return { user: null, error: "No authenticated user found" };
    }

    return {
      user: {
        id: user.id,
        email: user.email || "",
      },
      error: null,
    };
  } catch (err) {
    return { user: null, error: "Authentication error" };
  }
}

/**
 * Get authenticated user and verify admin status
 */
export async function getAuthenticatedAdmin(): Promise<AdminAuthResult> {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { user: null, admin: null, error: "No authenticated user found" };
    }

    // Check if user is admin - mp_admins.id is UUID field that matches auth.users.id
    const { data: admin, error: adminError } = await supabase
      .from("mp_admins")
      .select("*")
      .eq("id", user.id) // mp_admins.id (UUID) matches auth.users.id (UUID)
      .single();

    if (adminError || !admin) {
      return {
        user: {
          id: user.id,
          email: user.email || "",
        },
        admin: null,
        error: "User is not an admin",
      };
    }

    return {
      user: {
        id: user.id,
        email: user.email || "",
      },
      admin,
      error: null,
    };
  } catch (err) {
    return { user: null, admin: null, error: "Authentication error" };
  }
}

/**
 * Check if the authenticated user can access data for a specific user (by email)
 * Users can access their own data, admins can access any user's data
 */
export async function canAccessUserData(
  targetEmail: string,
): Promise<AccessResult> {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        canAccess: false,
        error: "Authentication required",
        isAdmin: false,
      };
    }

    // Check if user is admin - mp_admins.id (UUID) matches auth.users.id (UUID)
    const { data: admin } = await supabase
      .from("mp_admins")
      .select("id")
      .eq("id", user.id)
      .single();

    if (admin) {
      return { canAccess: true, error: null, isAdmin: true };
    }

    // Check if user is accessing their own data
    if (user.email === targetEmail) {
      return { canAccess: true, error: null, isAdmin: false };
    }

    return { canAccess: false, error: "Access denied", isAdmin: false };
  } catch (err) {
    return { canAccess: false, error: "Authentication error", isAdmin: false };
  }
}

/**
 * Check if the authenticated user can access data for a specific user (by UUID)
 * Users can access their own data, admins can access any user's data
 */
export async function canAccessUserDataById(
  targetUserId: string,
): Promise<AccessResult> {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        canAccess: false,
        error: "Authentication required",
        isAdmin: false,
      };
    }

    // Check if user is admin - mp_admins.id (UUID) matches auth.users.id (UUID)
    const { data: admin } = await supabase
      .from("mp_admins")
      .select("id")
      .eq("id", user.id)
      .single();

    if (admin) {
      return { canAccess: true, error: null, isAdmin: true };
    }

    // For regular users, check if they're accessing their own data
    // Need to get the user's record from mp_users using their UUID
    const { data: userData } = await supabase
      .from("mp_users")
      .select("id, uuid")
      .eq("uuid", user.id)
      .single();

    if (
      userData &&
      (userData.uuid === targetUserId ||
        userData.id.toString() === targetUserId)
    ) {
      return { canAccess: true, error: null, isAdmin: false };
    }

    return { canAccess: false, error: "Access denied", isAdmin: false };
  } catch (err) {
    return { canAccess: false, error: "Authentication error", isAdmin: false };
  }
}

/**
 * Check if the authenticated user can access a specific parcel
 */
export async function canAccessParcel(parcelId: string): Promise<AccessResult> {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        canAccess: false,
        error: "Authentication required",
        isAdmin: false,
      };
    }

    // Check if user is admin - mp_admins.id (UUID) matches auth.users.id (UUID)
    const { data: admin } = await supabase
      .from("mp_admins")
      .select("id")
      .eq("id", user.id)
      .single();

    if (admin) {
      return { canAccess: true, error: null, isAdmin: true };
    }

    // Check if the parcel belongs to the authenticated user
    const { data: parcel } = await supabase
      .from("users_parcels_details")
      .select("user_uuid, user_id")
      .eq("id", parcelId)
      .single();

    if (parcel && parcel.user_uuid === user.id) {
      return { canAccess: true, error: null, isAdmin: false };
    }

    return { canAccess: false, error: "Access denied", isAdmin: false };
  } catch (err) {
    return { canAccess: false, error: "Authentication error", isAdmin: false };
  }
}

/**
 * Check if the authenticated user can access a specific order
 */
export async function canAccessOrder(orderId: string): Promise<AccessResult> {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        canAccess: false,
        error: "Authentication required",
        isAdmin: false,
      };
    }

    // Check if user is admin - mp_admins.id (UUID) matches auth.users.id (UUID)
    const { data: admin } = await supabase
      .from("mp_admins")
      .select("id")
      .eq("id", user.id)
      .single();

    if (admin) {
      return { canAccess: true, error: null, isAdmin: true };
    }

    // Check if the order belongs to the authenticated user
    // First get the user's data to find their user_id (integer)
    const { data: userData } = await supabase
      .from("mp_users")
      .select("id")
      .eq("uuid", user.id)
      .single();

    if (!userData) {
      return { canAccess: false, error: "User not found", isAdmin: false };
    }

    const { data: order } = await supabase
      .from("mp_ecommerce_orders")
      .select("user_id")
      .eq("id", orderId)
      .single();

    if (order && order.user_id === userData.id) {
      return { canAccess: true, error: null, isAdmin: false };
    }

    return { canAccess: false, error: "Access denied", isAdmin: false };
  } catch (err) {
    return { canAccess: false, error: "Authentication error", isAdmin: false };
  }
}

/**
 * Require admin authentication (throws if not admin)
 */
export async function requireAdmin(): Promise<{ user: AuthUser; admin: any }> {
  const { user, admin, error } = await getAuthenticatedAdmin();
  if (error || !user || !admin) {
    throw new Error(error || "Admin authentication required");
  }
  return { user, admin };
}

/**
 * Require user authentication (throws if not authenticated)
 */
export async function requireAuth(): Promise<AuthUser> {
  const { user, error } = await getAuthenticatedUser();
  if (error || !user) {
    throw new Error(error || "Authentication required");
  }
  return user;
}

/**
 * Create a standardized unauthorized response
 */
export function createUnauthorizedResponse(message: string = "Unauthorized") {
  return NextResponse.json({ error: message }, { status: 401 });
}

/**
 * Create a standardized forbidden response
 */
export function createForbiddenResponse(message: string = "Access denied") {
  return NextResponse.json({ error: message }, { status: 403 });
}

/**
 * Check if the authenticated user can access user data by email
 */
export async function canAccessUserDataByEmail(
  targetEmail: string,
): Promise<AccessResult> {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        canAccess: false,
        error: "Authentication required",
        isAdmin: false,
      };
    }

    // Check if user is admin - mp_admins.id (UUID) matches auth.users.id (UUID)
    const { data: admin } = await supabase
      .from("mp_admins")
      .select("id")
      .eq("id", user.id)
      .single();

    if (admin) {
      return { canAccess: true, error: null, isAdmin: true };
    }

    // Check if user is accessing their own data
    if (user.email === targetEmail) {
      return { canAccess: true, error: null, isAdmin: false };
    }

    return { canAccess: false, error: "Access denied", isAdmin: false };
  } catch (err) {
    return { canAccess: false, error: "Authentication error", isAdmin: false };
  }
}

/**
 * Check if the authenticated user can access parcel data by tracking ID
 */
export async function canAccessParcelByTrackingId(
  trackingId: string,
): Promise<AccessResult> {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        canAccess: false,
        error: "Authentication required",
        isAdmin: false,
      };
    }

    // Check if user is admin - mp_admins.id (UUID) matches auth.users.id (UUID)
    const { data: admin } = await supabase
      .from("mp_admins")
      .select("id")
      .eq("id", user.id)
      .single();

    if (admin) {
      return { canAccess: true, error: null, isAdmin: true };
    }

    // Check if the parcel belongs to the authenticated user
    const { data: parcel } = await supabase
      .from("users_parcels_details")
      .select("user_uuid")
      .eq("tracking_id", trackingId)
      .single();

    if (parcel && parcel.user_uuid === user.id) {
      return { canAccess: true, error: null, isAdmin: false };
    }

    return { canAccess: false, error: "Access denied", isAdmin: false };
  } catch (err) {
    return { canAccess: false, error: "Authentication error", isAdmin: false };
  }
}

/**
 * Validate that a user can only create/modify data for themselves
 */
export async function validateUserOwnership(
  providedUserId: string,
): Promise<AccessResult> {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error || !user) {
      return {
        canAccess: false,
        error: "Authentication required",
        isAdmin: false,
      };
    }

    // Check if user is admin (admins can modify any user's data)
    // mp_admins.id (UUID) matches auth.users.id (UUID)
    const supabase = await createClient();
    const { data: admin } = await supabase
      .from("mp_admins")
      .select("id")
      .eq("id", user.id)
      .single();

    if (admin) {
      return { canAccess: true, error: null, isAdmin: true };
    }

    // For regular users, they can only modify their own data
    if (user.id !== providedUserId) {
      return {
        canAccess: false,
        error: "Cannot modify data for other users",
        isAdmin: false,
      };
    }

    return { canAccess: true, error: null, isAdmin: false };
  } catch (err) {
    return { canAccess: false, error: "Authentication error", isAdmin: false };
  }
}
