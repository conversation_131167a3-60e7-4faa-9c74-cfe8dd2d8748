// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@12.0.0?target=deno";
import { corsHeaders } from "../_shared/config.ts";

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
});

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  if (req.method === "POST") {
    try {
      const { code, amount } = await req.json();

      if (!code || amount === undefined || amount === null) {
        return new Response(
          JSON.stringify({ success: false, error: "Missing code or amount" }),
          { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } }
        );
      }

      try {
        let coupon: Stripe.Coupon;
        
        // First try promotion codes
        const promotionCodes = await stripe.promotionCodes.list({
          code: code,
          active: true,
          limit: 1
        });

        if (promotionCodes.data.length > 0) {
          coupon = promotionCodes.data[0].coupon;
        } else {
          // If no promotion code found, try direct coupon
          try {
            coupon = await stripe.coupons.retrieve(code);
          } catch (_e) {
            return new Response(
              JSON.stringify({ 
                success: false, 
                error: "Invalid discount code. Please check and try again." 
              }),
              { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } }
            );
          }
        }

        // Validate the coupon
        if (!coupon.valid) {
          return new Response(
            JSON.stringify({ success: false, error: "Coupon is not valid" }),
            { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } }
          );
        }

        // Check expiration
        if (coupon.redeem_by && new Date(coupon.redeem_by * 1000) < new Date()) {
          return new Response(
            JSON.stringify({ success: false, error: "Discount code has expired" }),
            { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } }
          );
        }

        // Calculate discount
        let discountAmount = 0;
        if (coupon.amount_off) {
          discountAmount = Math.min(coupon.amount_off / 100, amount);
        } else if (coupon.percent_off) {
          discountAmount = Math.round((amount * coupon.percent_off) / 100 * 100) / 100;
        }

        return new Response(
          JSON.stringify({
            success: true,
            discountCode: code,
            discountAmount: discountAmount,
            finalAmount: amount - discountAmount
          }),
          { status: 200, headers: { ...corsHeaders, "Content-Type": "application/json" } }
        );

      } catch (stripeError) {
        console.error('Stripe API Error:', stripeError);
        return new Response(
          JSON.stringify({ 
            success: false, 
            error: stripeError instanceof Error ? stripeError.message : "Invalid discount code" 
          }),
          { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } }
        );
      }

    } catch (error) {
      console.error('Discount validation error:', error);
      return new Response(
        JSON.stringify({ success: false, error: "Error processing discount code" }),
        { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }
  } else {
    return new Response(
      JSON.stringify({ error: `Method ${req.method} not allowed` }),
      { status: 405, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }
});