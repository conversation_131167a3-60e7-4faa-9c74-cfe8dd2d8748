// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

import { corsHeaders } from "../_shared/config.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const AUDIENCE_ID = Deno.env.get("RESEND_AUDIENCE_ID");

serve(async (req: Request) => {
  // Handle CORS preflight request
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (req.method === "POST") {
      const { firstName, lastName, email, receiveMarketingInfo } = await req.json();

      const response = await fetch(`https://api.resend.com/audiences/${AUDIENCE_ID}/contacts`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${Deno.env.get("RESEND_API_KEY")}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: email,
          first_name: firstName,
          last_name: lastName,
          unsubscribed: !receiveMarketingInfo,
        }),
      });

      console.log('subscription_status: ', !receiveMarketingInfo);
  
      if (!response.ok) {
        const errorText = await response.text(); // Get the error response text
        console.error("Error response from Resend API:", errorText);
        throw new Error(`Failed to add to marketing list: ${errorText}`);
      }
  
      await response.json();
      return new Response(
        JSON.stringify({
          success: true,
        }),
        { status: 200, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    return new Response(JSON.stringify({ error: `Method ${req.method} not allowed on this path` }), { status: 405, headers: { ...corsHeaders, "Content-Type": "application/json" } });
  } catch (error) {
    console.error("API Error:", error);
    return new Response(JSON.stringify({ success: false, error: error instanceof Error ? error.message : "Server error" }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});
