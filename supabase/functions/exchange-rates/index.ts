// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

import { corsHeaders } from "../_shared/config.ts";
import { createClient } from "jsr:@supabase/supabase-js";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

console.log("Exchange Rates Function Started!")

interface ExchangeRateResponse {
  success: boolean
  base: string
  rates: Record<string, number>
}

// Currency code to country name mapping
const currencyToCountry: Record<string, string> = {
  'BWP': 'Botswana',
  'EGP': 'Egypt',
  'GHS': 'Ghana',
  'INR': 'India',
  'IDR': 'Indonesia',
  'KES': 'Kenya',
  'KWD': 'Kuwait',
  'LRD': 'Liberia',
  'MWK': 'Malawi',
  'MYR': 'Malaysia',
  'NAD': 'Namibia',
  'NGN': 'Nigeria',
  'PKR': 'Pakistan',
  'PHP': 'Philippines',
  'RWF': 'Rwanda',
  'SAR': 'Saudi Arabia',
  'SLL': 'Sierra Leone',
  'SGD': 'Singapore',
  'ZAR': 'South Africa',
  'SSP': 'South Sudan',
  'TZS': 'Tanzania',
  'THB': 'Thailand',
  'AED': 'United Arab Emirates',
  'ZMW': 'Zambia',
  'ZWL': 'Zimbabwe'
}

serve(async (req: Request) => {
  // Handle CORS preflight request
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        {
          status: 405,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      )
    }

    // Get API key from environment variables
    const apiKey = Deno.env.get('FOREX_API_KEY')
    if (!apiKey) {
      console.error('FOREX_API_KEY environment variable is not set')
      return new Response(
        JSON.stringify({ error: 'API key not configured' }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      )
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Fetch exchange rates from API
    const apiUrl = `https://api.forexrateapi.com/v1/latest?api_key=${apiKey}&base=GBP&currencies=BWP,EGP,GHS,INR,IDR,KES,KWD,LRD,MWK,MYR,NAD,NGN,PKR,PHP,RWF,SAR,SLL,SGD,ZAR,SSP,TZS,THB,AED,ZMW,ZWL`
    
    console.log('Fetching exchange rates from API...')
    const response = await fetch(apiUrl)
    
    if (!response.ok) {
      console.error(`API request failed: ${response.status} ${response.statusText}`)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch exchange rates from API' }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      )
    }

    const data: ExchangeRateResponse = await response.json()
    
    if (!data.success || !data.rates) {
      console.error('Invalid API response:', data)
      return new Response(
        JSON.stringify({ error: 'Invalid response from exchange rate API' }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      )
    }

    console.log(`Received rates for ${Object.keys(data.rates).length} currencies`)

    // Prepare data for database insertion
    const exchangeRateRecords = Object.entries(data.rates).map(([currencyCode, rate]) => ({
      currency_code: currencyCode,
      country_name: currencyToCountry[currencyCode] || currencyCode,
      rate_per_gbp: rate,
      last_updated: new Date().toISOString(),
    }))

    // Update exchange rates in database using upsert
    const { data: upsertData, error: upsertError } = await supabase
      .from('mp_ecommerce_exchange_rates')
      .upsert(
        exchangeRateRecords,
        { 
          onConflict: 'currency_code',
          ignoreDuplicates: false 
        }
      )
      .select()

    if (upsertError) {
      console.error('Database upsert error:', upsertError)
      return new Response(
        JSON.stringify({ error: 'Failed to update exchange rates in database' }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      )
    }

    console.log(`Successfully updated ${exchangeRateRecords.length} exchange rates`)

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully updated ${exchangeRateRecords.length} exchange rates`,
        base_currency: data.base,
        last_updated: new Date().toISOString(),
        currencies_updated: Object.keys(data.rates)
      }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    )

  } catch (error) {
    console.error('Unexpected error:', error)
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    )
  }
});

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Set environment variable: FOREX_API_KEY=your_actual_api_key
*/
