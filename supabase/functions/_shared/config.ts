export const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, PUT, OPTIONS",
};

export interface PaymentRequestBody {
  amount: number;
  parcelId: number;
  trackingId: string;
  email: string;
  is_discounted: boolean;
  discount_amount: string;
  discount_code: string;
  original_amount: string;
}

export interface FulfillmentRequestBody {
  trackingId: string;
  uuid: string;
  shippingMethod: string;
  isInsured: boolean;
  paymentIntentId: string;
  shipmentPrice: number;
  insuranceAmount: number;
  totalDue: number;
}
