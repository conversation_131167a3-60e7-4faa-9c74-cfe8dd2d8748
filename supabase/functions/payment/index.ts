// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

import { corsHeaders } from "../_shared/config.ts";
import { createClient } from "jsr:@supabase/supabase-js";
import Stripe from "https://esm.sh/stripe@12.0.0?target=deno";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// Initialize Stripe
const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") || "", {
  apiVersion: "2023-10-16",
  httpClient: Stripe.createFetchHttpClient(),
});

const supabaseClient = createClient(Deno.env.get("DB_URL")!, Deno.env.get("SERVICE_ROLE_KEY")!);

serve(async (req: Request) => {
  const url = new URL(req.url);
  
  // Handle CORS preflight request
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (req.method === "POST") {
      const { amount, parcelId, trackingId, email, is_discounted, discount_amount, discount_code, original_amount } = await req.json();

      // Validate amount
      const paymentAmount = Number(amount);
      if (isNaN(paymentAmount) || paymentAmount <= 0) {
        return new Response(JSON.stringify({ success: false, error: "Invalid amount" }), { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } });
      }

      // Create or retrieve a customer
      const customers = await stripe.customers.list({ email });
      const customer = customers.data.length > 0 ? customers.data[0] : await stripe.customers.create({ email });

      // Create PaymentIntent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(paymentAmount * 100),
        currency: "gbp",
        customer: customer.id,
        metadata: {
          email,
          parcel_id: parcelId,
          tracking_id: trackingId,
          discount_code,
          is_discounted: String(is_discounted),
          discount_amount: String(discount_amount),
          original_amount: String(original_amount),
        },
        receipt_email: email,
      });

      return new Response(
        JSON.stringify({
          success: true,
          customerId: customer.id,
          metadata: paymentIntent.metadata,
          clientSecret: paymentIntent.client_secret,
        }),
        { status: 200, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    if (req.method === "PUT") {
      let fulfillmentResponse = null;

      const { trackingId, uuid, shippingMethod, paymentIntentId, isInsured, shipmentPrice, insuranceAmount, totalDue } = await req.json();

      if (!trackingId || !uuid || !paymentIntentId) {
        return new Response(JSON.stringify({ success: false, error: "Required fields missing" }), { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } });
      }

      const validatedShipmentPrice = Number(shipmentPrice);
      const validatedInsuranceAmount = Number(insuranceAmount);
      const validatedTotalDue = Number(totalDue);

      if (isNaN(validatedShipmentPrice) || isNaN(validatedInsuranceAmount) || isNaN(validatedTotalDue)) {
        return new Response(JSON.stringify({ success: false, error: "Invalid numeric values provided" }), { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } });
      }

      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      const charge = paymentIntent.latest_charge as string;
      let receiptUrl = null;

      if (charge) {
        const chargeDetails = await stripe.charges.retrieve(charge);
        receiptUrl = chargeDetails.receipt_url;
      }

      const originalAmount = Number(paymentIntent.metadata.original_amount) || 0;
      const discountAmount = Number(paymentIntent.metadata.discount_amount) || 0;
      const totalDueOnDiscount = originalAmount - discountAmount;

      const { data: existingFulfillment, error: existingFulfillmentError } = await supabaseClient
        .from("mp_awaiting_fulfilment")
        .select("*")
        .eq("tracking_id", trackingId);

      if (existingFulfillment && existingFulfillment.length > 0) {
        return new Response(JSON.stringify({ success: false, error: "Fulfillment already exists" }), { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } });
      }

      // Create new fulfillment entry
      const { data: fulfillmentData, error: fulfillmentError } = await supabaseClient
        .from("mp_awaiting_fulfilment")
        .insert([{ tracking_id: trackingId, uuid }])
        .select();

      if (fulfillmentError) {
        return new Response(JSON.stringify({ success: false, error: fulfillmentError.message }), { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } });
      }

      fulfillmentResponse = fulfillmentData;

      const updateData = {
        is_payed_for: true,
        invoice: receiptUrl,
        payment_method: "stripe",
        is_insured: Boolean(isInsured),
        payment_date: new Date().toISOString(),
        selected_shipping_method_id: Number(shippingMethod),
        is_discounted: paymentIntent.metadata.is_discounted === "true",
        discount_amount: Number(paymentIntent.metadata.discount_amount) || 0,
        total_due: paymentIntent.metadata.is_discounted === "true" ? totalDueOnDiscount : validatedTotalDue,
      };

      const { error: updateError } = await supabaseClient.from("users_parcels_details").update(updateData).eq("tracking_id", trackingId);

      if (updateError) {
        return new Response(JSON.stringify({ success: false, error: updateError.message }), { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } });
      }

      return new Response(JSON.stringify({ success: true, data: fulfillmentResponse, receiptUrl }), { status: 200, headers: { ...corsHeaders, "Content-Type": "application/json" } });
    }

    if (req.method === "GET") {
      const paymentIntentId = url.searchParams.get("payment_intent");

      if (!paymentIntentId) {
        return new Response(JSON.stringify({ error: "Payment intent ID is required" }), { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } });
      }

      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      const charge = paymentIntent.latest_charge as string;

      if (charge) {
        const chargeDetails = await stripe.charges.retrieve(charge);
        return new Response(JSON.stringify({ receiptUrl: chargeDetails.receipt_url }), { status: 200, headers: { ...corsHeaders, "Content-Type": "application/json" } });
      }

      return new Response(JSON.stringify({ error: "No receipt available" }), { status: 404, headers: { ...corsHeaders, "Content-Type": "application/json" } });
    }

    return new Response(JSON.stringify({ error: `Method ${req.method} not allowed on this path` }), { status: 405, headers: { ...corsHeaders, "Content-Type": "application/json" } });
  } catch (error) {
    console.error("API Error:", error);
    return new Response(JSON.stringify({ success: false, error: error instanceof Error ? error.message : "Server error" }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});
