// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

import { corsHeaders } from "../_shared/config.ts";
import { createClient } from "jsr:@supabase/supabase-js";
import Stripe from "https://esm.sh/stripe@12.0.0?target=deno";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// Initialize Stripe
const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") || "", {
  apiVersion: "2023-10-16",
  httpClient: Stripe.createFetchHttpClient(),
});

const supabaseClient = createClient(
  Deno.env.get("DB_URL")!,
  Deno.env.get("SERVICE_ROLE_KEY")!,
);

serve(async (req: Request) => {
  const url = new URL(req.url);
  // Use static corsHeaders (wildcard) to avoid origin mismatches in browsers.
  // Keep responseHeaders as a simple alias to the shared corsHeaders.
  const responseHeaders = corsHeaders;

  // Helper to standardize JSON responses with proper headers.
  // Placed here so it can close over `responseHeaders` defined above.
  const respondJson = (body: any, status = 200, headers = responseHeaders) => {
    return new Response(JSON.stringify(body), {
      status,
      headers: { ...headers, "Content-Type": "application/json" },
    });
  };

  // Handle CORS preflight request
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: responseHeaders });
  }

  try {
    if (req.method === "POST") {
      const {
        userId,
        email,
        items,
        subtotal,
        taxAmount,
        shippingCost,
        // Packer-derived duty (GBP) passed from the client
        duty,
        totalAmount,
        currency = "gbp",
        discountCode,
        isDiscounted,
        originalAmount,
        discountAmount,
      } = await req.json();

      // Validate required fields
      if (
        !userId ||
        !email ||
        !items ||
        !Array.isArray(items) ||
        items.length === 0
      ) {
        return respondJson(
          { success: false, error: "Missing required fields" },
          400,
        );
      }

      // Validate amount
      const paymentAmount = Number(totalAmount);
      if (isNaN(paymentAmount) || paymentAmount <= 0) {
        return respondJson({ success: false, error: "Invalid amount" }, 400);
      }

      // Create or retrieve a customer
      const customers = await stripe.customers.list({ email });
      const customer =
        customers.data.length > 0
          ? customers.data[0]
          : await stripe.customers.create({ email });

      // Create PaymentIntent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(paymentAmount * 100),
        currency: currency.toLowerCase(),
        customer: customer.id,
        metadata: {
          email,
          user_id: String(userId),
          order_type: "shop_order",
          items_count: String(items.length),
          subtotal: String(subtotal || 0),
          tax_amount: String(taxAmount || 0),
          shipping_cost: String(shippingCost || 0),
          // include duty in metadata for reconciliation
          duty: String(duty || 0),
          discount_code: discountCode || "",
          is_discounted: String(isDiscounted || false),
          discount_amount: String(discountAmount || 0),
          original_amount: String(originalAmount || totalAmount),
        },
        receipt_email: email,
      });

      return respondJson(
        {
          success: true,
          customerId: customer.id,
          metadata: paymentIntent.metadata,
          clientSecret: paymentIntent.client_secret,
        },
        200,
      );
    }

    if (req.method === "GET") {
      const paymentIntentId = url.searchParams.get("payment_intent");

      if (!paymentIntentId) {
        return respondJson({ error: "Payment intent ID is required" }, 400);
      }

      const paymentIntent =
        await stripe.paymentIntents.retrieve(paymentIntentId);
      const charge = paymentIntent.latest_charge as string;

      if (charge) {
        const chargeDetails = await stripe.charges.retrieve(charge);
        return respondJson({ receiptUrl: chargeDetails.receipt_url }, 200);
      }

      return respondJson({ error: "No receipt available" }, 404);
    }

    return respondJson(
      { error: `Method ${req.method} not allowed on this path` },
      405,
    );
  } catch (error) {
    console.error("API Error:", error);
    return respondJson(
      {
        success: false,
        error: error instanceof Error ? error.message : "Server error",
      },
      500,
    );
  }
});
