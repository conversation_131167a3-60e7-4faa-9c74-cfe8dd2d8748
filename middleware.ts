import { type NextRequest, NextResponse } from "next/server";
import { updateSession } from "@/lib/supabase/middleware";

export async function middleware(request: NextRequest) {
  const { supabaseResponse: response, user } = await updateSession(request);

  // Get admin data from cookie
  const adminCookie = request.cookies.get("_auth_admin")?.value;
  let adminData = null;

  if (adminCookie) {
    try {
      adminData = JSON.parse(adminCookie);
    } catch (error) {
      console.error("Error parsing admin cookie:", error);
    }
  }

  const isAdmin = !!adminData;

  // Define route categories
  // `protectedRoutes` contained "/home/<USER>" which prevented unauthenticated users
  // from viewing the shop. We remove it so the shop becomes public.
  const protectedRoutes: string[] = [];
  const adminRoutes = [
    "/admin/dashboard",
    "/admin/products",
    "/admin/parcels",
    "/admin/edit-parcel",
    "/admin/consolidate",
    "/admin/users",
    "/admin/profile",
    "/admin/orders",
  ];
  // Remove "/home/<USER>" from publicRoutes so admins can navigate to the shop
  // without being redirected back to the admin dashboard.
  const publicRoutes = ["/login", "/", "/admin"];

  const isProtectedRoute = protectedRoutes.some((route) =>
    request.nextUrl.pathname.startsWith(route),
  );
  const isAdminRoute = adminRoutes.some((route) =>
    request.nextUrl.pathname.startsWith(route),
  );
  const isPublicRoute = publicRoutes.includes(request.nextUrl.pathname);

  // Check if this is a logout request
  const isLogoutRequest = request.nextUrl.searchParams.get("logout") === "true";

  // Redirect logic for authenticated users (skip if logging out)
  if (user && isPublicRoute && !isLogoutRequest) {
    if (isAdmin) {
      return NextResponse.redirect(new URL("/admin/dashboard", request.url));
    } else {
      return NextResponse.redirect(new URL("/home/<USER>", request.url));
    }
  }

  // Protect admin routes
  if (isAdminRoute && (!user || !isAdmin)) {
    return NextResponse.redirect(new URL("/login", request.url));
  }

  // Protect regular user routes (allow both regular users and admins)
  if (!user && isProtectedRoute) {
    return NextResponse.redirect(new URL("/login", request.url));
  }

  return response;
}

export const config = {
  matcher: [
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
