import { UserService } from "./user.service";
import { isDevEnvironment } from "@/lib/utils";
import { supabase } from "@/lib/supabase_client";
import { Admin } from "@/data/models/admin.model";

export enum LoginResult {
  failure = "failure",
  success = "success",
  unauthorized = "unauthorized",
  unconfirmedUser = "unconfirmedUser",
  invalidCredentials = "invalidCredentials",
}

class LoginResultData {
  constructor(
    public result: LoginResult,
    public userId?: string,
    public email?: string,
    public customMessage?: string,
  ) {}
}

export class AuthService {
  async login(email: string, password: string): Promise<LoginResultData> {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: email,
      password: password,
    });

    if (error) {
      if (error.message.includes("Invalid login credentials")) {
        console.log("Invalid login credentials");
        return new LoginResultData(LoginResult.invalidCredentials);
      } else if (error.message.includes("Email not confirmed")) {
        console.log("Email not confirmed");
        return new LoginResultData(LoginResult.unconfirmedUser);
      }

      const userExists = await this.checkUserExists(email);
      if (userExists) {
        console.log("User exists in Supabase");
        return new LoginResultData(LoginResult.success);
      }
      return new LoginResultData(LoginResult.failure);
    } else {
      const session = data.session;
      const user = data.user;

      if (user !== null && session !== null) {
        // Fetch user data from the API route instead of Supabase directly
        const response = await fetch(
          `/api/users/lookup?email=${encodeURIComponent(email)}`,
        );
        if (!response.ok) {
          console.error(
            "Error fetching mp_users data via API:",
            await response.text(),
          );
          return new LoginResultData(LoginResult.failure);
        }
        const result = await response.json();
        const mpUserData = result.data;

        if (!mpUserData) {
          console.error("No matching user found in mp_users table");
          return new LoginResultData(LoginResult.failure);
        }

        // Store both auth and mp_users data in localStorage
        localStorage.setItem("__mp_user_data", JSON.stringify(mpUserData));

        // Check if user is also an admin
        try {
          const adminResponse = await fetch(`/api/auth/check-admin`);

          if (adminResponse.ok) {
            const adminResult = await adminResponse.json();

            if (adminResult.isAdmin && adminResult.data) {
              // User is also an admin, set admin cookie
              if (typeof window !== "undefined") {
                document.cookie = `_auth_admin=${JSON.stringify(adminResult.data)}; path=/; SameSite=Lax; Secure=${location.protocol === "https:"}`;
              }
              console.log("User authenticated as both user and admin");
            }
          }
        } catch (adminError) {
          // Admin check failed, but user login was successful, so continue
          console.log(
            "Admin check failed, but user login successful:",
            adminError,
          );
        }

        return new LoginResultData(LoginResult.success);
      } else if (user !== null) {
        return new LoginResultData(LoginResult.unconfirmedUser);
      } else {
        return new LoginResultData(LoginResult.failure);
      }
    }
  }

  async adminLogin(email: string, password: string): Promise<Admin> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      // Handle errors from the sign-in
      if (error || !data.user) {
        console.error("Error signing in:", error);
        throw new Error("Invalid email or password.");
      }

      // Fetch the admin details from the API route using the email
      const response = await fetch(
        `/api/admin/users/lookup?email=${encodeURIComponent(email)}`,
      );
      if (!response.ok) {
        console.error("Error fetching admin via API:", await response.text());
        throw new Error("Unable to fetch admin details.");
      }
      const result = await response.json();
      const adminData = result.data;

      if (!adminData) {
        throw new Error("Unable to fetch admin details.");
      }

      // Set admin cookie instead of sessionStorage
      if (typeof window !== "undefined") {
        document.cookie = `_auth_admin=${JSON.stringify(adminData)}; path=/; SameSite=Lax; Secure=${location.protocol === "https:"}`;
      }

      // Return the admin info
      return adminData as Admin;
    } catch (err) {
      console.error("Login failed:", err);
      throw new Error("Unable to login. Please try again later.");
    }
  }

  async checkUserAddressAndNavigateUser(): Promise<void> {
    const userService = new UserService();
    const userProfile = await userService.getUserProfile();

    const response = await fetch(
      "/api/users_address/lookup?email=<EMAIL>",
    );

    let addressData = null;
    if (response.ok) {
      const result = await response.json();
      addressData = result.data;
    }

    if (addressData) {
      window.location.href = "/home/<USER>";
    } else {
      sessionStorage.setItem("first_name", userProfile?.first_name ?? "");
      sessionStorage.setItem("last_name", userProfile?.last_name ?? "");
      sessionStorage.setItem("email_address", userProfile?.email ?? "");
      window.location.href = "/address";
    }
  }

  redirectToShop(): void {
    window.location.href = "/home/<USER>";
  }

  handleLoginResult(
    resultData: LoginResultData,
    customMessage?: string,
  ): void | string | Promise<void> {
    switch (resultData.result) {
      case LoginResult.success:
        // Check if user is also an admin
        if (typeof window !== "undefined") {
          const adminCookie = document.cookie
            .split("; ")
            .find((row) => row.startsWith("_auth_admin="));

          if (adminCookie) {
            // User is an admin, redirect to admin dashboard
            window.location.href = "/admin/dashboard";
            return;
          }
        }
        return this.redirectToShop();
      case LoginResult.failure:
        return (
          resultData.customMessage ??
          customMessage ??
          "Login failed. Please try again."
        );
      case LoginResult.invalidCredentials:
        return customMessage ?? "Invalid email or password. Please try again.";
      case LoginResult.unauthorized:
        return (
          customMessage ??
          "Login failed. Please check the email and password you provided."
        );
      case LoginResult.unconfirmedUser:
        return (
          customMessage ??
          "Please verify your account by clicking the link in your email."
        );
    }
  }

  async checkUserExists(email: string): Promise<boolean> {
    try {
      const response = await fetch(
        `/api/users/lookup?email=${encodeURIComponent(email)}`,
      );
      if (!response.ok) {
        // If the response is not ok, treat as user not found or error
        return false;
      }
      const result = await response.json();
      // Assuming the API returns { data: ... }
      if (!result || result.data === null) {
        return false;
      }
      // User exists
      return true;
    } catch (error) {
      console.error("Error checking if user exists:", error);
      return false;
    }
  }

  async sendResetPasswordEmail(email: string): Promise<boolean> {
    // Check if user exists
    const { data } = await supabase
      .from("users")
      .select("*")
      .eq("email", email)
      .single();

    if (data === null) {
      return false;
    } else {
      // User exists, attempt to send the reset password email
      const { error: resetError } = await supabase.auth.resetPasswordForEmail(
        email,
        {
          redirectTo: isDevEnvironment()
            ? `${window.location.origin}/reset`
            : `https://mailpallet.com/reset`,
        },
      );

      // Return true if the email was sent successfully, false if there was an error
      return resetError === null;
    }
  }

  async resetUserPassword(
    password: string,
    confirmPassword: string,
  ): Promise<string | boolean> {
    if (password !== confirmPassword) {
      return "Passwords provided do not match.";
    }

    try {
      // Update the password using the current session
      const { data, error } = await supabase.auth.updateUser({
        password: password,
      });

      if (error) {
        console.error("Error updating password:", error);
        return error.message;
      }

      if (data.user) {
        // Sign out the user and clear any stored data
        await supabase.auth.signOut();
        localStorage.removeItem("__mp_user_data");
        sessionStorage.clear();
        return true;
      }

      return "Failed to update password. Please try again.";
    } catch (error) {
      console.error("Error in resetUserPassword:", error);
      return "An error occurred while resetting your password. Please try again.";
    }
  }

  async signOut() {
    // Clear user data from localStorage
    if (typeof window !== "undefined") {
      localStorage.removeItem("__mp_user_data");

      // Clear admin cookie
      document.cookie =
        "_auth_admin=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
    }

    // Sign out from Supabase
    await supabase.auth.signOut();

    // Force immediate redirect to home shop with logout parameter
    if (typeof window !== "undefined") {
      window.location.replace("/home/<USER>");
    }
  }
}
