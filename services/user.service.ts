import { supabase } from "@/lib/supabase_client";

export interface UserData {
  id?: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  email: string;
  receive_marketing: boolean;
  signed_shipping_policy?: boolean;
  signed_privacy_policy?: boolean;
  country_code?: string;
}

export interface RegisterModel {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  countryCode: string;
  mobileNumber: string;
  confirmPassword: string;
  receiveMarketingInfo?: boolean;
  agreeToShippingPolicy: boolean;
  agreeToPrivacyPolicy: boolean;
}

export interface UserDetails extends UserData {
  deliverTo: string;
  company?: string;
  address: string;
  city: string;
  stateProvince: string;
  postalCode: string;
}

const API_URL = process.env.NEXT_PUBLIC_SUPABASE_FUNCTIONS_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

export class UserService {
  async createAccount(model: RegisterModel): Promise<boolean | string> {
    try {
      // Combine country code and phone number
      const fullPhoneNumber = `${model.countryCode}${model.mobileNumber}`;

      // Check if email or phone number already exists
      const emailExists = await this.checkEmailExists(model.email);
      const phoneExists = await this.checkPhoneExists(fullPhoneNumber);

      if (emailExists || phoneExists) {
        let message = "";
        if (emailExists && phoneExists) {
          message = "Email and phone number are already in use.";
        } else if (emailExists) {
          message = "Email is already in use.";
        } else {
          message = "Phone number is already in use.";
        }
        return message;
      }

      // Insert the data into the Supabase table
      const { data, error } = await supabase.auth.signUp({
        email: model.email,
        password: model.password,
      });

      const user = data?.user;
      if (error) {
        console.error("Supabase signUp error:", error.message);
        return false;
      }

      if (user) {
        await this.insertUserDetails(
          user.id,
          model.email,
          model.firstName,
          model.lastName,
          model.countryCode,
          fullPhoneNumber,
          model.receiveMarketingInfo || false,
          model.agreeToShippingPolicy,
          model.agreeToPrivacyPolicy,
        );

        // Call the Supabase function to add to the marketing list
        const response = await this.addOrRemoveFromMarketingList(
          model.email,
          model.firstName,
          model.lastName,
          model.receiveMarketingInfo || false,
        );
        if (!response) {
          console.error("Error adding to marketing list");
        }
      }

      // If we reach this point without throwing an exception, the insert was successful
      return true;
    } catch (e) {
      console.error("Error during registration:", e);
      return false;
    }
  }

  async getUserProfile(): Promise<UserData> {
    try {
      const response = await fetch("/api/users/profile");
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Error fetching user profile");
      }
      const data = result.data;
      return {
        id: data.id,
        first_name: data.first_name || "",
        last_name: data.last_name || "",
        phone_number: data.phone_number || "",
        email: data.email || "",
        receive_marketing: data.receive_marketing || false,
      };
    } catch (e) {
      console.error("Error fetching user profile:", e);
      throw e;
    }
  }

  private async checkEmailExists(email: string): Promise<boolean> {
    const response = await fetch(
      `/api/users/check-email?email=${encodeURIComponent(email)}`,
    );
    const result = await response.json();
    if (!response.ok) {
      console.error("Error checking email existence:", result.error);
      return false;
    }
    // The double exclamation marks (!!) convert the value of result.exists to a boolean.
    // If result.exists is truthy (e.g., true, 1, "yes"), it returns true; otherwise, it returns false.
    return !!result.exists;
  }

  private async checkPhoneExists(phoneNumber: string): Promise<boolean> {
    const response = await fetch(
      `/api/users/check-phone?phoneNumber=${encodeURIComponent(phoneNumber)}`,
    );
    const result = await response.json();
    if (!response.ok) {
      console.error("Error checking phone number existence:", result.error);
      return false;
    }
    return !!result.exists;
  }

  private async insertUserDetails(
    uuid: string,
    email: string,
    firstName: string,
    lastName: string,
    countryCode: string,
    fullPhoneNumber: string,
    receiveMarketingInfo: boolean,
    agreeToShippingPolicy: boolean,
    agreeToPrivacyPolicy: boolean,
  ): Promise<void> {
    try {
      const response = await fetch("/api/users/profile", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          uuid,
          email,
          last_name: lastName,
          first_name: firstName,
          country_code: countryCode,
          phone_number: fullPhoneNumber,
          receive_marketing: receiveMarketingInfo,
          signed_shipping_policy: agreeToShippingPolicy,
          signed_privacy_policy: agreeToPrivacyPolicy,
        }),
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Error inserting user details");
      }
    } catch (e) {
      console.error("Error inserting user shipping ID:", e);
      throw e;
    }
  }

  async updateUserMarketingPreference(
    receiveMarketing: boolean,
  ): Promise<boolean> {
    try {
      const response = await fetch("/api/users/marketing-preference", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ receiveMarketing }),
      });
      const result = await response.json();
      if (!response.ok)
        throw new Error(result.error || "Error updating marketing preference");
      return true;
    } catch (error) {
      console.error("Error updating user marketing preference:", error);
      return false;
    }
  }

  async updateUserProfile(userData: Partial<UserData>): Promise<boolean> {
    try {
      const response = await fetch("/api/users/profile-update", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(userData),
      });
      const result = await response.json();
      if (!response.ok)
        throw new Error(result.error || "Error updating user profile");
      return true;
    } catch (error) {
      console.error("Error updating user profile:", error);
      return false;
    }
  }

  async addOrRemoveFromMarketingList(
    email: string,
    firstName: string,
    lastName: string,
    receiveMarketingInfo: boolean,
  ): Promise<boolean> {
    const response = await fetch(`${API_URL}/newsletter-subscription`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
      },
      body: JSON.stringify({
        email: email,
        firstName: firstName,
        lastName: lastName,
        receiveMarketingInfo: receiveMarketingInfo,
      }),
    });

    const { success } = await response.json();
    return success;
  }

  async getUserDetailsById(userId: string): Promise<UserDetails | null> {
    try {
      const response = await fetch(
        `/api/users/details?userId=${encodeURIComponent(userId)}`,
      );
      const result = await response.json();
      if (!response.ok) {
        console.error("Error fetching user details:", result.error);
        return null;
      }
      return result.data as UserDetails;
    } catch (e) {
      console.error("Error fetching user details:", e);
      return null;
    }
  }

  async getUserDetailsByParcelId(
    parcelId: string,
  ): Promise<UserDetails | null> {
    try {
      // First, fetch the parcel details to get the user_id
      const { data: parcelData, error: parcelError } = await supabase
        .from("users_parcels_details")
        .select("user_id")
        .eq("tracking_id", parcelId)
        .single();

      if (parcelError) {
        console.error("Error fetching parcel data:", parcelError.message);
        return null;
      }

      if (!parcelData || !parcelData.user_id) {
        console.log("No parcel found with tracking ID:", parcelId);
        return null;
      }

      // Use the existing getUserDetailsById function with the extracted user_id
      return await this.getUserDetailsById(parcelData.user_id.toString());
    } catch (e) {
      console.error("Error fetching user details by parcel ID:", e);
      return null;
    }
  }

  async sendEmail(to: string, subject: string, html: string) {
    const response = await fetch("https://sendemail-bbwyvxdasa-uc.a.run.app", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        to,
        subject,
        html,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(
        data.error || `Failed to send email (${response.status})`,
      );
    }

    return data;
  }
}
