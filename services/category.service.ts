export interface CategoryVariantAttribute {
  name: string;
  values: Array<any>;
  type: "enum";
  options?: string[];
}

export interface Category {
  id: string;
  name: string;
  parent_id: string | null;
}

export class CategoryService {
  async getCategoryVariantAttributes(
    categoryId: string,
  ): Promise<CategoryVariantAttribute[] | null> {
    try {
      const response = await fetch(
        `/api/categories/variant-attributes?categoryId=${encodeURIComponent(categoryId)}`,
      );
      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          "Error fetching category variant attributes via API:",
          errorText,
        );
        throw new Error(errorText);
      }
      const result = await response.json();
      return (result.data as CategoryVariantAttribute[]) || null;
    } catch (error) {
      console.error("Error in getCategoryVariantAttributes:", error);
      throw error;
    }
  }

  async getCategoryBrands(categoryId: string): Promise<string[] | null> {
    try {
      const response = await fetch(
        `/api/categories/brands?categoryId=${encodeURIComponent(categoryId)}`,
      );
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error fetching category brands via API:", errorText);
        throw new Error(errorText);
      }
      const result = await response.json();
      return (result.brands as string[]) || null;
    } catch (error) {
      console.error("Error in getCategoryBrands:", error);
      throw error;
    }
  }

  async getCategories(): Promise<Category[]> {
    try {
      const response = await fetch(`/api/categories/list`);
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error fetching categories via API:", errorText);
        throw new Error(errorText);
      }
      const result = await response.json();
      return (result.data as Category[]) || [];
    } catch (error) {
      console.error("Error in getCategories:", error);
      throw error;
    }
  }
}
