import { supabase } from "@/lib/supabase_client";
import {
  Order,
  OrderStatus,
  ShippingAddress,
  ShippingDetails,
} from "@/data/models/order.model";

export class OrderService {
  public orders: Order[] = [];

  /**
   * Create a new order
   */
  async createOrder(order: Order): Promise<void> {
    try {
      const response = await fetch("/api/orders/create", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(order),
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error creating order via API:", errorText);
        throw new Error(errorText);
      }
      // Optionally, you can use the returned data if needed:
      // const result = await response.json();
    } catch (error) {
      console.error("Error in createOrder:", error);
      throw error;
    }
  }
  /**
   * Fetch orders with pagination and optional filtering
   */
  async listOrders(
    status?: OrderStatus,
    searchTerm?: string,
    page: number = 1,
    limit: number = 10,
    userId?: string,
  ): Promise<{ orders: Order[]; total: number }> {
    try {
      const params = new URLSearchParams();
      if (status) params.append("status", status);
      if (searchTerm) params.append("searchTerm", searchTerm);
      if (userId) params.append("userId", userId);
      params.append("page", page.toString());
      params.append("limit", limit.toString());

      const response = await fetch(`/api/orders/list?${params.toString()}`);
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error fetching orders via API:", errorText);
        throw new Error(errorText);
      }
      const result = await response.json();
      this.orders = result.orders || [];
      return { orders: result.orders || [], total: result.total || 0 };
    } catch (error) {
      console.error("Error in listOrders:", error);
      throw error;
    }
  }

  /**
   * Update order status and add entry to status history
   */
  async updateOrderStatus(
    orderId: string,
    newStatus: OrderStatus,
    notes?: string,
    updatedBy?: string,
  ): Promise<boolean> {
    try {
      const response = await fetch("/api/orders/status", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ orderId, newStatus, notes, updatedBy }),
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error updating order status via API:", errorText);
        throw new Error(errorText);
      }
      return true;
    } catch (error) {
      console.error("Error in updateOrderStatus:", error);
      return false;
    }
  }

  /**
   * Add or update admin notes for an order
   */
  async addAdminNote(orderId: string, adminNotes: string): Promise<boolean> {
    try {
      const response = await fetch("/api/orders/admin-note", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ orderId, adminNotes }),
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error adding admin note via API:", errorText);
        throw new Error(errorText);
      }
      return true;
    } catch (error) {
      console.error("Error in addAdminNote:", error);
      return false;
    }
  }

  /**
   * Update shipping details for an order
   */
  async updateShippingDetails(
    orderId: string,
    shippingDetails: Partial<ShippingDetails>,
  ): Promise<boolean> {
    try {
      const response = await fetch("/api/orders/shipping-details", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ orderId, shippingDetails }),
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error updating shipping details via API:", errorText);
        throw new Error(errorText);
      }
      return true;
    } catch (error) {
      console.error("Error in updateShippingDetails:", error);
      return false;
    }
  }

  /**
   * Cancel an order
   */
  async cancelOrder(
    orderId: string,
    reason?: string,
    cancelledBy?: string,
  ): Promise<boolean> {
    try {
      // Use the updateOrderStatus method to handle status change and history
      return await this.updateOrderStatus(
        orderId,
        "cancelled",
        reason,
        cancelledBy,
      );
    } catch (error) {
      console.error("Error in cancelOrder:", error);
      return false;
    }
  }

  /**
   * Modify shipping address for an order
   */
  async modifyShippingAddress(
    orderId: string,
    shippingAddress: Partial<ShippingAddress>,
  ): Promise<boolean> {
    try {
      const response = await fetch("/api/orders/shipping-address", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ orderId, shippingAddress }),
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error updating shipping address via API:", errorText);
        throw new Error(errorText);
      }
      return true;
    } catch (error) {
      console.error("Error in modifyShippingAddress:", error);
      return false;
    }
  }

  /**
   * Get a single order by ID
   */
  async getOrderById(orderId: string): Promise<Order | null> {
    try {
      const response = await fetch(
        `/api/orders/id?orderId=${encodeURIComponent(orderId)}`,
      );
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error fetching order by ID via API:", errorText);
        throw new Error(errorText);
      }
      const result = await response.json();
      return result.data as Order;
    } catch (error) {
      console.error("Error in getOrderById:", error);
      return null;
    }
  }

  /**
   * Get orders by user ID
   */
  async getOrdersByUserId(
    userId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ orders: Order[]; total: number }> {
    return this.listOrders(undefined, undefined, page, limit, userId);
  }

  /**
   * Get orders by status
   */
  async getOrdersByStatus(
    status: OrderStatus,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ orders: Order[]; total: number }> {
    return this.listOrders(status, undefined, page, limit);
  }

  /**
   * Process refund for an order
   */
  async processRefund(orderId: string, amount: number): Promise<void> {}
}
