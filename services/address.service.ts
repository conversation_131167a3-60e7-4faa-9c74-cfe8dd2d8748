import { supabase } from "@/lib/supabase_client";

export interface UserAddressModel {
  uuid?: string;
  user_id?: string;
  DeliverTo?: string;
  FirstName?: string;
  LastName?: string;
  Company?: string;
  Street?: string;
  City?: string;
  StateProvince?: string;
  PostalCode?: string;
  Address?: string;
  EmailAddress?: string;
  WithinAccra?: boolean;
  OutsideAccra?: boolean;
  WithinLagos?: boolean;
  OutsideLagos?: boolean;
}

export interface MailpalletLocation {
  id: number;
  address_line_one: string;
  address_line_two: string;
  city: string;
  postcode: string;
  country: string;
  phone_number: string;
  state?: string;
  zip_code?: string;
}

export class AddressService {
  async fetchUserAddress(): Promise<UserAddressModel> {
    try {
      const res = await fetch("/api/address");
      const address = await res.json();
      // console.log("Fetched UserAddress data:", data);
      return address as UserAddressModel;
    } catch (e) {
      console.error("Error fetching user address:", e);
      return {}; // Return an empty object if no data found
    }
  }

  async fetchUserLocations(): Promise<MailpalletLocation[]> {
    try {
      const res = await fetch("/api/locations");
      const locations = await res.json();
      return locations as MailpalletLocation[];
    } catch (e) {
      console.error("Error fetching mailpallet locations:", e);
      return []; // Return an empty array if no data found or error occurs
    }
  }

  async updateUserAddress(
    address: UserAddressModel,
  ): Promise<boolean | string> {
    try {
      // Call the API route using fetch with method 'PUT' (standard for updates)
      const res = await fetch("/api/address", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(address),
      });

      if (!res.ok) {
        const errorText = await res.text();
        return `Failed to update address: ${errorText}`;
      }

      // Optionally, parse the returned address data if needed
      // const updatedAddress = await res.json();

      return true;
    } catch (e: any) {
      console.error("Error updating user address:", e);
      return `Failed to update address: ${e.message || e}`;
    }
  }

  async createNewAddress(address: UserAddressModel): Promise<boolean | string> {
    try {
      const res = await fetch("/api/address", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(address),
      });

      if (res.status === 409) {
        return "Address already exists for this user";
      }

      if (!res.ok) {
        const errorText = await res.text();
        return `Failed to create new address: ${errorText}`;
      }

      // Optionally, parse the returned address data if needed
      // const createdAddress = await res.json();

      // Remove the data we stored in session storage
      sessionStorage.removeItem("id");
      sessionStorage.removeItem("first_name");
      sessionStorage.removeItem("last_name");
      sessionStorage.removeItem("email_address");

      return true;
    } catch (e: any) {
      console.error("Error creating new address:", e);
      return `Failed to create new address: ${e.message || e}`;
    }
  }
}
