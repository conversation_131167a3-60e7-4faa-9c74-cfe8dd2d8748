import { supabase } from "@/lib/supabase_client";
import { ParcelModel } from "@/data/models/parcel.model";
import { generateConsolidateParcelCode } from "@/lib/utils";
import { ShippingMethod } from "@/data/models/shipping_methods.model";
import {
  ConsolidationRequest,
  SupabaseConsolidationRequest,
} from "@/data/models/consolidation_requests.models";

export class ParcelService {
  public parcels: ParcelModel[] = [];

  async fetchParcels(
    country: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ parcels: ParcelModel[]; total: number }> {
    try {
      const params = new URLSearchParams();
      params.append("country", country);
      params.append("page", page.toString());
      params.append("limit", limit.toString());
      // userId is optional; if not provided, the endpoint will use the authenticated user

      const response = await fetch(`/api/parcels/fetch?${params.toString()}`);
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error fetching parcels via API:", errorText);
        throw new Error(errorText);
      }
      const result = await response.json();
      this.parcels = result.parcels || [];
      return { parcels: result.parcels || [], total: result.total || 0 };
    } catch (error) {
      console.error("Error fetching parcels:", error);
      return { parcels: [], total: 0 };
    }
  }

  isParcelInsurable(parcelId: number): boolean {
    return this.calculateTotalPrice(parcelId) <= 2500.0;
  }

  calculateDaysDifference(receivedDate: Date): number {
    // Ensure valid date
    if (!receivedDate || isNaN(receivedDate.getTime())) {
      console.log("Invalid received date:", receivedDate);
      return 0;
    }

    const today = new Date();

    const diffTime = Math.abs(today.getTime() - receivedDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(0, diffDays > 30 ? diffDays - 30 : 0); // First 30 days free
  }

  calculateParcelStorageFee(parcelId: number): number {
    const parcel = this.parcels.find((parcel) => parcel.id === parcelId);
    if (!parcel?.received_on) {
      console.log("No received_on date found");
      return 0;
    }

    const days = this.calculateDaysDifference(new Date(parcel.received_on));
    const fee = days * (parcel.storage_fee || 0);
    return fee;
  }

  async getShippingMethods(originCountry: string): Promise<ShippingMethod[]> {
    try {
      const params = new URLSearchParams();
      params.append("originCountry", originCountry);

      const response = await fetch(
        `/api/parcels/shipping-methods?${params.toString()}`,
      );
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error fetching shipping methods via API:", errorText);
        throw new Error(errorText);
      }
      const result = await response.json();
      return result.shippingMethods || [];
    } catch (error) {
      console.error("Unexpected error:", error);
      return [];
    }
  }

  calculateTotalPrice(parcelId: number): number {
    const parcel = this.parcels.find((parcel) => parcel.id === parcelId);
    if (parcel === undefined) return 0;
    else {
      const pricingModel = parcel.shipping_options?.filter(
        (o) => o.id === parcel.selected_shipping_method_id,
      )[0];
      return (
        (pricingModel?.shippingFee ?? 0) +
        (pricingModel?.consolidationFee ?? 0) +
        this.calculateParcelStorageFee(parcelId) +
        (pricingModel?.handlingFee ?? 0) +
        (parcel.dangerous_goods_price ?? 0) +
        (parcel.has_insurance ? (pricingModel?.insuranceFee ?? 0) : 0) +
        (parcel.duty ?? 0)
      );
    }
  }

  getParcelById(parcelId: number): ParcelModel | undefined {
    return this.parcels.find((parcel) => parcel.id === parcelId);
  }

  setSelectedShippingMethod(
    selectedShippingMethod: number,
    parcelId: number,
  ): void {
    const parcel = this.getParcelById(parcelId);
    if (parcel) {
      parcel.selected_shipping_method_id = selectedShippingMethod;
    }
  }

  setHasInsurance(hasInsurance: boolean, parcelId: number): void {
    const parcel = this.getParcelById(parcelId);
    if (parcel) {
      parcel.has_insurance = hasInsurance;
    }
  }

  async updateParcelItemValue(
    itemValue: number,
    parcelId: number,
  ): Promise<void> {
    try {
      const response = await fetch("/api/parcels/item-value", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ parcelId, itemValue }),
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error updating item value via API:", errorText);
        throw new Error(errorText);
      }
      // Optionally, you can use the returned data if needed:
      const result = await response.json();
      if (result.length === 0) {
        console.warn("No data returned from API (No rows were updated)");
      }
    } catch (error) {
      console.error("Unexpected error updating item value:", error);
      throw error;
    }
  }

  async addConsolidationRequest(
    parcelIds: number[],
    userId: number,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch("/api/parcels/consolidation-request", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ parcelIds, userId }),
      });
      const result = await response.json();
      if (!response.ok || !result.success) {
        console.error(
          "Error creating consolidation request via API:",
          result.error,
        );
        return { success: false, error: result.error || "Unknown error" };
      }
      return { success: true };
    } catch (error) {
      console.error("Unexpected error creating consolidation request:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      };
    }
  }

  async insertParcelData(parcelData: any, userUuid: string, userId: string) {
    try {
      const preparedData = await this.prepareParcelData(
        parcelData,
        userUuid,
        userId,
      );
      const response = await fetch("/api/parcels/insert", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ parcelData: preparedData, userUuid, userId }),
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Error inserting parcel data: ${errorText}`);
      }
      const result = await response.json();
      return result.data;
    } catch (error) {
      throw error;
    }
  }

  async fetchActiveRequests(): Promise<ConsolidationRequest[]> {
    try {
      const response = await fetch("/api/parcels/active-requests");
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error fetching active requests via API:", errorText);
        throw new Error(errorText);
      }
      const result = await response.json();
      return result.data || [];
    } catch (error) {
      console.error("Error in fetchActiveRequests:", error);
      throw error;
    }
  }

  async sendEmail(to: string, subject: string, html: string) {
    const response = await fetch("https://sendemail-bbwyvxdasa-uc.a.run.app", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        to,
        subject,
        html,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(
        data.error || `Failed to send email (${response.status})`,
      );
    }

    return data;
  }

  private async prepareParcelData(
    parcelData: any,
    userUuid: string,
    userId: string,
  ) {
    return {
      uuid: userUuid,
      user_id: userId,
      invoice: null,
      est_due_date: null,
      payment_date: null,
      delivered_date: null,
      payment_method: null,
      total_due: 0,
      items_value: 0.0,
      is_insured: false,
      is_payed_for: false,
      is_discounted: false,
      discount_amount: null,
      country: parcelData.originCountry,
      package_content: parcelData.packageContent,
      received_at: parcelData.receivedAt,
      received_on: parcelData.receivedOn,
      shipping_address: parcelData.shippingAddress,
      vendor_tracking: parcelData.vendorTracking,
      bought_from: parcelData.boughtFrom,
      status: parcelData.status,
      duty: parcelData.duty,
      is_dangerous_goods: parcelData.isDangerousGoods,
      dangerous_goods_price: parcelData.dangerousGoodsFee,
      tracking_id: parcelData.trackingId,
      dimensions: parcelData.dimensions,
      package_weight: parcelData.packageWeight,
      shipping_options: parcelData.shippingOptions,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  }

  async updateRequestStatus(
    requestCode: string,
    newStatus: number,
  ): Promise<void> {
    const { error } = await supabase
      .from("consolidation_parcel_requests")
      .update({ status: newStatus })
      .eq("code", requestCode);

    if (error) {
      throw new Error("Failed to update request status");
    }
  }

  async updateParcelConsolidationStatus(
    parcelId: number,
    isConsolidated: boolean,
  ): Promise<void> {
    if (!parcelId) {
      throw new Error("Missing parcel ID");
    }

    const { error } = await supabase
      .from("users_parcels_details")
      .update({ is_consolidated: isConsolidated })
      .eq("id", parcelId);

    if (error) {
      throw new Error(
        `Failed to update consolidation status for parcel ${parcelId}: ${error.message}`,
      );
    }
  }

  async uploadParcelPdf(parcelId: number, file: File): Promise<void> {
    try {
      // First, get the parcel details to extract user_id and tracking_id
      const { data: parcelData, error: fetchError } = await supabase
        .from("users_parcels_details")
        .select("user_id, tracking_id, uuid")
        .eq("id", parcelId)
        .single();

      if (fetchError || !parcelData) {
        throw new Error(
          `Failed to fetch parcel details: ${fetchError?.message || "Parcel not found"}`,
        );
      }

      // Generate filename: [user_id]_[tracking_id].pdf
      const fileName = `${parcelData.user_id}_${parcelData.tracking_id}.pdf`;

      // Upload file to Supabase storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from("parcel-docs")
        .upload(fileName, file, {
          cacheControl: "3600",
          upsert: true, // Replace if file already exists
        });

      if (uploadError) {
        throw new Error(`Failed to upload file: ${uploadError.message}`);
      }

      // Construct the authenticated URL for the private bucket
      const authenticatedUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/authenticated/parcel-docs/${fileName}`;

      // Update the parcel record with the PDF information
      const pdfInfo = {
        name: fileName,
        url: authenticatedUrl,
      };

      const { error: updateError } = await supabase
        .from("users_parcels_details")
        .update({ items_pdf: pdfInfo })
        .eq("id", parcelId);

      if (updateError) {
        // If database update fails, try to clean up the uploaded file
        await supabase.storage.from("parcel-docs").remove([fileName]);
        throw new Error(
          `Failed to update parcel record: ${updateError.message}`,
        );
      }

      console.log(
        `PDF uploaded successfully for parcel ${parcelId}: ${fileName}`,
      );
    } catch (error) {
      console.error("Error uploading parcel PDF:", error);
      throw error;
    }
  }

  async deleteParcelPdf(parcelId: number): Promise<void> {
    try {
      // First, get the current PDF information
      const { data: parcelData, error: fetchError } = await supabase
        .from("users_parcels_details")
        .select("items_pdf")
        .eq("id", parcelId)
        .single();

      if (fetchError) {
        throw new Error(
          `Failed to fetch parcel details: ${fetchError.message}`,
        );
      }

      // If there's a PDF associated with this parcel, delete it from storage
      if (parcelData?.items_pdf?.name) {
        const { error: deleteError } = await supabase.storage
          .from("parcel-docs")
          .remove([parcelData.items_pdf.name]);

        if (deleteError) {
          console.warn(
            `Failed to delete file from storage: ${deleteError.message}`,
          );
          // Continue with database update even if storage deletion fails
        }
      }

      // Update the parcel record to remove the PDF reference
      const { error: updateError } = await supabase
        .from("users_parcels_details")
        .update({ items_pdf: null })
        .eq("id", parcelId);

      if (updateError) {
        throw new Error(
          `Failed to update parcel record: ${updateError.message}`,
        );
      }

      console.log(`PDF deleted successfully for parcel ${parcelId}`);
    } catch (error) {
      console.error("Error deleting parcel PDF:", error);
      throw error;
    }
  }
}
