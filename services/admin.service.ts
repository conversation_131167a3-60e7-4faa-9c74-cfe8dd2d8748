import { User } from "@/data/models/user.model";
import { DashboardParcelData } from "@/data/models/dashboard.model";
import { supabase } from "@/lib/supabase_client";

export class AdminService {
  async getUserDetails(userUuid: string): Promise<User> {
    const res = await fetch(`/api/admin/users/${userUuid}`);
    if (!res.ok) {
      const errorText = await res.text();
      throw new Error(`Error fetching user details: ${errorText}`);
    }
    const data = await res.json();
    if (!data) throw new Error("User details not found");
    return data;
  }

  async getDashboardUserData() {
    const res = await fetch("/api/admin/dashboard/users");
    if (!res.ok) {
      const errorText = await res.text();
      throw new Error(`Error fetching users per country: ${errorText}`);
    }
    return await res.json();
  }

  // Function to fetch and shape parcel data
  async getDashboardParcelData(): Promise<DashboardParcelData | null> {
    const res = await fetch("/api/admin/dashboard/parcels");
    if (!res.ok) {
      const errorText = await res.text();
      throw new Error(`Error fetching dashboard parcel data: ${errorText}`);
    }
    return await res.json();
  }
}
