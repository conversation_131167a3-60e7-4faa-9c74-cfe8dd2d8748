import { ExchangeRate } from "@/data/models/exchange_rates.model";
import { supabase } from "@/lib/supabase_client";

export class ExchangeRateService {
  async getExchangeRateISO(
    currency_code: string,
  ): Promise<ExchangeRate | null> {
    try {
      const response = await fetch(
        `/api/exchange/rate?currency_code=${encodeURIComponent(currency_code)}`,
      );
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error fetching exchange rate via API:", errorText);
        throw new Error(errorText);
      }
      const result = await response.json();
      return result.data as ExchangeRate | null;
    } catch (error) {
      console.error("Error fetching exchange rate:", error);
      throw error;
    }
  }
}
