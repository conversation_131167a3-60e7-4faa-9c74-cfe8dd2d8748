import React, { useEffect, useMemo, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>it<PERSON>,
} from "@/components/ui/sheet";
import {
  Command,
  CommandInput,
  CommandList,
  CommandItem,
  CommandEmpty,
} from "@/components/ui/command";

import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";
import { useCurrency } from "@/components/app/CurrencyProvider";
import { country_currencies, currencyCodeToFlag } from "@/lib/constants";

type Props = {
  /**
   * Controls whether the mobile picker sheet is open.
   * Parent (CurrencyFab) should manage this state.
   */
  open: boolean;
  /**
   * Callback invoked when the sheet open state should change.
   */
  onOpenChange: (open: boolean) => void;
  /**
   * Optional list of currency codes to show as "popular" quick choices.
   */
  popular?: string[];
};

/**
 * MobileCurrencyPicker
 *
 * Mobile-first sheet + searchable list for selecting country / currency.
 * - Uses the app's CurrencyProvider to set the chosen currency.
 * - Designed to be opened by a floating action button on small screens.
 */
export default function MobileCurrencyPicker({
  open,
  onOpenChange,
  popular = ["GHS", "NGN", "ZAR", "GBP"],
}: Props) {
  const {
    currencyCode: activeCurrency,
    setCurrency,
    clearGeoipCountryUnsupported,
    isLoadingExchangeRate,
    displayCurrencyCode,
  } = useCurrency();

  const [query, setQuery] = useState<string>("");

  // Build the canonical list of currencies from the constants mapping.
  const currencies = useMemo(() => {
    return Object.keys(country_currencies).map((code) => ({
      code,
      country: country_currencies[code as keyof typeof country_currencies],
      flag: currencyCodeToFlag(code),
    }));
  }, []);

  // Simple filtered list (search by country name or currency code)
  const filtered = useMemo(() => {
    const q = query.trim().toLowerCase();
    if (!q) return currencies;
    return currencies.filter(
      (c) =>
        c.code.toLowerCase().includes(q) ||
        (c.country && c.country.toLowerCase().includes(q)),
    );
  }, [currencies, query]);

  // Popular list (preserve order and only include known codes)
  const popularList = useMemo(
    () => popular.filter((c) => currencies.some((x) => x.code === c)),
    [popular, currencies],
  );

  // When sheet closes, clear query to restore default state on reopen.
  useEffect(() => {
    if (!open) setQuery("");
  }, [open]);

  // Selection handler: set currency in provider and close sheet.
  const handleSelect = async (code: string) => {
    const normalized = (code || "GBP").toUpperCase();
    try {
      await setCurrency(normalized, "manual");
    } catch {
      // Best-effort: even if provider call fails, continue to close UI.
    } finally {
      try {
        clearGeoipCountryUnsupported();
      } catch {
        /* ignore */
      }
      onOpenChange(false);
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="bottom" className="p-0 min-h-screen">
        <SheetHeader className="sticky top-0 z-20 bg-background/95 backdrop-blur-sm border-b">
          <div className="flex items-center justify-between px-4 py-3">
            <div className="flex flex-col">
              <SheetTitle className="text-base">
                Choose country / currency
              </SheetTitle>
              <div className="text-xs text-muted-foreground mt-1">
                {isLoadingExchangeRate ? (
                  "Loading rates…"
                ) : (
                  <>
                    Showing:{" "}
                    <span className="font-medium">{displayCurrencyCode}</span>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
                aria-label="Close currency picker"
                className="p-2"
              >
                ✕
              </Button>
            </div>
          </div>

          <div className="px-4 pb-3" />
        </SheetHeader>

        <div className="px-4 pt-2 pb-6">
          {/* Search (kept outside sticky header so it doesn't overlap the popular row on small screens) */}
          <div className="mb-3">
            <Command className="w-full">
              <CommandInput
                value={query}
                onValueChange={setQuery}
                placeholder="Search country or currency..."
                autoFocus
              />
            </Command>
          </div>

          {/* Popular quick row */}
          {popularList.length > 0 && (
            <div className="mb-3">
              <div className="text-xs text-muted-foreground mb-2">Popular</div>
              <div className="flex gap-2 overflow-x-auto pb-2">
                {popularList.map((code) => {
                  const country =
                    country_currencies[
                      code as keyof typeof country_currencies
                    ] || code;
                  const flag = currencyCodeToFlag(code);
                  const isSelected = displayCurrencyCode === code;
                  return (
                    <button
                      key={code}
                      onClick={() => handleSelect(code)}
                      className={`flex-shrink-0 inline-flex items-center gap-3 px-3 py-2 border rounded-full text-sm ${
                        isSelected
                          ? "bg-blue-600 text-white border-blue-600"
                          : "bg-white"
                      }`}
                      aria-pressed={isSelected}
                      type="button"
                    >
                      <span className="text-lg">{flag}</span>
                      <div className="text-left">
                        <div className="font-medium leading-4">{country}</div>
                        <div className="text-[11px] text-muted-foreground">
                          {code}
                        </div>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          )}

          {/* Main list */}
          <div className="">
            <div className="h-full rounded-md border overflow-auto">
              <Command>
                <CommandList>
                  <CommandEmpty>
                    No matches for "<span className="font-medium">{query}</span>
                    "
                  </CommandEmpty>

                  <div className="p-1">
                    {filtered.map((c) => {
                      const { code, country, flag } = c;
                      const isSelected = displayCurrencyCode === code;
                      return (
                        <CommandItem
                          key={code}
                          onSelect={() => handleSelect(code)}
                          className="flex items-center justify-between py-3 px-2 hover:bg-muted rounded"
                        >
                          <div className="flex items-center gap-3">
                            <div className="text-2xl">{flag}</div>
                            <div className="text-left">
                              <div className="text-sm font-medium">
                                {country}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {code}
                              </div>
                            </div>
                          </div>
                          <div>
                            {isSelected && (
                              <Check className="w-4 h-4 text-blue-600" />
                            )}
                          </div>
                        </CommandItem>
                      );
                    })}
                  </div>
                </CommandList>
              </Command>
            </div>
          </div>

          <div className="mt-3 flex justify-end gap-2">
            <Button variant="ghost" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
