"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { loadStripe } from "@stripe/stripe-js";
import { Button } from "@/components/ui/button";
import { Loader2, Check as CheckIcon, X as XIcon } from "lucide-react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Elements, PaymentElement, useStripe, useElements } from "@stripe/react-stripe-js";

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

const API_URL = process.env.NEXT_PUBLIC_SUPABASE_FUNCTIONS_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

interface PaymentFormProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
  parcelId: number;
  trackingId: string;
  email: string;
  uuid: string;
  shippingMethod: string;
  isInsured: boolean;
  shipmentPrice: number;
  insuranceAmount: number;
  totalDue: number;
}

interface CheckoutFormProps {
  amount: number;
  onClose: () => void;
  parcelId: number;
  trackingId: string;
  email: string;
  uuid: string;
  shippingMethod: string;
  isInsured: boolean;
  shipmentPrice: number;
  insuranceAmount: number;
  totalDue: number;
  onSuccess: (receiptUrl: string) => void;
  onError: (message: string) => void;
  onDiscountApplied: (discountAmount: number) => void;
}

// The actual form component
function CheckoutForm({
  amount,
  onClose,
  parcelId,
  trackingId,
  email,
  uuid,
  shippingMethod,
  isInsured,
  shipmentPrice,
  insuranceAmount,
  totalDue,
  onSuccess,
  onError,
  onDiscountApplied,
}: CheckoutFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [discountCode, setDiscountCode] = useState("");
  const [discountError, setDiscountError] = useState("");
  const [discountAmount, setDiscountAmount] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [appliedDiscount, setAppliedDiscount] = useState(false);
  const [isApplyingDiscount, setIsApplyingDiscount] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    try {
      const currentAmount = totalDue - discountAmount;

      const { error: submitError } = await elements.submit();
      if (submitError) {
        onError(submitError.message ?? "An error occurred");
        return;
      }

      const response = await fetch(`${API_URL}/payment`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          email,
          parcelId,
          trackingId,
          amount: currentAmount,
          discount_code: discountCode,
          is_discounted: appliedDiscount,
          original_amount: totalDue.toString(),
          discount_amount: discountAmount.toString(),
        }),
      });

      const { clientSecret } = await response.json();

      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        clientSecret,
        redirect: "if_required",
      });

      if (error) {
        onError(error.message ?? "An error occurred");
      } else if (paymentIntent.status === "succeeded") {
        try {
          const fulfillmentResponse = await fetch(`${API_URL}/payment/update-payment-status`, {
            method: "PUT",
            headers: { 
              "Content-Type": "application/json",
              "Authorization": `Bearer ${SUPABASE_ANON_KEY}`,
            },
            body: JSON.stringify({
              trackingId,
              uuid,
              shippingMethod,
              isInsured,
              paymentIntentId: paymentIntent.id,
              shipmentPrice,
              insuranceAmount,
              totalDue,
            }),
          });

          const fulfillmentData = await fulfillmentResponse.json();
          
          if (!fulfillmentResponse.ok || !fulfillmentData.success) {
            throw new Error(fulfillmentData.error || "Failed to update status");
          }

          onSuccess(fulfillmentData.receiptUrl);
        } catch (error) {
          console.error("Post-payment Error:", error);
          onError(error instanceof Error ? error.message : "An unexpected error occurred");
        }
      }
    } catch (error) {
      console.error("Payment Error:", error);
      onError("An unexpected error occurred");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleApplyDiscount = async () => {
    setIsApplyingDiscount(true);
    setDiscountError("");

    try {
      const response = await fetch(`${API_URL}/validate-discount`, {
        method: "POST",
        headers: { 
          "Content-Type": "application/json",
          "Authorization": `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          code: discountCode.trim(),
          amount: totalDue,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to validate discount code");
      }

      if (data.success) {
        setAppliedDiscount(true);
        setDiscountCode(data.discountCode);
        setDiscountAmount(data.discountAmount);
        onDiscountApplied(data.discountAmount);
      } else {
        setDiscountError(data.error || "Invalid discount code");
      }
    } catch (error) {
      console.error("Discount application error:", error);
      setDiscountError(error instanceof Error ? error.message : "Error processing discount code");
    } finally {
      setIsApplyingDiscount(false);
    }
  };

  const getDisplayAmount = () => {
    if (appliedDiscount) {
      return (totalDue - discountAmount).toFixed(2);
    }
    return totalDue.toFixed(2);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <div className="flex gap-2">
          <Input
            type="text"
            placeholder="Discount Code"
            value={discountCode}
            onChange={(e) => setDiscountCode(e.target.value)}
            disabled={appliedDiscount || isProcessing}
            className="flex-1"
          />
          <Button type="button" variant="outline" onClick={handleApplyDiscount} disabled={!discountCode.trim() || appliedDiscount || isProcessing || isApplyingDiscount}>
            {isApplyingDiscount ? (
              <span className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Applying...
              </span>
            ) : (
              "Apply"
            )}
          </Button>
        </div>
        {discountError && <p className="text-sm text-red-500">{discountError}</p>}
        {appliedDiscount && <p className="text-sm text-green-500">Discount applied successfully! New total: £{getDisplayAmount()}</p>}
      </div>

      <PaymentElement />
      <div className="flex gap-4">
        <Button type="button" variant="outline" onClick={onClose} className="flex-1" disabled={isProcessing}>
          Cancel
        </Button>
        <Button type="submit" className="flex-1" disabled={isProcessing}>
          {isProcessing ? (
            <span className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              Processing...
            </span>
          ) : (
            `Pay £${getDisplayAmount()}`
          )}
        </Button>
      </div>
    </form>
  );
}

// The main payment modal component
export function StripePaymentForm({ isOpen, onClose, amount, parcelId, trackingId, email, uuid, shippingMethod, isInsured, shipmentPrice, insuranceAmount, totalDue }: PaymentFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [clientSecret, setClientSecret] = useState<string>("");
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [receiptUrl, setReceiptUrl] = useState<string>("");
  const [discountedAmount, setDiscountedAmount] = useState(amount);

  useEffect(() => {
    if (isOpen && amount) {
      setIsLoading(true);
      fetch(`${API_URL}/payment`, {
        method: "POST",
        headers: { 
          "Content-Type": "application/json",
          "Authorization": `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({ 
          amount, 
          parcelId, 
          trackingId,
          email 
        }),
      })
        .then((res) => res.json())
        .then((data) => {
          if (data.success) {
            setClientSecret(data.clientSecret);
          } else {
            throw new Error(data.error || "Failed to create payment intent");
          }
        })
        .catch((error) => {
          console.error("Payment Error:", error);
          setErrorMessage(error.message);
          setShowErrorDialog(true);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [isOpen, amount, parcelId, trackingId, email]);

  const options = {
    clientSecret,
    appearance: {
      theme: "stripe" as const,
    },
  };

  const handlePaymentSuccess = (receiptUrl: string) => {
    setReceiptUrl(receiptUrl);
    onClose();
    setShowSuccessDialog(true);
  };

  const handlePaymentError = (message: string) => {
    onClose();
    setErrorMessage(message);
    setShowErrorDialog(true);
  };

  const handleDiscountApplied = (discountAmount: number) => {
    setDiscountedAmount(totalDue - discountAmount);
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Complete Payment</DialogTitle>
          </DialogHeader>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            </div>
          ) : clientSecret ? (
            <Elements stripe={stripePromise} options={options}>
              <CheckoutForm
                amount={discountedAmount}
                onClose={onClose}
                parcelId={parcelId}
                trackingId={trackingId}
                email={email}
                uuid={uuid}
                isInsured={isInsured}
                shipmentPrice={shipmentPrice}
                insuranceAmount={insuranceAmount}
                totalDue={totalDue}
                shippingMethod={shippingMethod}
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
                onDiscountApplied={handleDiscountApplied}
              />
            </Elements>
          ) : null}
        </DialogContent>
      </Dialog>

      {/* Success Dialog */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Payment Successful!</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center gap-4 py-4">
            <div className="rounded-full bg-green-100 p-3">
              <CheckIcon className="h-6 w-6 text-green-600" />
            </div>
            <p className="text-center text-gray-600">Your payment has been processed successfully.</p>
            {receiptUrl && (
              <a href={receiptUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">
                View Receipt
              </a>
            )}
          </div>
          <Button
            onClick={() => {
              setShowSuccessDialog(false);
              router.refresh();
              window.location.reload();
            }}
          >
            Continue
          </Button>
        </DialogContent>
      </Dialog>

      {/* Error Dialog */}
      <Dialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Payment Failed</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center gap-4 py-4">
            <div className="rounded-full bg-red-100 p-3">
              <XIcon className="h-6 w-6 text-red-600" />
            </div>
            <p className="text-center text-gray-600">{errorMessage || "There was an error processing your payment."}</p>
          </div>
          <Button onClick={() => setShowErrorDialog(false)} variant="destructive">
            Close
          </Button>
        </DialogContent>
      </Dialog>
    </>
  );
}
