import { ExternalLink, Package2 } from "lucide-react";
import { ParcelModel } from "@/data/models/parcel.model";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface PackageInfoCardProps {
  parcelModel: ParcelModel;
}

export function PackageInfoCard({ parcelModel }: PackageInfoCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Package2 className="w-5 h-5 mr-2" />
          Package Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <p>
          <strong>Tracking ID:</strong> {parcelModel.tracking_id}
        </p>
        <p>
          <strong>Dimensions:</strong> {parcelModel.dimensions}
        </p>
        <p>
          <strong>Weight:</strong> {parcelModel.package_weight} kg
        </p>
        <p>
          <strong>Content:</strong> {parcelModel.package_content}
        </p>
        <p>
          {/* <strong>Value:</strong> £{parcelModel.items_value?.toFixed(2)} */}
        </p>
        <p>
          <strong>Third Party Tracking: </strong>{parcelModel.third_party_tracking === "-" ? (
            <span> {parcelModel.third_party_tracking}</span>
          ) : (
            <a href={parcelModel.third_party_tracking} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">
              External Tracking
            </a>
          )}
        </p>
      </CardContent>
    </Card>
  );
}
