import { Truck } from "lucide-react";
import { formatDate } from "@/lib/utils";
import { ParcelModel } from "@/data/models/parcel.model";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ShippingInfoCardProps {
  parcelModel: ParcelModel;
}

export function ShippingInfoCard({ parcelModel }: ShippingInfoCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Truck className="w-5 h-5 mr-2" />
          Shipping Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <p>
          <strong>Received At:</strong> {parcelModel.received_at}
        </p>
        <p>
          <strong>Received On:</strong> {formatDate(new Date(parcelModel.received_on ?? "")) ?? "N/A"}
        </p>
        <p>
          <strong>Address:</strong> {parcelModel.shipping_address}
        </p>
        <p>
          <strong>Vendor Tracking:</strong> {parcelModel.vendor_tracking}
        </p>
        <p>
          <strong>Delivered:</strong> {formatDate(new Date(parcelModel.delivered_date ?? "")) ?? "-"}
        </p>
      </CardContent>
    </Card>
  );
}
