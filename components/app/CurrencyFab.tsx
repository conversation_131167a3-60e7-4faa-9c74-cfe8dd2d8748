"use client";

import React, { useMemo, useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Check } from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { country_currencies, currencyCodeToFlag } from "@/lib/constants";
import { useCurrency } from "@/components/app/CurrencyProvider";
import DesktopCurrencyDialog from "@/components/app/DesktopCurrencyDialog";
import MobileCurrencyPicker from "@/components/app/MobileCurrencyPicker";

/**
 * CurrencyFab
 *
 * Renders a desktop dialog trigger on larger viewports and a mobile sheet FAB on
 * small viewports. The desktop and mobile pickers are exported as separate
 * components and implemented in `components/app/pickers`.
 *
 * Visibility:
 * - Hidden while auth is loading.
 * - Hidden for authenticated users unless `geoipCountryUnsupported` is true,
 *   in which case the picker is available so the user can select a currency.
 */

/* Session storage keys (match existing usage in the app) */
const STORAGE_KEY = "mp_currency";
const STORAGE_SOURCE = "mp_currency_source";

export default function CurrencyFab() {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const {
    geoipCountryUnsupported,
    isLoadingExchangeRate,
    displayCurrencyCode,
  } = useCurrency();

  // Detect mobile viewport
  const [isMobile, setIsMobile] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false);

  useEffect(() => {
    if (typeof window === "undefined") return;
    const mq = window.matchMedia("(max-width: 768px)");
    const update = () => setIsMobile(!!mq.matches);
    update();
    mq.addEventListener("change", update);
    return () => mq.removeEventListener("change", update);
  }, []);

  // Auto-open mobile picker when geoip indicates unsupported country
  useEffect(() => {
    if (geoipCountryUnsupported && isMobile) {
      setMobileOpen(true);
    }
  }, [geoipCountryUnsupported, isMobile]);

  // Maintain previous visibility behavior
  if (authLoading) return null;
  if (isAuthenticated && !geoipCountryUnsupported) return null;

  return (
    <>
      {/* Desktop: DesktopCurrencyDialog includes its own trigger */}
      {!isMobile ? (
        <div className="fixed right-5 bottom-6 z-50">
          <DesktopCurrencyDialog />
        </div>
      ) : (
        /* Mobile FAB + sheet picker */
        <div className="fixed right-5 bottom-6 z-50">
          <button
            aria-label="Choose currency / country"
            className="bg-blue-600 hover:bg-blue-700 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400"
            onClick={() => setMobileOpen(true)}
            title="Choose currency / country"
            type="button"
          >
            {/* Show current display flag while rates are stable; show skeleton while loading */}
            {isLoadingExchangeRate ? (
              <Skeleton className="w-6 h-6" />
            ) : (
              <span className="text-xl">
                {currencyCodeToFlag(displayCurrencyCode)}
              </span>
            )}
          </button>

          <MobileCurrencyPicker
            open={mobileOpen}
            onOpenChange={setMobileOpen}
          />
        </div>
      )}
    </>
  );
}
