import { Info } from "lucide-react";
import { convertToPascalCasing } from "@/lib/utils";
import { ParcelModel } from "@/data/models/parcel.model";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ShippingMethodWithFees } from "@/data/models/shipping_methods.model";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ShippingOptionsCardProps {
  parcelModel: ParcelModel;
  shippingOptions: ShippingMethodWithFees[];
  selectedShippingMethodId: number;
  shippingError: string;
  onShippingMethodChange: (value: string) => void;
}

export function ShippingOptionsCard({ parcelModel, shippingOptions, selectedShippingMethodId, shippingError, onShippingMethodChange }: ShippingOptionsCardProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Shipping Options</CardTitle>
          {parcelModel.package_weight! < 20 && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground bg-muted px-2 py-1 rounded-md">
              <Info className="h-3 w-3" />
              <span>Some shipping options require parcels with a weight of 20kg or more</span>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <RadioGroup className="flex gap-6" value={selectedShippingMethodId.toString()} onValueChange={onShippingMethodChange} disabled={parcelModel.is_payed_for}>
          {shippingOptions.map((option) => (
            <div className="flex items-center" key={option.id}>
              <RadioGroupItem value={option.id.toString()} id={option.id.toString()} disabled={parcelModel.is_payed_for} />
              <label htmlFor={option.id.toString()} className={`ml-2 ${parcelModel.is_payed_for ? "text-gray-500" : ""}`}>
                {convertToPascalCasing(option.method_name)}
              </label>
            </div>
          ))}
        </RadioGroup>
        {shippingError && <p className="text-sm text-red-500 mt-2">{shippingError}</p>}
      </CardContent>
    </Card>
  );
}
