import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { supabase } from "@/lib/supabase_client";
import { Card, CardContent } from "@/components/ui/card";
import { Plane, Ship, Package2, Calculator, Ruler } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface ShippingMethod {
  id: number;
  method_name: string;
  origin_country: string;
  min_weight: number;
  handling_fee: number;
  dim_divisor: number;
}

interface ShippingRoute {
  id: number;
  origin_country: string;
  destination_country: string;
  is_active: boolean;
  base_price: number;
  shipping_method_id: number;
  method: ShippingMethod;
}

interface CostBreakdown {
  dimensionalWeight: number;
  chargeableWeight: number;
  baseShippingCost: number;
  handlingFee: number;
  total: number;
  dimensions: {
    length: string;
    width: string;
    height: string;
  };
}

export default function ShippingCalculator() {
  const [weight, setWeight] = useState<string>("");
  const [length, setLength] = useState<string>("");
  const [width, setWidth] = useState<string>("");
  const [height, setHeight] = useState<string>("");
  const [costBreakdown, setCostBreakdown] = useState<CostBreakdown | null>(null);
  const [destinations, setDestinations] = useState<string[]>([]);
  const [originCountries, setOriginCountries] = useState<string[]>([]);
  const [shippingMethods, setShippingMethods] = useState<ShippingMethod[]>([]);
  const [selectedOrigin, setSelectedOrigin] = useState<string>("");
  const [selectedDestination, setSelectedDestination] = useState<string>("");
  const [selectedMethod, setSelectedMethod] = useState<string>("");
  const [availableRoutes, setAvailableRoutes] = useState<ShippingRoute[]>([]);

  // Fetch available destinations
  const fetchDestinations = async () => {
    const { data, error } = await supabase.from("mp_shipping_routes").select("origin_country, destination_country").eq("is_active", true);

    if (error) {
      console.error("Error fetching destinations:", error);
      return;
    }

    if (data) {
      // const uniqueOrigins = Array.from(new Set(data.map((d) => d.origin_country)));
      const uniqueDestinations = Array.from(new Set(data.map((d) => d.destination_country)));
      //Uncomment below line once the US and other Origin prices are set.
      // setOriginCountries(uniqueOrigins);
      setOriginCountries(["UK"]);
      setDestinations(uniqueDestinations);
    }
  };

  // Fetch shipping methods and routes for selected origin and destination
  const fetchShippingMethods = async (origin: string, destination: string) => {
    const { data: routes, error } = await supabase
      .from("mp_shipping_routes")
      .select(`*, method:mp_shipping_methods(*)`)
      .eq("origin_country", origin)
      .eq("destination_country", destination)
      .eq("is_active", true);

    if (error) {
      console.error("Error fetching shipping methods:", error);
      return;
    }

    if (routes) {
      setAvailableRoutes(routes as ShippingRoute[]);
      setShippingMethods(routes.map((route) => route.method) as ShippingMethod[]);
    }
  };

  useEffect(() => {
    fetchDestinations();
  }, []);

  useEffect(() => {
    if (selectedOrigin && selectedDestination) {
      fetchShippingMethods(selectedOrigin, selectedDestination);
    }
  }, [selectedOrigin, selectedDestination]);

  const calculateDimensionalWeight = (l: number, w: number, h: number, divisor: number): number => {
    return (l * w * h) / divisor;
  };

  const handleEstimate = () => {
    if (!validateInputs()) return;

    const selectedRoute = availableRoutes.find((route) => route.shipping_method_id.toString() === selectedMethod);
    const method = shippingMethods.find((m) => m.id.toString() === selectedMethod);

    if (!selectedRoute || !method) return;

    // Calculate dimensional weight
    const dimensionalWeight = calculateDimensionalWeight(parseFloat(length), parseFloat(width), parseFloat(height), method.dim_divisor);

    // Determine chargeable weight
    const actualWeight = parseFloat(weight);
    const chargeableWeight = Math.max(actualWeight, dimensionalWeight);

    // Calculate base shipping cost
    const baseShippingCost = chargeableWeight * selectedRoute.base_price;

    // Calculate total cost
    const totalCost = baseShippingCost + method.handling_fee;

    setCostBreakdown({
      dimensionalWeight,
      chargeableWeight,
      baseShippingCost,
      handlingFee: method.handling_fee,
      total: totalCost,
      dimensions: { length, width, height },
    });
  };

  const validateInputs = () => {
    if (!selectedOrigin || !selectedDestination || !selectedMethod || !weight || !length || !width || !height) {
      alert("Please fill in all fields including dimensions");
      return false;
    }

    const method = shippingMethods.find((m) => m.id.toString() === selectedMethod);
    if (method && parseFloat(weight) < method.min_weight) {
      alert(`Minimum weight requirement for this method is ${method.min_weight}kg`);
      return false;
    }

    return true;
  };

  return (
    <div className="max-w-4xl mx-auto">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
        <Card className="bg-white shadow-xl rounded-xl overflow-hidden">
          <div className="bg-gradient-to-r from-mp-primary to-blue-600 p-6">
            <h2 className="text-2xl font-bold text-black text-center flex items-center justify-center gap-2">
              <Calculator className="w-6 h-6" />
              Shipping Cost Calculator
            </h2>
          </div>

          <CardContent className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* From Location */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">From</Label>
                <Select
                  onValueChange={(value) => {
                    setSelectedOrigin(value);
                    setSelectedMethod("");
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select location" />
                  </SelectTrigger>
                  <SelectContent>
                    {originCountries.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country.toUpperCase()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* To Location */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">To</Label>
                <Select onValueChange={setSelectedDestination}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select destination" />
                  </SelectTrigger>
                  <SelectContent>
                    {destinations.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country.toUpperCase()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Shipping Method */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Shipping Method</Label>
                <Select disabled={!selectedOrigin || !selectedDestination} onValueChange={setSelectedMethod}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select method" />
                  </SelectTrigger>
                  <SelectContent>
                    {shippingMethods.map((method) => (
                      <SelectItem key={method.id} value={method.id.toString()}>
                        {method.method_name
                          .split("_")
                          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                          .join(" ")}
                        {` (Min Weight - ${method.min_weight}kg)`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Weight Input */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Weight (kg)</Label>
                <div className="relative">
                  <Package2 className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input type="number" value={weight} onChange={(e) => setWeight(e.target.value)} className="pl-10" placeholder="Enter weight in kg" min="0" step="0.1" />
                </div>
              </div>

              {/* Dimensions Section */}
              <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Length */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Length (cm)</Label>
                  <div className="relative">
                    <Ruler className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input type="number" value={length} onChange={(e) => setLength(e.target.value)} className="pl-10" placeholder="Length" min="0" step="0.1" />
                  </div>
                </div>

                {/* Width */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Width (cm)</Label>
                  <div className="relative">
                    <Ruler className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 rotate-90" />
                    <Input type="number" value={width} onChange={(e) => setWidth(e.target.value)} className="pl-10" placeholder="Width" min="0" step="0.1" />
                  </div>
                </div>

                {/* Height */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Height (cm)</Label>
                  <div className="relative">
                    <Ruler className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input type="number" value={height} onChange={(e) => setHeight(e.target.value)} className="pl-10" placeholder="Height" min="0" step="0.1" />
                  </div>
                </div>
              </div>
            </div>

            {/* Calculate Button */}
            <div className="flex justify-center pt-4">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleEstimate}
                className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
              >
                <Calculator className="w-5 h-5" />
                Calculate Cost
              </motion.button>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {costBreakdown && (
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5, delay: 0.2 }} className="mt-8">
          <Card className="bg-white shadow-xl rounded-xl overflow-hidden">
            <div className="bg-gradient-to-r from-green-500 to-green-600 p-4">
              <h3 className="text-xl font-bold text-white text-center">Cost Breakdown</h3>
            </div>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-gray-600">Dimensions (L × W × H)</span>
                  <span className="font-semibold">
                    {costBreakdown.dimensions.length} × {costBreakdown.dimensions.width} × {costBreakdown.dimensions.height} cm
                  </span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-gray-600">Dimensional Weight</span>
                  <span className="font-semibold">{costBreakdown.dimensionalWeight.toFixed(2)} kg</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-gray-600">Chargeable Weight</span>
                  <span className="font-semibold">{costBreakdown.chargeableWeight.toFixed(2)} kg</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-gray-600">Base Shipping Cost</span>
                  <span className="font-semibold">£{costBreakdown.baseShippingCost.toFixed(2)}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-gray-600">Handling Fee</span>
                  <span className="font-semibold">£{costBreakdown.handlingFee.toFixed(2)}</span>
                </div>

                <div className="flex justify-between items-center py-3 bg-gray-50 rounded-lg px-4 mt-4">
                  <span className="font-bold text-gray-700">Total Cost</span>
                  <span className="font-bold text-mp-primary text-xl">£{costBreakdown.total.toFixed(2)}</span>
                </div>
              </div>

              <div className="mt-6 bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-lg">
                <div className="text-sm text-blue-700">
                  <span className="font-semibold block mb-2">Please Note:</span>
                  <ul className="list-disc ml-5">
                    <li>The chargeable weight is the greater of actual weight and dimensional weight.</li>
                    <li>Import duties and taxes are not included in the calculation.</li>
                    <li>Dangerous goods fee are not included in the calculation.</li>
                    <li>Storage of your items for the first month is free. Charges apply after that.</li>
                    <li>Final costs may be subject to change based on actual weight and dimensions verification.</li>
                  </ul>
                </div>
              </div>

              <div className="mt-4 bg-amber-50 border-l-4 border-amber-500 p-4 rounded-r-lg">
                <div className="text-sm text-amber-700">
                  <span className="font-semibold block mb-2">Shipping Information:</span>
                  <ul className="list-disc ml-5">
                    <li>Consolidated Air Shipping usually takes between 3 to 4 weeks. This is due to the time taken to consolidate multiple parcels.</li>
                    <li>Air Freight delivers within 5 to 7 business days.</li>
                    <li>Sea Freight can take up to 8 weeks.</li>
                    <li>Priority Shipping is available, with delivery in 1 to 5 working days.</li>
                    <li>Please be aware that delivery times may vary during festive periods.</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
