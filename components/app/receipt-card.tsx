import { Receipt, ExternalLink } from "lucide-react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

interface ReceiptCardProps {
  invoice: string;
}

export function ReceiptCard({ invoice }: ReceiptCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Receipt className="w-5 h-5 mr-2" />
          Receipt
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <span>Payment Receipt</span>
          <a href={invoice} target="_blank" rel="noopener noreferrer" className="flex items-center text-blue-600 hover:text-blue-800 underline">
            View Receipt
            <ExternalLink className="w-4 h-4 ml-1" />
          </a>
        </div>
      </CardContent>
    </Card>
  );
}
