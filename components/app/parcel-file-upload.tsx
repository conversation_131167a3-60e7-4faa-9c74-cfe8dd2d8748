"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ParcelModel } from "@/data/models/parcel.model";
import { ParcelService } from "@/services/parcel.service";
import { useToast } from "@/hooks/use-toast";
import { Upload, FileText, Trash2, Loader2 } from "lucide-react";
import { supabase } from "@/lib/supabase_client";

interface ParcelFileUploadProps {
  parcelModel: ParcelModel;
  userEmail: string;
  onUploadSuccess?: () => void;
}

export default function ParcelFileUpload({ parcelModel, userEmail, onUploadSuccess }: ParcelFileUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();
  const parcelService = new ParcelService();

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Check file type
      if (file.type !== "application/pdf") {
        toast({
          title: "Invalid file type",
          description: "Please select a PDF file.",
          variant: "destructive",
        });
        return;
      }

      // Check file size (5MB limit)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        toast({
          title: "File too large",
          description: "Please select a PDF file smaller than 5MB.",
          variant: "destructive",
        });
        return;
      }

      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !parcelModel.id) {
      toast({
        title: "Error",
        description: "Please select a file and ensure parcel ID is available.",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    try {
      await parcelService.uploadParcelPdf(parcelModel.id, selectedFile);
      toast({
        title: "Success",
        description: "PDF uploaded successfully!",
      });
      setSelectedFile(null);
      // Reset the file input
      const fileInput = document.getElementById(`file-input-${parcelModel.id}`) as HTMLInputElement;
      if (fileInput) {
        fileInput.value = "";
      }
      onUploadSuccess?.();
    } catch (error) {
      console.error("Upload error:", error);
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Failed to upload PDF. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleDelete = async () => {
    if (!parcelModel.id) {
      toast({
        title: "Error",
        description: "Parcel ID is not available.",
        variant: "destructive",
      });
      return;
    }

    setIsDeleting(true);
    try {
      await parcelService.deleteParcelPdf(parcelModel.id);
      toast({
        title: "Success",
        description: "PDF deleted successfully!",
      });
      onUploadSuccess?.();
    } catch (error) {
      console.error("Delete error:", error);
      toast({
        title: "Delete failed",
        description: error instanceof Error ? error.message : "Failed to delete PDF. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleViewPdf = async () => {
    if (!parcelModel.items_pdf?.url) {
      toast({
        title: "Error",
        description: "PDF URL is not available.",
        variant: "destructive",
      });
      return;
    }
    const { data, error } = await supabase.storage
      .from("parcel-docs")
      .createSignedUrl(parcelModel.items_pdf.name, 60 * 60); // 1 hour expiry

    if (error) {
      console.error("Error creating signed URL:", error);
      toast({
        title: "Error",
        description: "Failed to generate PDF link. Please try again.",
        variant: "destructive",
      });
      return;
    }
    const pdfUrl = data?.signedUrl;
    if (pdfUrl) {
      window.open(pdfUrl, "_blank");
    }
    else {
      toast({
        title: "Error",
        description: "Failed to retrieve PDF URL. Please try again.",
        variant: "destructive",
      });
    }
  };

  const hasExistingPdf = parcelModel.items_pdf && parcelModel.items_pdf.url;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Invoice/Items PDF
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {hasExistingPdf ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">
                  {parcelModel.items_pdf?.name || "PDF uploaded"}
                </span>
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleViewPdf}
                >
                  View
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={handleDelete}
                  disabled={isDeleting || parcelModel.status === "Shipped"}
                >
                  {isDeleting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor={`file-input-${parcelModel.id}`}>
                Upload Invoice/Items PDF (Max 5MB)
              </Label>
              <Input
                id={`file-input-${parcelModel.id}`}
                type="file"
                accept=".pdf"
                onChange={handleFileSelect}
                className="cursor-pointer"
              />
            </div>

            {selectedFile && (
              <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">
                    {selectedFile.name}
                  </span>
                  <span className="text-xs text-blue-600">
                    ({(selectedFile.size / (1024 * 1024)).toFixed(2)} MB)
                  </span>
                </div>
                <Button
                  size="sm"
                  onClick={handleUpload}
                  disabled={isUploading}
                  className="ml-2"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload
                    </>
                  )}
                </Button>
              </div>
            )}

            <div className="text-sm text-gray-600">
              <p>Please upload a PDF containing the invoice or list of items for this parcel.</p>
              <p className="text-xs mt-1">Supported format: PDF (max 5MB)</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}