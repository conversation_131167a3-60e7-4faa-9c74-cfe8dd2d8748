import { AlertCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import { ParcelModel } from "@/data/models/parcel.model";

interface InsuranceAndPaymentCardProps {
  parcelModel: ParcelModel;
  selectedPriorityShipping: boolean;
  insuranceAmount: number;
  hasInsurance: boolean;
  isConsolidationRequested: boolean;
  onInsuranceChange: (checked: boolean) => void;
  onPaymentClick: () => void;
}

export function InsuranceAndPaymentCard({
  parcelModel,
  selectedPriorityShipping,
  insuranceAmount,
  hasInsurance,
  isConsolidationRequested,
  onInsuranceChange,
  onPaymentClick,
}: InsuranceAndPaymentCardProps) {
  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex flex-col gap-4">
          {!parcelModel.is_payed_for && !selectedPriorityShipping && insuranceAmount > 0 && (
            <div className="flex items-center gap-2 text-sm text-amber-600 bg-amber-50 border border-amber-200 p-3 rounded-md">
              <AlertCircle className="h-4 w-4 flex-shrink-0" />
              <p>Items that are more than £2,000 will be insured at a cap amount of £2,000</p>
            </div>
          )}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {insuranceAmount > 0 && (
                <>
                  <Checkbox id="add-insurance" checked={hasInsurance} disabled={parcelModel.is_payed_for} onCheckedChange={(checked) => onInsuranceChange(checked as boolean)} />
                  <label htmlFor="add-insurance" className={`${parcelModel.is_payed_for ? "text-gray-500" : ""}`}>
                    Add Insurance
                    {parcelModel.is_payed_for && <span className="ml-2 text-sm text-gray-400">(Already paid)</span>}
                  </label>
                </>
              )}
            </div>
            <Button className="w-1/3" onClick={onPaymentClick} disabled={parcelModel.is_payed_for || isConsolidationRequested}>
              Pay Now
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
