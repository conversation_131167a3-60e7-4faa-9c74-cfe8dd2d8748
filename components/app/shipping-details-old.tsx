import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useState, useEffect, useRef } from "react";
import { ParcelModel } from "@/data/models/parcel.model";
import { ParcelService } from "@/services/parcel.service";
import { convertToPascalCasing, formatDate } from "@/lib/utils";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { StripePaymentForm } from "@/components/app/stripe-payment-form";
import { ShippingMethodWithFees } from "@/data/models/shipping_methods.model";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Package2, Truck, DollarSign, ShoppingBag, Receipt, ExternalLink, AlertCircle, AlertTriangle, Info } from "lucide-react";

export default function ShippingDetails(props: { parcelModel: ParcelModel; userEmail: string; }) {
  const { parcelModel, userEmail } = props;
  const parcelServiceRef = useRef(new ParcelService());

  const selectedShippingMethod = parcelModel.shipping_options?.filter((o) => o.id === parcelModel.selected_shipping_method_id)[0];

  const [totalPrice, setTotalPrice] = useState(0);
  const [storageFee, setStorageFee] = useState(0);
  const [shippingError, setShippingError] = useState<string>("");
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [dangerousGoodsPrice, setDangerousGoodsPrice] = useState<number>(0);
  const [shippingOptions, setShippingOptions] = useState<ShippingMethodWithFees[]>([]);
  const [selectedPriorityShipping, setSelectedPriorityShipping] = useState(false);
  const [destinationCountry, setDestinationCountry] = useState<string | null>(null);
  const [insuranceAmount, setInsuranceAmount] = useState(selectedShippingMethod?.insuranceFee ?? 0);
  const [shipmentPrice, setShipmentPrice] = useState<number>(selectedShippingMethod?.shippingFee ?? 0);
  const [hasInsurance, setHasInsurance] = useState((parcelModel.is_payed_for && parcelModel.is_insured) || false);
  const [selectedShippingMethodId, setSelectedShippingMethodId] = useState<number>(parcelModel.selected_shipping_method_id ?? 0);

  const getCountry = (): string | null => {
    const shippingAddress = parcelModel.shipping_address;
    if (shippingAddress != null) {
      const addressParts = shippingAddress.split(",");
      return addressParts[addressParts.length - 1].trim();
    }
    return null;
  };

  const getShippingOptions = () => {
    try {
      setShippingOptions(parcelModel.shipping_options ?? []);
    } catch (error) {
      console.error("Error loading shipping options:", error);
    }
  };

  const updateTotalPrice = () => {
    try {
      const newTotalPrice = parcelServiceRef.current.calculateTotalPrice(parcelModel.id ?? 0);
      setTotalPrice(newTotalPrice);
    } catch (error) {
      console.error("Error calculating total price:", error);
    }
  };

  const handleInsuranceChange = async (checked: boolean) => {
    try {
      setHasInsurance(checked);
      parcelServiceRef.current.setHasInsurance(checked, parcelModel.id ?? 0);
      updateTotalPrice();
    } catch (error) {
      console.error("Error calculating insurance:", error);
    }
  };

  const handlePayment = () => {
    if (!selectedShippingMethodId) {
      setShippingError("Please select a shipping option before proceeding");
      return;
    }
    setShippingError("");
    setIsPaymentModalOpen(true);
  };

  const handleShippingMethodChange = async (value: string) => {
    if (parcelModel.is_payed_for) return;

    const methodId = parseInt(value, 10);
    setSelectedShippingMethodId(methodId);
    parcelModel.selected_shipping_method_id = methodId;
    setShippingError("");

    // Update insurance amount based on the new shipping method
    const selectedMethod = shippingOptions.find(option => option.id === methodId);
    setInsuranceAmount(selectedMethod?.insuranceFee ?? 0);

    try {
      parcelServiceRef.current.setSelectedShippingMethod(methodId, parcelModel.id ?? 0);
      updateTotalPrice();
    } catch (error) {
      console.error("Error updating shipping method:", error);
      setShippingError("Failed to update shipping method. Please try again.");
    }
  };

  useEffect(() => {
    parcelServiceRef.current.parcels = [parcelModel];
    
    if (parcelModel && parcelModel.id) {
      getShippingOptions();
      setDestinationCountry(getCountry());
      
      const calcStorageFee = parcelServiceRef.current.calculateParcelStorageFee(parcelModel.id ?? 0);
      setStorageFee(calcStorageFee);

      if (parcelModel.is_dangerous_goods) {
        setDangerousGoodsPrice(parcelModel.dangerous_goods_price ?? 0);
      }

      setSelectedShippingMethodId(parcelModel.selected_shipping_method_id ?? 0);
    }
  }, [parcelModel]);

  useEffect(() => {
    if (parcelModel.is_payed_for && parcelModel.is_insured) {
      setInsuranceAmount(selectedShippingMethod?.insuranceFee ?? 0);
      updateTotalPrice();
    }
  }, [parcelModel.is_payed_for, parcelModel.is_insured]);

  const isConsolidationRequested = parcelModel.consolidation_status !== undefined;

  const formattedTotalPrice = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'GBP',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(totalPrice);

  return (
    <div className="space-y-6">
      {parcelModel.is_dangerous_goods && (
        <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <AlertTriangle className="h-5 w-5 text-yellow-600" />
          <span className="text-yellow-800">This parcel contains dangerous goods and requires special handling</span>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Package Information Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package2 className="w-5 h-5 mr-2" />
              Package Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p>
              <strong>Tracking ID:</strong> {parcelModel.tracking_id}
            </p>
            <p>
              <strong>Dimensions:</strong> {parcelModel.dimensions}
            </p>
            <p>
              <strong>Weight:</strong> {parcelModel.package_weight} kg
            </p>
            <p>
              <strong>Content:</strong> {parcelModel.package_content}
            </p>
            <p>
              <strong>Value:</strong> £{parcelModel.items_value?.toFixed(2)}
            </p>
          </CardContent>
        </Card>

        {/* Shipping Information Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="w-5 h-5 mr-2" />
              Shipping Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p>
              <strong>Received At:</strong> {parcelModel.received_at}
            </p>
            <p>
              <strong>Received On:</strong> {formatDate(new Date(parcelModel.received_on ?? "")) ?? "N/A"}
            </p>
            <p>
              <strong>Address:</strong> {parcelModel.shipping_address}
            </p>
            <p>
              <strong>Vendor Tracking:</strong> {parcelModel.vendor_tracking}
            </p>
            <p>
              <strong>Delivered:</strong> {formatDate(new Date(parcelModel.delivered_date ?? "")) ?? "-"}
            </p>
          </CardContent>
        </Card>

        {/* Order Information Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ShoppingBag className="w-5 h-5 mr-2" />
              Order Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p>
              <strong>Bought From:</strong> {parcelModel.bought_from}
            </p>
            <p>
              <strong>Est. Due Date:</strong> {formatDate(new Date(parcelModel.est_due_date ?? "")) ?? "Pending"}
            </p>
            <p>
              <strong>Status:</strong> {parcelModel.status ?? "N/A"}
            </p>
            <p>
              <strong>Duty:</strong> £{parcelModel.duty?.toFixed(2)}
            </p>
            <p>
              <strong>Payment Date:</strong> {formatDate(new Date(parcelModel.payment_date ?? "")) ?? "Pending"}
            </p>
            <p>
              <strong>Payment Method:</strong> {parcelModel.payment_method ?? "N/A"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Shipping Options Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Shipping Options</CardTitle>
            {parcelModel.package_weight! < 20 && (
              <div className="flex items-center gap-2 text-xs text-muted-foreground bg-muted px-2 py-1 rounded-md">
                <Info className="h-3 w-3" />
                <span>Some shipping options require parcels with a weight of 20kg or more</span>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <RadioGroup 
            className="flex gap-6" 
            value={selectedShippingMethodId.toString()} 
            onValueChange={handleShippingMethodChange} 
            disabled={parcelModel.is_payed_for}
          >
            {shippingOptions.map((option) => (
              <div className="flex items-center" key={option.id}>
                <RadioGroupItem 
                  value={option.id.toString()} 
                  id={option.id.toString()} 
                  disabled={parcelModel.is_payed_for}
                />
                <label 
                  htmlFor={option.id.toString()} 
                  className={`ml-2 ${parcelModel.is_payed_for ? "text-gray-500" : ""}`}
                >
                  {convertToPascalCasing(option.method_name)}
                </label>
              </div>
            ))}
          </RadioGroup>
          {shippingError && <p className="text-sm text-red-500 mt-2">{shippingError}</p>}
        </CardContent>
      </Card>

      {/* Receipt Card - Only show if there's an invoice */}
      {parcelModel.invoice && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Receipt className="w-5 h-5 mr-2" />
              Receipt
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span>Payment Receipt</span>
              <a href={parcelModel.invoice} target="_blank" rel="noopener noreferrer" className="flex items-center text-blue-600 hover:text-blue-800 underline">
                View Receipt
                <ExternalLink className="w-4 h-4 ml-1" />
              </a>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Cost Breakdown Card */}
      {/* <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="w-5 h-5 mr-2" />
            Cost Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Description</TableHead>
                <TableHead className="text-right">Amount</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell>Shipment Price</TableCell>
                <TableCell className="text-right">£{(selectedShippingMethod?.shippingFee ?? 0).toFixed(2)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Storage Fee</TableCell>
                <TableCell className="text-right">£{storageFee?.toFixed(2)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Handling Fee</TableCell>
                <TableCell className="text-right">£{(selectedShippingMethod?.handlingFee ?? 0).toFixed(2)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Consolidation Fee</TableCell>
                <TableCell className="text-right">£{(selectedShippingMethod?.consolidationFee ?? 0).toFixed(2)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Dangerous Goods Fee</TableCell>
                <TableCell className="text-right">£{parcelModel.dangerous_goods_price?.toFixed(2)}</TableCell>
              </TableRow>
              {!selectedPriorityShipping && (
                <TableRow>
                  <TableCell>Duty</TableCell>
                  <TableCell className="text-right">£{parcelModel.duty?.toFixed(2)}</TableCell>
                </TableRow>
              )}
              {hasInsurance && (
                <TableRow>
                  <TableCell>Insurance</TableCell>
                  <TableCell className="text-right">£{selectedShippingMethod?.insuranceFee?.toFixed(2)}</TableCell>
                </TableRow>
              )}
              <TableRow className="font-medium">
                <TableCell>Total Due</TableCell>
                <TableCell className="text-right">{formattedTotalPrice}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card> */}

      {/* Total Amount */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="w-5 h-5 mr-2" />
            Total
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Description</TableHead>
                <TableHead className="text-right">Amount</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow className="font-medium">
                <TableCell>Total Due</TableCell>
                <TableCell className="text-right">{formattedTotalPrice}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Insurance and Payment Card */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col gap-4">
            {/* Insurance Alert */}
            {!parcelModel.is_payed_for && !selectedPriorityShipping && insuranceAmount > 0 && (
              <div className="flex items-center gap-2 text-sm text-amber-600 bg-amber-50 border border-amber-200 p-3 rounded-md">
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <p>Items that are more than £2,000 will be insured at a cap amount of £2,000</p>
              </div>
            )}

            {/* Insurance Checkbox and Pay Button */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {insuranceAmount > 0 && (
                  <>
                    <Checkbox
                      id="add-insurance"
                      checked={hasInsurance}
                      disabled={parcelModel.is_payed_for}
                      onCheckedChange={(checked) => {
                        handleInsuranceChange(checked as boolean);
                      }}
                    />
                    <label
                      htmlFor="add-insurance"
                      className={`${parcelModel.is_payed_for ? "text-gray-500" : ""}`}
                    >
                      Add Insurance
                      {parcelModel.is_payed_for && <span className="ml-2 text-sm text-gray-400">(Already paid)</span>}
                    </label>
                  </>
                )}
              </div>
              <Button className="w-1/3" onClick={handlePayment} disabled={parcelModel.is_payed_for || isConsolidationRequested}>
                Pay Now
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <StripePaymentForm
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        amount={totalPrice}
        parcelId={parcelModel.id ?? 0}
        trackingId={parcelModel.tracking_id ?? ""}
        isInsured={hasInsurance}
        email={userEmail ?? ""}
        uuid={parcelModel.uuid ?? ""}
        shippingMethod={parcelModel.selected_shipping_method_id?.toString() ?? ""}
        shipmentPrice={shipmentPrice}
        insuranceAmount={insuranceAmount}
        totalDue={totalPrice}
      />
    </div>
  );
}
