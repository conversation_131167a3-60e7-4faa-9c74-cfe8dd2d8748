import { formatDate } from "@/lib/utils";
import { ShoppingBag } from "lucide-react";
import { ParcelModel } from "@/data/models/parcel.model";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface OrderInfoCardProps {
  parcelModel: ParcelModel;
}

export function OrderInfoCard({ parcelModel }: OrderInfoCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <ShoppingBag className="w-5 h-5 mr-2" />
          Order Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <p>
          <strong>Bought From:</strong> {parcelModel.bought_from}
        </p>
        <p>
          <strong>Est. Due Date:</strong> {formatDate(new Date(parcelModel.est_due_date ?? "")) ?? "Pending"}
        </p>
        <p>
          <strong>Status:</strong> {parcelModel.status ?? "N/A"}
        </p>
        <p>
          <strong>Duty:</strong> £{parcelModel.duty?.toFixed(2)}
        </p>
        <p>
          <strong>Payment Date:</strong> {formatDate(new Date(parcelModel.payment_date ?? "")) ?? "Pending"}
        </p>
        <p>
          <strong>Payment Method:</strong> {parcelModel.payment_method ?? "N/A"}
        </p>
      </CardContent>
    </Card>
  );
}
