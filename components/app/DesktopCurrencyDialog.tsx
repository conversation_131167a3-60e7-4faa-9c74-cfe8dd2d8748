"use client";

import React, { use<PERSON>emo, useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Check } from "lucide-react";
import { country_currencies, currencyCodeToFlag } from "@/lib/constants";
import { useCurrency } from "@/components/app/CurrencyProvider";

/**
 * DesktopCurrencyDialog
 *
 * Extraction of the desktop dialog portion of the Currency FAB.
 * - On desktop this component renders a trigger (button) and a Dialog containing:
 *   - Search input
 *   - Current selection / source indicator
 *   - Grid of currency/country choices (small cards)
 * - It uses the shared CurrencyProvider via useCurrency() to apply selections.
 *
 * Props:
 * - trigger?: React.ReactNode
 *   If provided, the given node will be used as the DialogTrigger (wrapped via DialogTrigger asChild).
 *   Otherwise a default circular button (flag) will be shown.
 *
 * Note:
 * - This component intentionally manages its own local search state and UI-only behavior.
 * - Mobile-specific UI is intentionally not included here; a separate mobile component should be used.
 */

type Props = {
  trigger?: React.ReactNode;
};

export default function DesktopCurrencyDialog({ trigger }: Props) {
  const [dialogSearch, setDialogSearch] = useState("");
  const {
    currencyCode: userCurrencyCode,
    currencySource,
    setCurrency,
    resetToAuto,
    geoipCountryUnsupported,
    clearGeoipCountryUnsupported,
    isLoadingExchangeRate,
    displayCurrencyCode,
  } = useCurrency();

  // available currency codes derived from constants
  const availableCurrencyCodes = useMemo(
    () => Object.keys(country_currencies).sort(),
    [],
  );

  const filterCurrencyList = (search: string) => {
    const q = search.trim().toLowerCase();
    if (!q) return availableCurrencyCodes;
    return availableCurrencyCodes.filter((code) => {
      const country =
        country_currencies[code as keyof typeof country_currencies] || "";
      return (
        code.toLowerCase().includes(q) || country.toLowerCase().includes(q)
      );
    });
  };

  const filteredCurrencyCodes = useMemo(
    () => filterCurrencyList(dialogSearch),
    [dialogSearch, availableCurrencyCodes],
  );

  useEffect(() => {
    if (geoipCountryUnsupported) {
      // When opened due to unsupported geo, we clear the unsupported flag here to avoid repeated auto-opens.
      try {
        clearGeoipCountryUnsupported();
      } catch {
        // ignore
      }
    }
    // only run when the unsupported flag changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [geoipCountryUnsupported]);

  const dispatchCurrencyChanged = async (
    code: string,
    source: "address" | "inferred" | "manual",
  ) => {
    const normalized = (code || "GBP").toUpperCase();

    try {
      await setCurrency(normalized, source);
    } catch {
      // best-effort persistence fallback (sessionStorage)
      try {
        sessionStorage.setItem("mp_currency", normalized);
        sessionStorage.setItem(
          "mp_currency_source",
          source === "manual" ? "manual" : "inferred",
        );
      } catch {
        // ignore storage failures
      }
    }
  };

  const handleSelectCurrency = (code: string) => {
    dispatchCurrencyChanged(code, "manual");
  };

  const handleResetToAuto = async () => {
    try {
      await resetToAuto();
    } catch {
      try {
        sessionStorage.removeItem("mp_currency");
        sessionStorage.removeItem("mp_currency_source");
      } catch {
        // ignore
      }
    }
  };

  // Default trigger: circular button showing displayCurrencyCode flag (or skeleton while loading)
  const DefaultTrigger = (
    <button
      aria-label="Choose currency / country"
      className="bg-blue-600 hover:bg-blue-700 text-white rounded-full w-12 h-12 flex items-center justify-center shadow focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400"
      title="Choose currency / country"
      type="button"
    >
      {isLoadingExchangeRate ? (
        <Skeleton className="w-5 h-5" />
      ) : (
        <span className="text-lg">{currencyCodeToFlag(displayCurrencyCode)}</span>
      )}
    </button>
  );

  return (
    <Dialog>
      <div>
        <DialogTrigger asChild>
          {trigger ? trigger : DefaultTrigger}
        </DialogTrigger>

        <DialogContent className="p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Choose country / currency</DialogTitle>
          </DialogHeader>

          <div className="mt-3">
            <div className="flex items-center gap-2">
              <input
                type="search"
                value={dialogSearch}
                onChange={(e) => setDialogSearch(e.target.value)}
                placeholder="Search country or currency code..."
                className="flex-1 px-3 py-2 border rounded-md text-sm"
                aria-label="Search country or currency"
              />
              <DialogClose asChild>
                <button className="text-sm text-gray-600 px-3 py-2 rounded-md hover:bg-gray-100">
                  Close
                </button>
              </DialogClose>
            </div>

            <div className="mt-3 flex items-center justify-between">
              <div className="text-sm text-gray-500">
                Current: <span className="font-medium">{displayCurrencyCode}</span>
                <span className="ml-2 text-xs text-gray-400">
                  {currencySource === "inferred"
                    ? " (inferred)"
                    : currencySource === "address"
                    ? " (from address)"
                    : " (you set)"}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <button
                  onClick={handleResetToAuto}
                  className="text-sm px-3 py-1 rounded bg-gray-100 hover:bg-gray-200"
                  type="button"
                >
                  Auto
                </button>
              </div>
            </div>

            <div className="mt-4">
              {filteredCurrencyCodes.length === 0 ? (
                <div className="text-sm text-gray-500">No matches</div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 max-h-64 overflow-y-auto p-1">
                  {filteredCurrencyCodes.map((code) => {
                    const country =
                      country_currencies[code as keyof typeof country_currencies] || code;
                    const flag = currencyCodeToFlag(code);
                    const isSelected = displayCurrencyCode === code;
                    return (
                      <div key={code} className="p-0">
                        <button
                          onClick={() => handleSelectCurrency(code)}
                          className={`w-full h-full text-left p-3 border rounded-lg shadow-sm hover:shadow-md transition-colors flex flex-col justify-between ${
                            isSelected ? "ring-2 ring-blue-400 bg-blue-50" : "bg-white"
                          }`}
                          aria-pressed={isSelected}
                          type="button"
                        >
                          <div className="flex items-center gap-3">
                            <div className="text-2xl">{flag}</div>
                            <div>
                              <div className="text-sm font-medium">{country}</div>
                              <div className="text-xs text-gray-500">{code}</div>
                            </div>
                          </div>
                          <div className="mt-2 flex items-center justify-end">
                            {isSelected ? <Check className="w-4 h-4 text-blue-600" /> : null}
                          </div>
                        </button>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>

            <div className="mt-4 flex justify-end gap-2">
              <DialogClose asChild>
                <Button variant="outline">Done</Button>
              </DialogClose>
            </div>
          </div>
        </DialogContent>
      </div>
    </Dialog>
  );
}
