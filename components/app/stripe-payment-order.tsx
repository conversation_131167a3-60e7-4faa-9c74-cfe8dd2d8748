"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { loadStripe } from "@stripe/stripe-js";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Check as CheckIcon, X as XIcon } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import type {
  OrderItem,
  Currency,
  ShippingAddress,
} from "@/data/models/order.model";
import {
  AddressService,
  type UserAddressModel,
} from "@/services/address.service";

import { getOrderSuccessful } from "@/lib/email";

const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
);

const API_URL = process.env.NEXT_PUBLIC_SUPABASE_FUNCTIONS_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

interface PaymentOrderProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  userId: string;
  email: string;
  items: OrderItem[];
  subtotal: number;
  taxAmount: number;
  shippingCost: number;
  totalAmount: number;
  currency: Currency;
  // Aggregate duty amount (GBP) passed from the UI / cart layer
  duty?: number;
}

interface OrderCheckoutFormProps {
  userId: string;
  email: string;
  items: OrderItem[];
  subtotal: number;
  taxAmount: number;
  shippingCost: number;
  totalAmount: number;
  currency: Currency;
  // Aggregate duty amount (GBP)
  duty?: number;
  onClose: () => void;
  onSuccess: (orderId: string, receiptUrl: string) => void;
  onError: (message: string) => void;
  onDiscountApplied: (discountAmount: number) => void;
}

// The actual checkout form component
function OrderCheckoutForm({
  userId,
  email,
  items,
  subtotal,
  taxAmount,
  shippingCost,
  totalAmount,
  currency,
  duty,
  onClose,
  onSuccess,
  onError,
  onDiscountApplied,
}: OrderCheckoutFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [discountCode, setDiscountCode] = useState("");
  const [discountError, setDiscountError] = useState("");
  const [discountAmount, setDiscountAmount] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [appliedDiscount, setAppliedDiscount] = useState(false);
  const [isApplyingDiscount, setIsApplyingDiscount] = useState(false);
  const [shippingAddress, setShippingAddress] = useState<UserAddressModel>({});
  const [isLoadingAddress, setIsLoadingAddress] = useState(true);

  // Fetch user's shipping address on component mount
  useEffect(() => {
    const fetchAddress = async () => {
      try {
        const addressService = new AddressService();
        const address = await addressService.fetchUserAddress();
        setShippingAddress(address);
      } catch (error) {
        console.error("Error fetching user address:", error);
        // Set empty address object if fetch fails
        setShippingAddress({});
      } finally {
        setIsLoadingAddress(false);
      }
    };

    fetchAddress();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    // Wait for address to load before processing
    if (isLoadingAddress) {
      onError("Please wait while we load your shipping address...");
      return;
    }

    setIsProcessing(true);

    try {
      const currentAmount = totalAmount - discountAmount;

      const { error: submitError } = await elements.submit();
      if (submitError) {
        onError(submitError.message ?? "An error occurred");
        return;
      }

      // Diagnostic: log outgoing payment-order payload
      try {
        console.debug("POST /payment-order payload:", {
          userId,
          email,
          items,
          subtotal,
          taxAmount,
          shippingCost,
          totalAmount: currentAmount,
          currency,
          discountCode,
          isDiscounted: appliedDiscount,
          originalAmount: totalAmount,
          discountAmount,
        });
      } catch (e) {
        // ignore logging failure
      }

      const response = await fetch(`${API_URL}/payment-order`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          userId,
          email,
          items,
          subtotal,
          taxAmount,
          shippingCost,
          // include the packer-derived duty here so the Payment Intent metadata
          // and any server-side logging are aware of duty
          duty: duty || 0,
          totalAmount: currentAmount,
          currency,
          discountCode: discountCode,
          isDiscounted: appliedDiscount,
          originalAmount: totalAmount.toString(),
          discountAmount: discountAmount.toString(),
        }),
      });
      // Handle non-OK HTTP status with a clear error message for the UI
      if (!response.ok) {
        const text = await response.text().catch(() => null);
        const msg = text
          ? `Payment initialization failed (${response.status}): ${text}`
          : `Payment initialization failed with status ${response.status}`;
        onError(msg);
        setIsProcessing(false);
        return;
      }

      // Parse response once and use for diagnostics + flow.
      const respJson = await response.json();
      try {
        console.debug(
          "POST /payment-order response status:",
          response.status,
          "body:",
          respJson,
        );
      } catch (e) {
        // ignore logging errors
      }
      const clientSecret = respJson?.clientSecret;

      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        clientSecret,
        redirect: "if_required",
      });

      if (error) {
        onError(error.message ?? "An error occurred");
      } else if (paymentIntent.status === "succeeded") {
        try {
          // Transform UserAddressModel to ShippingAddress format
          const transformedShippingAddress: ShippingAddress = {
            id: shippingAddress.uuid || `addr_${userId}_${Date.now()}`,
            recipient_name:
              `${shippingAddress.FirstName || ""} ${shippingAddress.LastName || ""}`.trim(),
            company_name: shippingAddress.Company || undefined,
            address_line_1:
              shippingAddress.Street || shippingAddress.Address || "",
            address_line_2: undefined, // UserAddressModel doesn't have address_line_2
            city: shippingAddress.City || "",
            state_province: shippingAddress.StateProvince || undefined,
            postal_code: shippingAddress.PostalCode || "",
            country: shippingAddress.DeliverTo!,
            phone_number: undefined, // UserAddressModel doesn't have phone_number
            delivery_instructions: shippingAddress.DeliverTo || undefined,
            is_residential: true, // Default to residential
          };

          // const variable for email
          const htmlValues = {
            firstName: shippingAddress.FirstName,
            lastName: shippingAddress.LastName,
            items,
          };

          try {
            console.debug("POST /create-order payload:", {
              userId,
              email,
              items,
              subtotal,
              taxAmount,
              shippingCost,
              totalAmount: currentAmount,
              currency,
              paymentIntentId: paymentIntent.id,
              discountAmount,
              originalAmount: totalAmount,
              shipping_address: transformedShippingAddress,
            });
          } catch (e) {
            // ignore logging errors
          }

          const orderResponse = await fetch(`${API_URL}/create-order`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
            },
            body: JSON.stringify({
              userId,
              email,
              items,
              subtotal,
              taxAmount,
              shippingCost,
              // include duty here so the created order persists it
              duty: duty || 0,
              totalAmount: currentAmount,
              currency,
              paymentIntentId: paymentIntent.id,
              discountAmount,
              originalAmount: totalAmount,
              shipping_address: transformedShippingAddress,
            }),
          });

          // Check HTTP status first and provide clear client-visible error if creation failed
          if (!orderResponse.ok) {
            const bodyText = await orderResponse.text().catch(() => null);
            const message = bodyText
              ? `Order creation failed (${orderResponse.status}): ${bodyText}`
              : `Order creation failed with status ${orderResponse.status}`;
            throw new Error(message);
          }

          // parse JSON body (success path)
          const orderData = await orderResponse.json();

          if (!orderData || !orderData.success) {
            throw new Error(orderData?.error || "Failed to create order");
          }

          // Note: Email sending is now handled server-side in the create-order function.
          // We still build the email HTML here for logging/debug purposes, but we DO NOT
          // send it from the client to avoid CORS issues and to ensure reliable delivery.
          const html = getOrderSuccessful(
            htmlValues.firstName!,
            htmlValues.lastName!,
            htmlValues.items,
          );

          // Optionally log the HTML locally for debugging (no network call)
          try {
            console.debug("Order email HTML (client-side preview):", html);
          } catch (e) {
            // ignore logging failures
          }

          // Notify parent of success (server has already created the order)
          onSuccess(orderData.orderId, orderData.receiptUrl);
        } catch (error) {
          console.error("Post-payment Error:", error);
          onError(
            error instanceof Error
              ? error.message
              : "An unexpected error occurred",
          );
        }
      }
    } catch (error) {
      console.error("Payment Error:", error);
      onError("An unexpected error occurred");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleApplyDiscount = async () => {
    setIsApplyingDiscount(true);
    setDiscountError("");

    try {
      try {
        console.debug("POST /validate-discount payload:", {
          code: discountCode.trim(),
          amount: totalAmount,
        });
      } catch (e) {
        // ignore logging failure
      }

      const response = await fetch(`${API_URL}/validate-discount`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
          "X-Debug": "1",
        },
        body: JSON.stringify({
          code: discountCode.trim(),
          amount: totalAmount,
        }),
      });

      // Parse once and log
      const data = await response.json();
      try {
        console.debug(
          "POST /validate-discount response:",
          response.status,
          data,
        );
      } catch (e) {
        // ignore logging errors
      }

      if (!response.ok) {
        throw new Error(data.error || "Failed to validate discount code");
      }

      if (data.success) {
        setAppliedDiscount(true);
        setDiscountCode(data.discountCode);
        setDiscountAmount(data.discountAmount);
        onDiscountApplied(data.discountAmount);
      } else {
        setDiscountError(data.error || "Invalid discount code");
      }
    } catch (error) {
      console.error("Discount application error:", error);
      setDiscountError(
        error instanceof Error
          ? error.message
          : "Error processing discount code",
      );
    } finally {
      setIsApplyingDiscount(false);
    }
  };

  const getDisplayAmount = () => {
    if (appliedDiscount) {
      return (totalAmount - discountAmount).toFixed(2);
    }
    return totalAmount.toFixed(2);
  };

  const getCurrencySymbol = () => {
    switch (currency) {
      case "GBP":
        return "£";
      case "USD":
        return "$";
      case "GHS":
        return "₵";
      case "NGN":
        return "₦";
      default:
        return "£";
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <div className="flex gap-2">
          <Input
            type="text"
            placeholder="Discount Code"
            value={discountCode}
            onChange={(e) => setDiscountCode(e.target.value)}
            disabled={appliedDiscount || isProcessing}
            className="flex-1"
          />
          <Button
            type="button"
            variant="outline"
            onClick={handleApplyDiscount}
            disabled={
              !discountCode.trim() ||
              appliedDiscount ||
              isProcessing ||
              isApplyingDiscount
            }
          >
            {isApplyingDiscount ? (
              <span className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Applying...
              </span>
            ) : (
              "Apply"
            )}
          </Button>
        </div>
        {discountError && (
          <p className="text-sm text-red-500">{discountError}</p>
        )}
        {appliedDiscount && (
          <p className="text-sm text-green-500">
            Discount applied successfully! New total: {getCurrencySymbol()}
            {getDisplayAmount()}
          </p>
        )}
      </div>

      <PaymentElement />
      <div className="flex gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={onClose}
          className="flex-1"
          disabled={isProcessing}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          className="flex-1"
          disabled={isProcessing || isLoadingAddress}
        >
          {isLoadingAddress ? (
            <span className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              Loading address...
            </span>
          ) : isProcessing ? (
            <span className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              Processing...
            </span>
          ) : (
            `Pay ${getCurrencySymbol()}${getDisplayAmount()}`
          )}
        </Button>
      </div>
    </form>
  );
}

// The main payment order modal component
export function StripePaymentOrder({
  isOpen,
  onClose,
  onSuccess,
  userId,
  email,
  items,
  subtotal,
  taxAmount,
  shippingCost,
  totalAmount,
  currency,
  duty,
}: PaymentOrderProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [clientSecret, setClientSecret] = useState<string>("");
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [orderId, setOrderId] = useState<string>("");
  const [receiptUrl, setReceiptUrl] = useState<string>("");
  const [discountedAmount, setDiscountedAmount] = useState(totalAmount);

  useEffect(() => {
    if (isOpen && totalAmount) {
      setIsLoading(true);

      const payload = {
        userId,
        email,
        items,
        totalAmount,
        currency,
        duty: duty || 0,
      };

      try {
        console.debug("useEffect POST /payment-order payload:", payload);
      } catch (e) {
        // ignore
      }

      fetch(`${API_URL}/payment-order`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify(payload),
      })
        .then(async (res) => {
          if (!res.ok) {
            const txt = await res.text().catch(() => null);
            throw new Error(
              txt || `Payment intent creation failed (${res.status})`,
            );
          }
          const json = await res.json().catch(() => null);
          console.debug(
            "useEffect POST /payment-order response:",
            res.status,
            json,
          );
          return json;
        })
        .then((data) => {
          if (data && data.success) {
            setClientSecret(data.clientSecret);
          } else {
            throw new Error(
              (data && data.error) || "Failed to create payment intent",
            );
          }
        })
        .catch((error) => {
          console.error("Payment Error:", error);
          setErrorMessage(
            error.message || "An error occurred while initializing payment",
          );
          setShowErrorDialog(true);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [isOpen, totalAmount, userId, email, items, currency]);

  const options = {
    clientSecret,
    appearance: {
      theme: "stripe" as const,
    },
  };

  const handlePaymentSuccess = (orderId: string, receiptUrl: string) => {
    setOrderId(orderId);
    setReceiptUrl(receiptUrl);
    setShowSuccessDialog(true);
    onClose();
  };

  const handlePaymentError = (message: string) => {
    onClose();
    setErrorMessage(message);
    setShowErrorDialog(true);
  };

  const handleDiscountApplied = (discountAmount: number) => {
    setDiscountedAmount(totalAmount - discountAmount);
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Complete Payment</DialogTitle>
          </DialogHeader>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            </div>
          ) : clientSecret ? (
            <Elements stripe={stripePromise} options={options}>
              <OrderCheckoutForm
                userId={userId}
                email={email}
                items={items}
                subtotal={subtotal}
                taxAmount={taxAmount}
                shippingCost={shippingCost}
                totalAmount={discountedAmount}
                currency={currency}
                duty={duty}
                onClose={onClose}
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
                onDiscountApplied={handleDiscountApplied}
              />
            </Elements>
          ) : null}
        </DialogContent>
      </Dialog>

      {/* Success Dialog */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Order Placed Successfully!</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center gap-4 py-4">
            <div className="rounded-full bg-green-100 p-3">
              <CheckIcon className="h-6 w-6 text-green-600" />
            </div>
            <p className="text-center text-gray-600">
              Your order has been placed and payment processed successfully.
            </p>
            <p className="text-center text-sm text-gray-500">
              Order ID: {orderId}
            </p>
            {receiptUrl && (
              <a
                href={receiptUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 underline"
              >
                View Receipt
              </a>
            )}
          </div>
          <Button
            onClick={() => {
              setShowSuccessDialog(false);
              if (onSuccess) {
                onSuccess();
              } else {
                router.refresh();
                window.location.reload();
              }
            }}
          >
            Continue
          </Button>
        </DialogContent>
      </Dialog>

      {/* Error Dialog */}
      <Dialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Order Failed</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center gap-4 py-4">
            <div className="rounded-full bg-red-100 p-3">
              <XIcon className="h-6 w-6 text-red-600" />
            </div>
            <p className="text-center text-gray-600">
              {errorMessage || "There was an error processing your order."}
            </p>
          </div>
          <Button
            onClick={() => setShowErrorDialog(false)}
            variant="destructive"
          >
            Close
          </Button>
        </DialogContent>
      </Dialog>
    </>
  );
}
