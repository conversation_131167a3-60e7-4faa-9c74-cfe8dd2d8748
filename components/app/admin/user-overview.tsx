'use client';

import {<PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle} from "@/components/ui/card";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table";
import {<PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, Legend, Tooltip} from 'recharts';

interface CountryData {
  country: string;
  user_count: number;
}

interface UsersPerCountryProps {
  data: CountryData[];
}

const COLORS = ['#0088FE', '#00C49F', '#FF8042', '#FFBB28', '#8884D8', '#AABBCC'];

export default function UserOverview({data}: UsersPerCountryProps) {
  const totalUsers = data.reduce((sum, item) => sum + item.user_count, 0);
  const sortedData = [...data].sort((a, b) => b.user_count - a.user_count);
  const topCountries = sortedData.slice(0, 5);
  const otherCount = sortedData.slice(5).reduce((sum, item) => sum + item.user_count, 0);
  const chartData = [...topCountries, {country: 'Other', user_count: otherCount}];

  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Users Overview</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col md:flex-row">
        <div className="w-full md:w-1/2 h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                nameKey="country"
                dataKey="user_count"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]}/>
                ))}
              </Pie>
              <Tooltip/>
              <Legend/>
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="w-full md:w-1/2 mt-4 md:mt-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Country</TableHead>
                <TableHead>Users</TableHead>
                <TableHead>Percentage</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedData.map((item) => (
                <TableRow key={item.country}>
                  <TableCell>{item.country}</TableCell>
                  <TableCell>{item.user_count.toLocaleString()}</TableCell>
                  <TableCell>{((item.user_count / totalUsers) * 100).toFixed(2)}%</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <div className="mt-4 text-right">
            <strong>Total Users: {totalUsers.toLocaleString()}</strong>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

