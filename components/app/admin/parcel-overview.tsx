'use client';

import {<PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle} from "@/components/ui/card";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table";
import {<PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer} from 'recharts';
import {DashboardParcelData} from "@/data/models/dashboard.model.ts";
import { useRouter } from "next/navigation";


interface ParcelOverviewProps {
  data: DashboardParcelData;
}

function formatTimestamp(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleString(undefined, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
}

export default function ParcelOverview({data}: ParcelOverviewProps) {
  const totalParcels = data["Parcels per country"].reduce((sum, item) => sum + item.total, 0);
  const received = data["Parcels received"];
  const processing = data["Parcels being processed"];
  const inProgress = data["Parcels in progress"];
  const shipped = data["Parcels shipped"];
  const router = useRouter();

  const chartData = data["Parcels per country"].map(item => ({
    country: item.country,
    total: item.total,

  }));

  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Parcels Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <h3 className="text-lg font-semibold mb-2">Parcel Status Summary</h3>
            <Table>
              <TableBody>
                <TableRow>
                  <TableCell>Received</TableCell>
                  <TableCell className="text-right">{received.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Processing</TableCell>
                  <TableCell className="text-right">{processing.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>In Progress</TableCell>
                  <TableCell className="text-right">{inProgress.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Shipped</TableCell>
                  <TableCell className="text-right">{shipped.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><strong>Total</strong></TableCell>
                  <TableCell className="text-right">{totalParcels.toLocaleString()}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-2">Parcels per Country</h3>
            <div className="h-[200px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3"/>
                  <XAxis dataKey="country"/>
                  <YAxis/>
                  <Tooltip/>
                  {/*<Legend />*/}
                  <Bar dataKey="total" fill="#8884d8"/>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
        <div>
          <h3 className="text-lg font-semibold mb-2">Parcel Data by Country</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Country</TableHead>
                <TableHead>Total Parcels</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data["Parcels per country"].map((item) => (
                <TableRow key={item.country}>
                  <TableCell>{item.country}</TableCell>
                  <TableCell>{item.total.toString()}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">Recent Parcels</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Parcel ID</TableHead>
                <TableHead>Destination</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Update</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data["Recent parcels"].map((parcel) => (
                <TableRow key={parcel.id} onClick={()=>{
                  router.push(`/admin/edit-parcel/${parcel.id}`);
                }}>
                  <TableCell className={"hover:underline"}>{parcel.id}</TableCell>
                  <TableCell>{parcel.destination}</TableCell>
                  <TableCell>{parcel.status}</TableCell>
                  <TableCell suppressHydrationWarning>{formatTimestamp(parcel.lastUpdate)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}

