"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { AddressService } from "@/services/address.service";
import { ExchangeRateService } from "@/services/exchange.service";
import { country_currencies } from "@/lib/constants";
import { useAuth } from "@/hooks/use-auth";

/**
 * CurrencyProvider
 *
 * Responsibilities:
 * - Centralize currency state for /home/<USER>
 *   - currencyCode (e.g. "GBP", "GHS")
 *   - currencySource ("address" | "inferred" | "manual")
 *   - exchangeRate: number | null (units of target currency per 1 GBP)
 *   - isLoadingExchangeRate: boolean
 * - Persist selection to sessionStorage using keys:
 *   - mp_currency
 *   - mp_currency_source
 * - Fetch exchange rates via ExchangeRateService and cache them in sessionStorage with TTL.
 * - Provide setCurrency(code, source?) and resetToAuto() helpers.
 * - Provide setCurrency(code, source?) and resetToAuto() helpers; prefer React context consumers for updates.
 *
 * Notes:
 * - This provider is a client component and should wrap the shop subtree in layout.
 * - It intentionally does not decide whether the FAB is shown; the FAB itself reads auth state.
 */

/* Session storage keys */
const STORAGE_KEY = "mp_currency";
const STORAGE_SOURCE = "mp_currency_source";
/* Rate cache key prefix */
const RATE_CACHE_PREFIX = "mp_rate_";
/* Rate cache TTL (ms) - configurable via NEXT_PUBLIC_MP_RATE_TTL_MS (ms). Default 30 minutes */
const RATE_TTL_MS = (() => {
  try {
    // NEXT_PUBLIC_* env values are inlined at build time by Next.js; parse if present.
    if (
      typeof process !== "undefined" &&
      process.env &&
      process.env.NEXT_PUBLIC_MP_RATE_TTL_MS
    ) {
      const v = parseInt(process.env.NEXT_PUBLIC_MP_RATE_TTL_MS, 10);
      if (!Number.isNaN(v) && v > 0) return v;
    }
  } catch {
    // ignore and fall back to default
  }
  return 30 * 60 * 1000;
})();

type CurrencySource = "address" | "inferred" | "manual";

type CurrencyContextValue = {
  currencyCode: string;
  currencySource: CurrencySource;
  exchangeRate: number | null;
  isLoadingExchangeRate: boolean;
  // Indicates whether a valid exchange rate could not be obtained for the current currency.
  // Consumers can use this to surface a non-blocking warning to users when rates are unavailable.
  exchangeRateUnavailable: boolean;
  setCurrency: (code: string, source?: CurrencySource) => Promise<void>;
  resetToAuto: () => Promise<void>;
  // add `force` flag to allow bypassing cache and force refresh
  refreshExchangeRate: (code?: string, force?: boolean) => Promise<void>;
  // Indicates that geoip detected a country we do not map to a supported currency.
  // Consumers (e.g., the currency/country picker) should display UI when this is true.
  geoipCountryUnsupported: boolean;
  // Clears the unsupported-geo flag (e.g., called after user manually selects a currency).
  clearGeoipCountryUnsupported: () => void;
  // The last confirmed currency that has a valid exchange rate applied.
  // This remains stable while a new exchange rate is being fetched so consumers
  // can continue displaying the previous symbol until new prices are ready.
  lastConfirmedCurrencyCode: string;
  // The currency that should be used for display (symbol/flag) at the moment.
  // While an exchange rate is loading this will hold `lastConfirmedCurrencyCode`.
  // When a fresh rate is confirmed it will be set to the newly applied currency.
  displayCurrencyCode: string;
};

const CurrencyContext = createContext<CurrencyContextValue | undefined>(
  undefined,
);

export function useCurrency(): CurrencyContextValue {
  const ctx = useContext(CurrencyContext);
  if (!ctx) {
    throw new Error("useCurrency must be used within CurrencyProvider");
  }
  return ctx;
}

/* CurrencyProvider notes
 *
 * The React context (`useCurrency`) is the single source of truth for currency
 * state within the `/home/<USER>
 * to read or update currency and should not rely on module-level setters or
 * DOM events. Any non-React callers should be migrated to call into React
 * components or a small React-rooted interface instead.
 */

/* Helpers for sessionStorage caching of rates */
function getCachedRate(code: string): { rate: number; ts: number } | null {
  if (typeof window === "undefined") return null;
  try {
    // Prefer localStorage for cross-tab visibility, fall back to sessionStorage.
    const key = RATE_CACHE_PREFIX + code;
    const rawLocal = localStorage.getItem(key);
    const rawSession = sessionStorage.getItem(key);

    const raw = rawLocal ?? rawSession;
    if (!raw) return null;

    const parsed = JSON.parse(raw) as { rate: number; ts: number };
    if (
      !parsed ||
      typeof parsed.rate !== "number" ||
      typeof parsed.ts !== "number"
    )
      return null;

    if (Date.now() - parsed.ts > RATE_TTL_MS) {
      // stale - remove from both storages if present
      try {
        localStorage.removeItem(key);
      } catch {
        // ignore
      }
      try {
        sessionStorage.removeItem(key);
      } catch {
        // ignore
      }
      return null;
    }
    return parsed;
  } catch {
    return null;
  }
}

function setCachedRate(code: string, rate: number) {
  if (typeof window === "undefined") return;
  try {
    const payload = JSON.stringify({ rate, ts: Date.now() });
    // Write to localStorage for cross-tab visibility, and sessionStorage for backward compatibility.
    try {
      localStorage.setItem(RATE_CACHE_PREFIX + code, payload);
    } catch {
      // ignore localStorage write failures (e.g., privacy mode)
    }
    try {
      sessionStorage.setItem(RATE_CACHE_PREFIX + code, payload);
    } catch {
      // ignore
    }
  } catch {
    // ignore
  }
}

/* Infer currency code from browser locale (best-effort) */
async function inferCurrencyFromGeoIP(): Promise<string | null> {
  try {
    // Call the Edge API that uses Vercel's NextRequest.geo (or header fallback)
    // The route returns { countryCode, currency, source } where currency may be null.
    const res = await fetch("/api/geo-currency", { cache: "no-store" });
    if (!res.ok) return null;
    const data = await res.json();
    // If the edge route could not map the country to a supported currency it returns currency: null.
    return data && data.currency ? String(data.currency).toUpperCase() : null;
  } catch (err) {
    // Network or other error - treat as no inference.
    // eslint-disable-next-line no-console
    console.error("GeoIP inference failed:", err);
    return null;
  }
}

/* CurrencyProvider implementation */
export default function CurrencyProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const addressService = useMemo(() => new AddressService(), []);
  const exchangeRateService = useMemo(() => new ExchangeRateService(), []);
  const { isAuthenticated, loading: authLoading } = useAuth();

  const [currencyCode, setCurrencyCode] = useState<string>("GBP");
  const [currencySource, setCurrencySource] =
    useState<CurrencySource>("inferred");
  const [exchangeRate, setExchangeRate] = useState<number | null>(1);
  const [isLoadingExchangeRate, setIsLoadingExchangeRate] =
    useState<boolean>(false);
  // Indicates whether the provider couldn't obtain a valid exchange rate for the
  // currently selected currency. This allows UI consumers to surface a helpful
  // non-blocking warning (e.g., "Exchange rates unavailable — showing GBP").
  const [exchangeRateUnavailable, setExchangeRateUnavailable] =
    useState<boolean>(false);
  const [isBootstrapped, setIsBootstrapped] = useState<boolean>(false);
  // When GeoIP indicates a country that is not in our supported mapping, set this
  // flag so the UI (CurrencyFab / picker) can open and ask the user to choose.
  const [geoipCountryUnsupported, setGeoipCountryUnsupported] =
    useState<boolean>(false);
  // Keep the last confirmed currency code that was successfully applied (i.e. there
  // is a corresponding exchangeRate). This is used to avoid flipping the displayed
  // currency symbol immediately when a new selection triggers a rate fetch.
  const [lastConfirmedCurrencyCode, setLastConfirmedCurrencyCode] =
    useState<string>("GBP");
  // The currency code currently used for display (flags/symbols). While a fresh
  // exchange rate is being fetched this will remain equal to lastConfirmedCurrencyCode.
  // After a successful fetch this will be updated to the newly confirmed code.
  const [displayCurrencyCode, setDisplayCurrencyCode] = useState<string>("GBP");

  /* Bootstrap: decide initial currency using sessionStorage -> address -> locale -> GBP */
  useEffect(() => {
    let mounted = true;
    const load = async () => {
      if (!mounted) return;
      try {
        // 1. Session preference (only used for unauthenticated users)
        // For authenticated users we prefer the server-side address-derived currency
        // and will apply that preference in the auth effect. This prevents a saved
        // session value from overriding a backed user address when the user is signed in.
        if (typeof window !== "undefined") {
          const saved = sessionStorage.getItem(STORAGE_KEY);
          const savedSource = sessionStorage.getItem(
            STORAGE_SOURCE,
          ) as CurrencySource | null;
          if (saved && !isAuthenticated) {
            setCurrencyCode(saved);
            setCurrencySource(savedSource === "manual" ? "manual" : "inferred");
            setIsBootstrapped(true);
            return;
          }
        }

        // 2. Try address
        try {
          const address = await addressService.fetchUserAddress();
          if (address && (address as any).DeliverTo) {
            const mapped = Object.keys(country_currencies).find(
              (code) =>
                country_currencies[code as keyof typeof country_currencies] ===
                (address as any).DeliverTo,
            );
            if (mounted && mapped) {
              setCurrencyCode(mapped);
              setCurrencySource("address");
              try {
                sessionStorage.setItem(STORAGE_KEY, mapped);
                sessionStorage.setItem(STORAGE_SOURCE, "address");
              } catch {
                // ignore
              }
              setIsBootstrapped(true);
              return;
            }
          }
        } catch (err) {
          // ignore address fetch errors and continue to inference
        }

        // 3. GeoIP inference (via edge API)
        try {
          const inferred = await inferCurrencyFromGeoIP();
          if (inferred) {
            if (mounted) {
              setCurrencyCode(inferred);
              setCurrencySource("inferred");
              try {
                sessionStorage.setItem(STORAGE_KEY, inferred);
                sessionStorage.setItem(STORAGE_SOURCE, "inferred");
              } catch {
                // ignore
              }
              setIsBootstrapped(true);
              return;
            }
          } else {
            // GeoIP couldn't be mapped to a supported currency: surface the unsupported flag
            // so the UI can open the country/currency picker for the user to choose.
            if (mounted) {
              setGeoipCountryUnsupported(true);
              setIsBootstrapped(true);
              return;
            }
          }
        } catch {
          // if geoip lookup fails, continue to fallback (GBP)
        }

        // 4. Fallback to GBP
        if (mounted) {
          setCurrencyCode("GBP");
          setCurrencySource("inferred");
          try {
            sessionStorage.setItem(STORAGE_KEY, "GBP");
            sessionStorage.setItem(STORAGE_SOURCE, "inferred");
          } catch {
            // ignore
          }
          setIsBootstrapped(true);
        }
      } catch (err) {
        // If anything goes wrong, fallback to stored or GBP
        try {
          const saved =
            typeof window !== "undefined"
              ? sessionStorage.getItem(STORAGE_KEY)
              : null;
          if (saved) {
            setCurrencyCode(saved);
            const savedSource =
              typeof window !== "undefined"
                ? (sessionStorage.getItem(
                    STORAGE_SOURCE,
                  ) as CurrencySource | null)
                : null;
            setCurrencySource(savedSource === "manual" ? "manual" : "inferred");
          } else {
            setCurrencyCode("GBP");
            setCurrencySource("inferred");
            if (typeof window !== "undefined") {
              sessionStorage.setItem(STORAGE_KEY, "GBP");
              sessionStorage.setItem(STORAGE_SOURCE, "inferred");
            }
          }
        } catch {
          setCurrencyCode("GBP");
          setCurrencySource("inferred");
        } finally {
          setIsBootstrapped(true);
        }
      }
    };

    load();

    return () => {
      mounted = false;
    };
    // We intentionally run only on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /* Fetch exchange rate whenever currencyCode changes and provider is bootstrapped */
  useEffect(() => {
    let mounted = true;
    const fetchRate = async (code: string) => {
      if (!mounted) return;
      const normalizedCode = code ? String(code).toUpperCase() : code;

      // If target is GBP or unspecified, treat as canonical GBP with rate 1.
      if (!normalizedCode || normalizedCode === "GBP") {
        setExchangeRate(1);
        setExchangeRateUnavailable(false);
        setIsLoadingExchangeRate(false);
        // Confirm GBP as the last confirmed/display currency
        setLastConfirmedCurrencyCode("GBP");
        setDisplayCurrencyCode("GBP");
        return;
      }

      // Check cache first
      const cached = getCachedRate(normalizedCode);
      if (cached) {
        setExchangeRate(cached.rate);
        setExchangeRateUnavailable(false);
        setIsLoadingExchangeRate(false);
        // Cache implies we have previously confirmed this currency - update markers
        setLastConfirmedCurrencyCode(normalizedCode);
        setDisplayCurrencyCode(normalizedCode);
        return;
      }

      // While fetching a new rate, keep the UI display currency stable by using the last confirmed code.
      setDisplayCurrencyCode((prev) => lastConfirmedCurrencyCode || prev);
      setIsLoadingExchangeRate(true);
      // Clear prior unavailable flag when starting a new fetch
      setExchangeRateUnavailable(false);
      try {
        const data =
          await exchangeRateService.getExchangeRateISO(normalizedCode);
        if (data && (data as any).rate_per_gbp) {
          const rate = (data as any).rate_per_gbp as number;
          setExchangeRate(rate);
          setCachedRate(normalizedCode, rate);
          setExchangeRateUnavailable(false);
          // Mark this currency as the last confirmed and update display
          setLastConfirmedCurrencyCode(normalizedCode);
          setDisplayCurrencyCode(normalizedCode);
        } else {
          // fallback (no rate found) - revert to GBP view but flag unavailable
          setExchangeRate(1);
          setExchangeRateUnavailable(true);
          setLastConfirmedCurrencyCode("GBP");
          setDisplayCurrencyCode("GBP");
        }
      } catch (err) {
        // on error fallback to 1 but mark rate as unavailable so UI can show a hint
        // eslint-disable-next-line no-console
        console.error("Failed to fetch exchange rate:", err);
        setExchangeRate(1);
        setExchangeRateUnavailable(true);
        setLastConfirmedCurrencyCode("GBP");
        setDisplayCurrencyCode("GBP");
      } finally {
        if (mounted) setIsLoadingExchangeRate(false);
      }
    };

    if (isBootstrapped) {
      fetchRate(currencyCode);
    }
    return () => {
      mounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currencyCode, isBootstrapped]);

  /* API: setCurrency */
  const setCurrency = async (
    code: string,
    source: CurrencySource = "manual",
  ) => {
    const normalized = (code || "GBP").toUpperCase();
    try {
      if (typeof window !== "undefined") {
        sessionStorage.setItem(STORAGE_KEY, normalized);
        sessionStorage.setItem(
          STORAGE_SOURCE,
          source === "manual" ? "manual" : "inferred",
        );
      }
    } catch {
      // ignore
    }

    // Update React state (will trigger exchange fetch via effect)
    setCurrencyCode(normalized);
    setCurrencySource(source);

    // No module-level snapshot is maintained. The React context is the single source of truth.
    // Providers should update only React state and sessionStorage; avoid module-level mutable state.

    // The provider no longer relies on module-level setters or DOM events.
    // Components should use `useCurrency()` to read and update currency state.
    // trigger exchange fetch (effect will run because currencyCode changed)
  };

  /* API: resetToAuto */
  const resetToAuto = async () => {
    try {
      if (typeof window !== "undefined") {
        sessionStorage.removeItem(STORAGE_KEY);
        sessionStorage.removeItem(STORAGE_SOURCE);
      }
    } catch {
      // ignore
    }

    // Re-run inference using GeoIP (Edge API). If GeoIP cannot map to a supported
    // currency we set a flag so the UI can prompt the user with the country picker.
    try {
      const inferred = await inferCurrencyFromGeoIP();

      // If we inferred a currency, persist and apply it.
      if (inferred) {
        try {
          if (typeof window !== "undefined") {
            sessionStorage.setItem(STORAGE_KEY, inferred);
            sessionStorage.setItem(STORAGE_SOURCE, "inferred");
          }
        } catch {
          // ignore
        }
        setCurrencyCode(inferred);
        setCurrencySource("inferred");
        // clear any previous unsupported flag
        setGeoipCountryUnsupported(false);
      } else {
        // GeoIP returned no supported currency for this country.
        // Fall back to GBP but surface the picker by setting the unsupported flag.
        try {
          if (typeof window !== "undefined") {
            sessionStorage.setItem(STORAGE_KEY, "GBP");
            sessionStorage.setItem(STORAGE_SOURCE, "inferred");
          }
        } catch {
          // ignore
        }
        setCurrencyCode("GBP");
        setCurrencySource("inferred");
        setGeoipCountryUnsupported(true);
      }
    } catch (err) {
      // On error, fall back to GBP and clear unsupported flag.
      try {
        if (typeof window !== "undefined") {
          sessionStorage.setItem(STORAGE_KEY, "GBP");
          sessionStorage.setItem(STORAGE_SOURCE, "inferred");
        }
      } catch {
        // ignore
      }
      setCurrencyCode("GBP");
      setCurrencySource("inferred");
      setGeoipCountryUnsupported(false);
    }
    // exchange fetch will follow via effect
  };

  /* API: refreshExchangeRate (manual refresh if needed)
   * Accepts `force` to bypass sessionStorage cache and always request a fresh rate.
   */
  const refreshExchangeRate = async (code?: string, force: boolean = false) => {
    const target = (code || currencyCode || "GBP").toUpperCase();
    if (target === "GBP") {
      setExchangeRate(1);
      setExchangeRateUnavailable(false);
      setIsLoadingExchangeRate(false);
      return;
    }

    setIsLoadingExchangeRate(true);
    // Clear prior unavailable flag when initiating a manual refresh
    setExchangeRateUnavailable(false);
    try {
      // Use cached value unless a forced refresh is requested
      if (!force) {
        const cached = getCachedRate(target);
        if (cached) {
          setExchangeRate(cached.rate);
          setExchangeRateUnavailable(false);
          setIsLoadingExchangeRate(false);
          return;
        }
      }

      const data = await exchangeRateService.getExchangeRateISO(target);
      if (data && (data as any).rate_per_gbp) {
        const rate = (data as any).rate_per_gbp as number;
        setExchangeRate(rate);
        setCachedRate(target, rate);
        setExchangeRateUnavailable(false);
      } else {
        setExchangeRate(1);
        setExchangeRateUnavailable(true);
      }
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error("Failed to refresh exchange rate:", err);
      setExchangeRate(1);
      setExchangeRateUnavailable(true);
    } finally {
      setIsLoadingExchangeRate(false);
    }
  };

  // Legacy programmatic registration removed.
  //
  // The provider no longer registers a module-level setter or maintains a
  // module-level snapshot. All components and UI should use the `useCurrency()`
  // hook to read currency and call `setCurrency(...)` or `resetToAuto()`.
  //
  // If any remaining non-React code requires programmatic access, please
  // migrate it to a React-based integration that calls into the provider hook.

  // Listen for cross-tab `storage` events to pick up external cache updates.
  // When another tab writes a cached rate into localStorage (key `mp_rate_{CODE}`),
  // this handler will pick it up and update the provider's `exchangeRate` if the
  // cached currency matches the currently selected currency.
  useEffect(() => {
    const handler = (e: StorageEvent) => {
      try {
        if (!e.key) return;
        if (!e.key.startsWith(RATE_CACHE_PREFIX)) return;

        // Extract currency code from the storage key
        const code = e.key.substring(RATE_CACHE_PREFIX.length);
        const raw = e.newValue;
        if (!raw) {
          // Key removed or expired - no immediate action required
          return;
        }
        const parsed = JSON.parse(raw) as { rate?: number; ts?: number } | null;
        if (!parsed || typeof parsed.rate !== "number") return;

        // If the updated cache entry matches the active currency, update the provider's rate
        // so consumers react immediately without waiting for a refetch.
        if (
          code &&
          currencyCode &&
          code.toUpperCase() === currencyCode.toUpperCase()
        ) {
          setExchangeRate(parsed.rate);
          setExchangeRateUnavailable(false);
        }
      } catch {
        // ignore any parsing/listener errors
      }
    };

    if (typeof window !== "undefined") {
      window.addEventListener("storage", handler);
    }
    return () => {
      if (typeof window !== "undefined") {
        window.removeEventListener("storage", handler);
      }
    };
    // Only re-register when the active currencyCode changes.
  }, [currencyCode]);

  // React to auth state changes: when a user becomes authenticated, prefer address-derived currency if available.
  // This ensures that after login we adopt the server-backed address preference (if present) and it takes
  // precedence over any session or manual selection as required.
  useEffect(() => {
    let mounted = true;
    const applyAddressPreference = async () => {
      if (!mounted) return;
      if (authLoading) return; // wait for auth to settle
      if (!isAuthenticated) return;

      try {
        const address = await addressService.fetchUserAddress();
        if (address && (address as any).DeliverTo) {
          const mapped = Object.keys(country_currencies).find(
            (code) =>
              country_currencies[code as keyof typeof country_currencies] ===
              (address as any).DeliverTo,
          );
          if (mapped) {
            // For authenticated users address-derived currency takes precedence over any existing selection.
            // Always apply the address mapping so server-backed preferences control the currency.
            setCurrency(mapped, "address");
          }
        }
      } catch (err) {
        // ignore address fetch errors
        // eslint-disable-next-line no-console
        console.error(
          "Failed to apply address preference on auth change:",
          err,
        );
      }
    };

    applyAddressPreference();

    return () => {
      mounted = false;
    };
    // We want to run when auth state changes or when currencySource changes
  }, [isAuthenticated, authLoading, currencySource]);

  // Clear helper for the unsupported geo flag (exposed to consumers)
  const clearGeoipCountryUnsupported = () => setGeoipCountryUnsupported(false);

  const value: CurrencyContextValue = {
    currencyCode,
    currencySource,
    exchangeRate,
    isLoadingExchangeRate,
    setCurrency,
    resetToAuto,
    refreshExchangeRate,
    exchangeRateUnavailable,
    geoipCountryUnsupported,
    clearGeoipCountryUnsupported,
    lastConfirmedCurrencyCode,
    displayCurrencyCode,
  };

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
}
