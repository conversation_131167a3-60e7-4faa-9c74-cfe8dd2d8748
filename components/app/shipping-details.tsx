import { TotalCard } from "./total-card";
import { useState, useEffect } from "react";
import { AlertTriangle } from "lucide-react";
import { ReceiptCard } from "./receipt-card";
import { OrderInfoCard } from "./order-info-card";
import { PackageInfoCard } from "./package-info-card";
import { ShippingInfoCard } from "./shipping-info-card";
import { useParcelService } from "@/hooks/use-parcel-service";
import { ShippingOptionsCard } from "./shipping-options-card";
import { ShippingDetailsProps } from "@/data/models/shipping.types";
import { InsuranceAndPaymentCard } from "./insurance-and-payment-card";
import { StripePaymentForm } from "@/components/app/stripe-payment-form";
import ParcelFileUpload from "@/components/app/parcel-file-upload";

export default function ShippingDetails({ parcelModel, userEmail }: ShippingDetailsProps) {
  const { parcelService, totalPrice, shippingOptions, updateTotalPrice } = useParcelService(parcelModel);

  const selectedShippingMethod = parcelModel.shipping_options?.filter((o) => o.id === parcelModel.selected_shipping_method_id)[0];

  const [shippingError, setShippingError] = useState<string>("");
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedPriorityShipping, setSelectedPriorityShipping] = useState(false);
  const [insuranceAmount, setInsuranceAmount] = useState(selectedShippingMethod?.insuranceFee ?? 0);
  const [shipmentPrice, setShipmentPrice] = useState<number>(selectedShippingMethod?.shippingFee ?? 0);
  const [hasInsurance, setHasInsurance] = useState((parcelModel.is_payed_for && parcelModel.is_insured) || false);
  const [selectedShippingMethodId, setSelectedShippingMethodId] = useState<number>(parcelModel.selected_shipping_method_id ?? 0);

  const handleInsuranceChange = async (checked: boolean) => {
    try {
      setHasInsurance(checked);
      parcelService.setHasInsurance(checked, parcelModel.id ?? 0);
      updateTotalPrice();
    } catch (error) {
      console.error("Error calculating insurance:", error);
    }
  };

  const handlePayment = () => {
    if (!selectedShippingMethodId) {
      setShippingError("Please select a shipping option before proceeding");
      return;
    }
    setShippingError("");
    setIsPaymentModalOpen(true);
  };

  const handleShippingMethodChange = async (value: string) => {
    if (parcelModel.is_payed_for) return;

    const methodId = parseInt(value, 10);
    setSelectedShippingMethodId(methodId);
    parcelModel.selected_shipping_method_id = methodId;
    setShippingError("");

    const selectedMethod = shippingOptions.find((option) => option.id === methodId);
    setInsuranceAmount(selectedMethod?.insuranceFee ?? 0);

    try {
      parcelService.setSelectedShippingMethod(methodId, parcelModel.id ?? 0);
      updateTotalPrice();
    } catch (error) {
      console.error("Error updating shipping method:", error);
      setShippingError("Failed to update shipping method. Please try again.");
    }
  };

  useEffect(() => {
    if (parcelModel.is_payed_for && parcelModel.is_insured) {
      setInsuranceAmount(selectedShippingMethod?.insuranceFee ?? 0);
      updateTotalPrice();
    }
  }, [parcelModel.is_payed_for, parcelModel.is_insured]);

  const isConsolidationRequested = parcelModel.consolidation_status !== undefined;

  const formattedTotalPrice = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "GBP",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(totalPrice);

  return (
    <div className="space-y-6">
      {parcelModel.is_dangerous_goods && (
        <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <AlertTriangle className="h-5 w-5 text-yellow-600" />
          <span className="text-yellow-800">This parcel contains dangerous goods and requires special handling</span>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <PackageInfoCard parcelModel={parcelModel} />
        <ShippingInfoCard parcelModel={parcelModel} />
        <OrderInfoCard parcelModel={parcelModel} />
      </div>

      <ShippingOptionsCard
        parcelModel={parcelModel}
        shippingOptions={shippingOptions}
        selectedShippingMethodId={selectedShippingMethodId}
        shippingError={shippingError}
        onShippingMethodChange={handleShippingMethodChange}
      />

      {parcelModel.invoice && <ReceiptCard invoice={parcelModel.invoice} />}

      <TotalCard formattedTotalPrice={formattedTotalPrice} />
      <ParcelFileUpload parcelModel={parcelModel} onUploadSuccess={() => window.location.reload()} userEmail={userEmail} />
      <InsuranceAndPaymentCard
        parcelModel={parcelModel}
        selectedPriorityShipping={selectedPriorityShipping}
        insuranceAmount={insuranceAmount}
        hasInsurance={hasInsurance}
        isConsolidationRequested={isConsolidationRequested}
        onInsuranceChange={handleInsuranceChange}
        onPaymentClick={handlePayment}
      />

      <StripePaymentForm
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        amount={totalPrice}
        parcelId={parcelModel.id ?? 0}
        trackingId={parcelModel.tracking_id ?? ""}
        isInsured={hasInsurance}
        email={userEmail ?? ""}
        uuid={parcelModel.uuid ?? ""}
        shippingMethod={parcelModel.selected_shipping_method_id?.toString() ?? ""}
        shipmentPrice={shipmentPrice}
        insuranceAmount={insuranceAmount}
        totalDue={totalPrice}
      />
    </div>
  );
}
