"use client";

import { z } from "zod";
import { useForm } from "react-hook-form";
import { useEffect, useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Label } from "@radix-ui/react-label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { ButtonLoading } from "@/components/app/button-loading";
import { ParcelModel, ParcelService } from "@/services/parcel.service";
import { Form, FormField, FormItem, FormControl, FormMessage, FormDescription } from "@/components/ui/form";

const formSchema = z.object({
  content: z.string().min(1, {
    message: "Content is required",
  }),
  boughtFrom: z.string().min(1, {
    message: "Bought from is required",
  }),
  trackingId: z.string().min(1, {
    message: "Tracking ID is required",
  }),
  vendorTrackingId: z.string().min(1, {
    message: "Vendor tracking ID is required",
  }),
  itemValue: z.string().min(1, {
    message: "Please enter the exact value for the item for exact duty calculation",
  }),
});

export default function AddParcelPricing(props: { parcelModel: ParcelModel }) {
  const { toast } = useToast();
  const { parcelModel } = props;

  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: {
      itemValue: "",
      content: parcelModel.package_content,
      boughtFrom: parcelModel.bought_from,
      trackingId: parcelModel.tracking_id,
      vendorTrackingId: parcelModel.vendor_tracking,
    },
  });

  const showSuccessToast = () => {
    toast({
      title: "Success",
      description: "Parcel item value updated successfully.",
      variant: "default",
    });
  };

  const showErrorToast = () => {
    toast({
      title: "Error",
      description: "Failed to update parcel item value. Please try again.",
      variant: "destructive",
    });
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setLoading(true);
    console.log(values);
    const parcelService = new ParcelService();
    try {
      await parcelService.updateParcelItemValue(parseFloat(values.itemValue), parcelModel?.id!);
      showSuccessToast();
      window.location.reload();
    } catch (error) {
      console.error("Error updating parcel item value:", error);
      showErrorToast();
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (form.getValues().itemValue === "") {
      form.trigger("itemValue");
    }
  }, [form]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4 px-2 sm:px-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="trackingId"
            render={({ field }) => (
              <FormItem>
                <Label htmlFor="tracking-id">Tracking ID</Label>
                <FormControl>
                  <Input {...field} id="tracking-id" placeholder="PKG240910T13215E5P79UWY" required disabled />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="vendorTrackingId"
            render={({ field }) => (
              <FormItem>
                <Label htmlFor="vendor-tracking-id">Vendor Tracking ID</Label>
                <FormControl>
                  <Input {...field} id="vendor-tracking-id" placeholder="TRACK1234" required disabled />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <FormField
          control={form.control}
          name="content"
          render={({ field }) => (
            <FormItem>
              <Label htmlFor="content">Content</Label>
              <FormControl>
                <Input {...field} id="content" placeholder="Parcel content" required disabled />
              </FormControl>
            </FormItem>
          )}
        />
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="itemValue"
            render={({ field }) => (
              <FormItem>
                <Label htmlFor="item-value">Item Value (&pound;)</Label>
                <FormControl>
                  <Input {...field} id="item-value" placeholder="0.00" autoFocus />
                </FormControl>
                <FormDescription>Please ensure the value of your item is in Great British Pounds (£)</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="boughtFrom"
            render={({ field }) => (
              <FormItem>
                <Label htmlFor="bought-from">Bought From</Label>
                <FormControl>
                  <Input {...field} id="bought-from" placeholder="Amazon" disabled />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        {
          !loading ? <Button type="submit" className="w-full">Submit</Button> : <ButtonLoading />
        }
      </form>
    </Form>
  );
}
