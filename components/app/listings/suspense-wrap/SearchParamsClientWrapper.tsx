"use client";

import React, { Suspense } from "react";
import { ReadonlyURLSearchParams, useSearchParams } from "next/navigation";

type SearchParams = ReadonlyURLSearchParams;

type RenderFn = (searchParams: SearchParams) => React.ReactNode;

type Props = {
  /**
   * Render prop that receives the client-side `searchParams` object.
   * Example:
   * <SearchParamsClientWrapper fallback={<Skeleton />}>
   *   {(params) => <ResultsPage query={params.get('q') ?? ''} />}
   * </SearchParamsClientWrapper>
   */
  children: RenderFn;
  /**
   * Optional fallback to show while the inner client reader hydrate/initializes.
   * Defaults to null (renders nothing).
   */
  fallback?: React.ReactNode;
  /**
   * Optional additional className to apply to the wrapper container.
   */
  className?: string;
};

/**
 * Internal client component that reads `useSearchParams()` and invokes the
 * render-prop. This must run on the client because useSearchParams is a
 * client-only hook.
 */
function SearchParamsReader({ children }: { children: RenderFn }) {
  const searchParams = useSearchParams();
  return <>{children(searchParams)}</>;
}

/**
 * SearchParamsClientWrapper
 *
 * A small wrapper component that places the `useSearchParams()` call inside
 * a client-side Suspense boundary. This allows pages (server components)
 * to safely render this component without causing CSR bailout, by ensuring
 * that all Next.js client-only navigation hooks execute inside a client
 * component tree.
 *
 * Usage (server component / page):
 * <SearchParamsClientWrapper fallback={<Skeleton />}>
 *   {(params) => <ClientOnlyResults params={params} />}
 * </SearchParamsClientWrapper>
 */
export default function SearchParamsClientWrapper({
  children,
  fallback = null,
  className,
}: Props) {
  return (
    <div className={className}>
      <Suspense fallback={fallback}>
        <SearchParamsReader>{children}</SearchParamsReader>
      </Suspense>
    </div>
  );
}
