import React, { useState, useEffect } from "react";
import { Carousel, CarouselContent, CarouselItem } from "./carousel";
import Autoplay from "embla-carousel-autoplay";

interface MPCarouselProps {
  images: { src: string; alt: string }[];
  mobileImages: { src: string; alt: string }[];
}

const MPCarousel: React.FC<MPCarouselProps> = ({ images, mobileImages }) => {
  const [currentImages, setCurrentImages] = useState(images);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 768) {
        setCurrentImages(mobileImages);
      } else {
        setCurrentImages(images);
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, [images, mobileImages]);

  return (
    <Carousel
      className="w-full overflow-hidden"
      opts={{
        loop: true,
      }}
      plugins={[
        Autoplay({
          delay: 5000,
        }),
      ]}
    >
      <CarouselContent>
        {currentImages.map((image, index) => (
          <CarouselItem key={index} className="pl-0">
            <div className="md:aspect-auto">
              <div className="sm:h-auto">
                <img
                  src={image.src}
                  alt={image.alt}
                  className="w-full h-full object-contain md:object-cover"
                />
              </div>
            </div>
          </CarouselItem>
        ))}
      </CarouselContent>
    </Carousel>
  );
};

export default MPCarousel;