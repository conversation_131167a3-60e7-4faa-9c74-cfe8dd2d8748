import { cn } from "@/lib/utils";
import Marquee from "@/components/ui/marquee";

const reviews = [
  {
    name: "<PERSON><PERSON><PERSON>",
    username: "",
    body: "I ordered a designer watch from the UK, and it arrived at my doorstep in Accra within a week! The tracking system was spot-on.",
    img: "https://avatar.vercel.sh/kwame_gh",
  },
  {
    name: "<PERSON><PERSON>",
    username: "",
    body: "Finally, I can shop for my favorite UK fashion brands! My summer dresses arrived in Lagos faster than I expected. Great service!",
    img: "https://avatar.vercel.sh/amaka_ng",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    username: "",
    body: "Ordered some limited edition sneakers from a UK store. The shipping was smooth, and they arrived in Gaborone in perfect condition!",
    img: "https://avatar.vercel.sh/tebogo_bw",
  },
  {
    name: "Nda<PERSON><PERSON>",
    username: "",
    body: "The customer support is outstanding. They've helped me every step of the way!",
    img: "https://avatar.vercel.sh/ndapewa_na",
  },
  {
    name: "<PERSON><PERSON>",
    username: "",
    body: "Impressed with how they handled my fragile items. The vintage vinyl records I ordered arrived in Kumasi without a scratch!",
    img: "https://avatar.vercel.sh/kofi_gh",
  },
  {
    name: "Chidi",
    username: "",
    body: "Ordered some premium whisky from Scotland. It arrived in Abuja well-packaged and on time. This service is a game-changer!",
    img: "https://avatar.vercel.sh/chidi_ng",
  },
];

const firstRow = reviews.slice(0, reviews.length / 2);
const secondRow = reviews.slice(reviews.length / 2);

const ReviewCard = ({ img, name, username, body }: { img: string; name: string; username: string; body: string }) => {
  return (
    <figure
      className={cn(
        "relative w-64 cursor-pointer overflow-hidden rounded-xl border p-4",
        // light styles
        "border-gray-950/[.1] bg-gray-950/[.01] hover:bg-gray-950/[.05]",
        // dark styles
        "dark:border-gray-50/[.1] dark:bg-gray-50/[.10] dark:hover:bg-gray-50/[.15]"
      )}
    >
      <div className="flex flex-row items-center gap-2">
        <img className="rounded-full" width="32" height="32" alt="" src={img} />
        <div className="flex flex-col">
          <figcaption className="text-sm font-medium dark:text-white">{name}</figcaption>
          <p className="text-xs font-medium dark:text-white/40">{username}</p>
        </div>
      </div>
      <blockquote className="mt-2 text-sm">{body}</blockquote>
    </figure>
  );
};

export function MPFAQ() {
  return (
    <div className="relative flex h-[500px] w-full flex-col items-center justify-center overflow-hidden">
      <Marquee pauseOnHover className="[--duration:20s]">
        {firstRow.map((review) => (
          <ReviewCard key={review.username} {...review} />
        ))}
      </Marquee>
      <Marquee reverse pauseOnHover className="[--duration:20s]">
        {secondRow.map((review) => (
          <ReviewCard key={review.username} {...review} />
        ))}
      </Marquee>
    </div>
  );
}
