"use client";

import { useEffect, useState } from "react";

interface AnnouncementProps {
  announcements: string[];
  speed?: number;
}

export const MPAnnouncementBanner = ({ announcements, speed = 30 }: AnnouncementProps) => {
  return (
    <div className="bg-white py-2 overflow-hidden border-y">
      <div className="relative whitespace-nowrap">
        {/* First copy of announcements */}
        <div className="inline-block animate-marquee">
          {announcements.map((announcement, index) => (
            <span key={index} className="mx-4 text-black">
              📢 {announcement}
            </span>
          ))}
        </div>
        {/* Second copy for seamless loop */}
        <div className="inline-block animate-marquee2 absolute top-0">
          {announcements.map((announcement, index) => (
            <span key={index} className="mx-4 text-black">
              📢 {announcement}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};
