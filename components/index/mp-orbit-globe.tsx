import OrbitingCircles from "@/components/ui/orbiting-circles";

export function MPOrbitGlobe({ images }: { images: { src: string; alt: string; url: string }[] }) {
  return (
    <div className="relative flex h-[500px] w-full flex-col items-center justify-center overflow-hidden rounded-lg bg-background">
      <span className="pointer-events-none whitespace-pre-wrap bg-gradient-to-b from-black to-gray-300 bg-clip-text text-center text-8xl font-semibold leading-none text-transparent dark:from-white dark:to-black"></span>

      {/* Inner Circles */}
      <OrbitingCircles
        className="size-[150px] border-none bg-transparent"
        duration={20}
        delay={20}
        radius={80}
      >
        <img
          src={images[0].src}
          alt={images[0].alt}
          onClick={() => window.open(images[0].url, "_blank")}
        />
      </OrbitingCircles>
      <OrbitingCircles
        className="size-[150px] border-none bg-transparent"
        duration={20}
        delay={10}
        radius={80}
      >
        <img
          src={images[1].src}
          alt={images[1].alt}
          onClick={() => window.open(images[1].url, "_blank")}
        />
      </OrbitingCircles>

      {/* Outer Circles (reverse) */}
      <OrbitingCircles
        className="size-[150px] border-none bg-transparent"
        radius={190}
        duration={20}
        reverse
      >
        <img
          src={images[2].src}
          alt={images[2].alt}
          onClick={() => window.open(images[2].url, "_blank")}
        />
      </OrbitingCircles>
      <OrbitingCircles
        className="size-[150px] border-none bg-transparent"
        radius={190}
        duration={20}
        delay={20}
        reverse
      >
        <img
          src={images[3].src}
          alt={images[3].alt}
          onClick={() => window.open(images[3].url, "_blank")}
        />
      </OrbitingCircles>
    </div>
  );
}