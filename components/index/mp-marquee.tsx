import Marquee from "@/components/ui/marquee";

export function MPMarquee({ images }: { images: { src: string; alt: string; url: string }[] }) {
  return (
    <div className="relative flex h-[85.27px] w-full flex-col items-center justify-center overflow-hidden bg-[#DEDDDE]">
      <Marquee pauseOnHover className="[--duration:20s]">
        {images.map((images) => (
          <img
            key={images.src}
            src={images.src}
            alt={images.alt}
            onClick={() => window.open(images.url, "_blank")}
            className="w-[130.69px] h-[65.27px]"
          />
        ))}
      </Marquee>
    </div>
  );
}