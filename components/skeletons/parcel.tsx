import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function ParcelSkeleton() {
  return (
    <div className="mx-auto w-full max-w-[1050px] space-y-4">
      {[...Array(4)].map((_, index) => (
        <Card key={index} className="overflow-hidden pt-4 pb-4">
          <div className="px-4 py-2">
            <div className="flex flex-col space-y-3">
              <div className="flex justify-between items-center">
                <Skeleton className="h-4 w-1/4" />
                <Skeleton className="h-4 w-4" />
              </div>
              <Skeleton className="h-4 w-1/3" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-4 w-2/5" />
              <Skeleton className="h-4 w-3/5" />
              <Skeleton className="h-4 w-1/4" />
            </div>
          </div>
        </Card>
      ))}
      <div className="flex justify-center mt-6 space-x-2">
        <Skeleton className="h-10 w-24" />
        <Skeleton className="h-10 w-40" />
        <Skeleton className="h-10 w-24" />
      </div>
    </div>
  );
}
