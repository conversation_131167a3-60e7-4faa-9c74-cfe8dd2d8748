import { Skeleton } from "@/components/ui/skeleton"

export function HomeSkeleton() {
  return (
    <div className="w-full max-w-[800px] mx-auto p-6 bg-white">
      {/* Tabs Skeleton */}
      <div className="w-full mb-6">
        <div className="grid w-full grid-cols-3 h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground">
          {[...Array(3)].map((_, index) => (
            <Skeleton key={index} className="h-8 mx-1 rounded-md bg-gray-200" />
          ))}
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200">
        {/* Header Section */}
        <div className="bg-gray-800 text-white p-6">
          <Skeleton className="h-8 w-64 bg-gray-700" /> {/* Title */}
          <Skeleton className="h-4 w-80 mt-2 bg-gray-700" /> {/* Subtitle */}
        </div>
        
        <div className="p-6">
          {/* Info Box */}
          <div className="bg-gray-100 border-l-4 border-gray-800 p-4 mb-6 rounded-r-lg">
            <Skeleton className="h-5 w-full bg-gray-200" />
          </div>
          
          {/* Address Fields */}
          <div className="space-y-3 divide-y divide-gray-200">
            {[...Array(8)].map((_, index) => (
              <div key={index} className="pt-3 first:pt-0">
                <Skeleton className="h-4 w-24 mb-1 bg-gray-200" /> {/* Label */}
                <Skeleton className="h-5 w-full bg-gray-100" /> {/* Value */}
              </div>
            ))}
          </div>
          
          {/* Footer Info */}
          <div className="mt-6 flex items-center bg-gray-100 p-3 rounded-lg">
            <Skeleton className="h-4 w-full bg-gray-200" />
          </div>
        </div>
      </div>
    </div>
  )
}
