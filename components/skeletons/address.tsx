import { Skeleton } from "@/components/ui/skeleton";

export default function AddressSkeleton() {
  return (
    <main className="flex flex-1 flex-col gap-4 p-4 lg:gap-6 lg:p-6 min-h-screen">
      <div className="flex items-center">
        <Skeleton className="h-8 w-48" /> {/* Page title skeleton */}
      </div>
      <div className="flex flex-1 items-center justify-center rounded-lg shadow-sm">
        <div className="flex flex-col items-center gap-1 w-full max-w-[800px]">
          <div className="w-full space-y-6">
            <Skeleton className="h-24 w-full" /> {/* Alert skeleton */}
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-20" /> {/* Label skeleton */}
                  <Skeleton className="h-10 w-full" /> {/* Input skeleton */}
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-28" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-28" />
                <Skeleton className="h-10 w-full" />
              </div>
              <Skeleton className="h-12 w-full" /> {/* Button skeleton */}
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
