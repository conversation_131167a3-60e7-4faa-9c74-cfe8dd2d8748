import { Skeleton } from "@/components/ui/skeleton";

export default function ProfileSkeleton() {
  return (
    <main className="flex flex-1 flex-col gap-2 p-4 lg:p-6 w-full overflow-x-hidden">
      <div className="flex flex-1 items-center justify-center rounded-lg shadow-sm">
        <div className="flex flex-col items-center gap-6 w-full max-w-[800px]">
          <div className="flex items-start w-full">
            {" "}
            {/* Changed to items-start and added w-full */}
            <Skeleton className="w-36 h-36 rounded-full" /> {/* Avatar skeleton */}
          </div>
          <div className="w-full space-y-4">
            {/* Profile information skeletons */}
            {[...Array(4)].map((_, index) => (
              <div key={index} className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" /> {/* Form description skeleton */}
                  <Skeleton className="h-5 w-32" /> {/* Form label skeleton */}
                </div>
                <Skeleton className="h-8 w-8 rounded-md" /> {/* Edit button skeleton */}
              </div>
            ))}

            {/* Marketing emails switch skeleton */}
            <div className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
              <div className="space-y-2">
                <Skeleton className="h-5 w-36" /> {/* Switch label skeleton */}
                <Skeleton className="h-4 w-64" /> {/* Switch description skeleton */}
              </div>
              <Skeleton className="h-6 w-10 rounded-full" /> {/* Switch skeleton */}
            </div>

            {/* Link cards */}
            <div className="mt-6">
              <div className="block">
                <div className="flex items-center justify-between p-6 bg-white rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-full">
                      <Skeleton className="w-6 h-6" /> {/* Skeleton for the icon */}
                    </div>
                    <div>
                      <Skeleton className="h-6 w-32 sm:w-48" /> {/* Skeleton for the title */}
                      <Skeleton className="h-4 w-24 sm:w-32 mt-1" /> {/* Skeleton for the description */}
                    </div>
                  </div>
                  <Skeleton className="w-4 h-4 sm:w-6 sm:h-6 text-gray-400" /> {/* Skeleton for the arrow icon */}
                </div>
              </div>
            </div>

            <div className="mt-6">
              <div className="block">
                <div className="flex items-center justify-between p-6 bg-white rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-full">
                      <Skeleton className="w-6 h-6" /> {/* Skeleton for the icon */}
                    </div>
                    <div>
                      <Skeleton className="h-6 w-32 sm:w-48" /> {/* Skeleton for the title */}
                      <Skeleton className="h-4 w-24 sm:w-32 mt-1" /> {/* Skeleton for the description */}
                    </div>
                  </div>
                  <Skeleton className="w-4 h-4 sm:w-6 sm:h-6 text-gray-400" /> {/* Skeleton for the arrow icon */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
