import { Skeleton } from "@/components/ui/skeleton"

export function ShopSkeleton() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
      {[...Array(9)].map((_, index) => (
        <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
          {/* Product Image Skeleton */}
          <div className="relative">
            {/* Discount Badge Skeleton */}
            {index % 3 === 0 && (
              <div className="absolute top-2 sm:top-3 left-2 sm:left-3 z-10">
                <Skeleton className="h-6 w-16 rounded-full bg-gray-300" />
              </div>
            )}

            {/* Origin Badge Skeleton */}
            <div className="absolute top-2 sm:top-3 right-2 sm:right-3 z-10">
              <Skeleton className="h-6 w-8 rounded-full bg-gray-300" />
            </div>

            {/* Image Skeleton */}
            <div className="w-full h-48 sm:h-56 md:h-64">
              <Skeleton className="w-full h-full bg-gray-200" />
            </div>
          </div>

          {/* Product Info Skeleton */}
          <div className="p-3 sm:p-4">
            {/* Title Skeleton */}
            <Skeleton className="h-5 sm:h-6 w-full mb-1 sm:mb-2 bg-gray-200" />

            {/* Description Skeleton */}
            <div className="mb-3 sm:mb-4 space-y-1">
              <Skeleton className="h-3 sm:h-4 w-full bg-gray-100" />
              <Skeleton className="h-3 sm:h-4 w-3/4 bg-gray-100" />
            </div>

            {/* Price and Stock Skeleton */}
            <div className="flex items-center justify-between">
              <div className="flex items-end gap-1 sm:gap-2">
                {/* Price Skeleton */}
                <Skeleton className="h-5 sm:h-6 w-16 bg-gray-200" />
                {/* Original Price Skeleton (for discounted items) */}
                {index % 4 === 0 && (
                  <Skeleton className="h-4 w-12 bg-gray-100" />
                )}
              </div>

              {/* Stock Status Skeleton */}
              <Skeleton className="h-5 w-16 rounded bg-gray-100" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
