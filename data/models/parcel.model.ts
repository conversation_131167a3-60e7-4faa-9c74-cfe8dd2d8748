import { ShippingMethodWithFees } from "./shipping_methods.model";

export interface ParcelModel {
  id?: number;
  tracking_id?: string;
  user_id?: number;
  dimensions?: string;
  package_weight?: number;
  package_content?: string;
  items_value?: number;
  received_at?: string;
  received_on?: string;
  shipping_address?: string;
  vendor_tracking?: string;
  delivered_date?: string;
  bought_from?: string;
  est_due_date?: string;
  status?: string;
  duty?: number;
  invoice?: string;
  payment_date?: string;
  payment_method?: string;
  storage_fee?: number;
  total_due?: number;
  is_insured?: boolean;
  is_payed_for?: boolean;
  is_dangerous_goods?: boolean;
  dangerous_goods_price?: number;
  is_air_cargo?: boolean;
  is_deleted?: boolean;
  is_sea_cargo?: boolean;
  is_discounted?: boolean;
  is_consolidated?: boolean;
  discount_amount?: number;
  uuid?: string;
  third_party_tracking?: string;
  selected_shipping_method_id?: number;
  has_insurance?: boolean;
  country?: string;
  consolidation_status?: number;
  items_pdf?: ParcelItemPDF;
  shipping_options?: ShippingMethodWithFees[];
}

interface ParcelItemPDF {
  name: string;
  url: string;
}