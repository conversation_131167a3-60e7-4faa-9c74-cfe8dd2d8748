type Currency = "GBP" | "GHS" | "NGN" | "USD";
type OrderStatus =
  | "pending"
  | "confirmed"
  | "processing"
  | "shipped"
  | "delivered"
  | "cancelled";
type PaymentStatus = "pending" | "completed" | "failed" | "refunded";
type Location = "UK" | "Ghana";
type ProductCondition = "New" | "Used";

type OrderItem = {
  id: string;
  product_id: string;
  variant_id?: string; // Added for variant-specific orders
  product_snapshot: {
    title: string;
    description: string;
    category: string;
    subcategory: string;
    condition: ProductCondition;
    refundable: boolean;
    sku: string;
    weight_kg?: number;
    dimensions_cm?: {
      length: number;
      width: number;
      height: number;
    };
  };
  variant_snapshot?: {
    variant_id: string;
    option_values: { [key: string]: string };
    sku?: string;
    price: number;
    image: { url: string; name: string };
    sale_price?: number;
    stock: number;
  };
  quantity: number;
  unit_price: number;
  discount_applied?: {
    type: "percentage" | "fixed_amount";
    value: number;
    description: string;
  };
  line_total: number;
  // Packer/legacy duty for this line (GBP)
  duty?: number;
  currency: Currency;
  warehouse_location: Location;
  notes?: string;
  created_at: Date;
  updated_at: Date;
};

type ShippingAddress = {
  id: string;
  recipient_name: string;
  company_name?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state_province?: string;
  postal_code: string;
  country: string;
  phone_number?: string;
  delivery_instructions?: string;
  is_residential: boolean;
};

type ShippingDetails = {
  partner_name: string;
  tracking_id: string;
};

type PaymentDetails = {
  method: "card" | "bank_transfer" | "paypal" | "apple_pay" | "google_pay";
  status: PaymentStatus;
  processor: string;
  transaction_id: string;
  // Aggregate packer-derived duty for the order/payment (GBP)
  duty?: number;
  subtotal: number;
  shipping_cost: number;
  discount_amount: number;
  total_amount: number;
  currency: Currency;
  card_last_four?: string;
  card_brand?: string;
  payment_date?: Date;
  refund_amount?: number;
  refund_date?: Date;
  refund_reason?: string;
  invoice_number: string;
  invoice_url?: string;
};

type Order = {
  id: string;
  order_number: string;
  user_id: string;
  status: OrderStatus;
  status_history: {
    status: OrderStatus;
    timestamp: Date;
    notes?: string;
    updated_by?: string;
  }[];
  items: OrderItem[];
  shipping_address: ShippingAddress;
  shipping_details: ShippingDetails;
  payment_details: PaymentDetails;
  subtotal: number;
  shipping_cost: number;
  discount_amount: number;
  total_amount: number;
  currency: Currency;
  created_at: Date;
  confirmed_at?: Date;
  shipped_at?: Date;
  delivered_at?: Date;
  cancelled_at?: Date;
  updated_at: Date;
  source: "web" | "mobile_app" | "admin" | "api";
  customer_notes?: string;
  admin_notes?: string;
  marketing_opt_in: boolean;
};

export type {
  Order,
  OrderItem,
  OrderStatus,
  ShippingAddress,
  ShippingDetails,
  Currency,
  PaymentStatus,
  Location,
  ProductCondition,
  PaymentDetails,
};
