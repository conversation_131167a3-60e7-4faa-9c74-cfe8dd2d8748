import { ParcelModel } from "./parcel.model";

export interface ConsolidationRequest {
  id: number;
  code: string;
  userId: number;
  userName: string;
  requestDate: string;
  status: string;
  parcels: ParcelModel[];
}

export interface SupabaseConsolidationRequest {
  id: number;
  code: string;
  status: number;
  created_at: string;
  user_id: number;
  mp_users: {
    first_name: string;
    last_name: string;
    email: string;
  };
  users_parcel_details: ParcelModel[];
}