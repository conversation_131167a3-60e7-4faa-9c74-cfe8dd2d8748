import { User } from "@supabase/supabase-js";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase_client";

// Helper function to get admin data from cookie
const getAdminDataFromCookie = () => {
  if (typeof window === "undefined") return null;

  const adminCookie = document.cookie
    .split("; ")
    .find((row) => row.startsWith("_auth_admin="));

  if (!adminCookie) return null;

  try {
    const adminData = JSON.parse(decodeURIComponent(adminCookie.split("=")[1]));
    return adminData;
  } catch (error) {
    console.error("Error parsing admin cookie:", error);
    return null;
  }
};

// Helper function to clear admin cookie
const clearAdminCookie = () => {
  if (typeof window !== "undefined") {
    document.cookie =
      "_auth_admin=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
  }
};

export function useAuth() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    const checkSession = async () => {
      setLoading(true);

      // Check for user data in localStorage
      const userData = localStorage.getItem("__mp_user_data");

      // Check for admin data in cookie
      const adminData = getAdminDataFromCookie();

      const { data, error } = await supabase.auth.getUser();
      if (error) {
        console.error("Error getting session:", error);
        // Clear storage if there's an auth error
        localStorage.removeItem("__mp_user_data");
        clearAdminCookie();
        setIsAuthenticated(false);
        setIsAdmin(false);
        setLoading(false);
        return;
      }

      setUser(data?.user || null);

      // Set authentication states based on stored data
      const hasUserData = !!userData;
      const hasAdminData = !!adminData;

      setIsAuthenticated(hasUserData);
      setIsAdmin(hasAdminData);

      setLoading(false);
    };

    checkSession();

    const { data: subscription } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user || null);

        if (!session?.user) {
          // Clear storage on sign out
          localStorage.removeItem("__mp_user_data");
          clearAdminCookie();
          setIsAuthenticated(false);
          setIsAdmin(false);
        } else {
          // Re-check storage when user signs in
          const userData = localStorage.getItem("__mp_user_data");
          const adminData = getAdminDataFromCookie();

          setIsAuthenticated(!!userData);
          setIsAdmin(!!adminData);
        }
      },
    );

    return () => {
      subscription.subscription.unsubscribe();
    };
  }, [router]);

  // Function to redirect authenticated users to home/shop
  const redirectIfAuthenticated = () => {
    if (!loading && isAuthenticated) {
      router.push("/home/<USER>");
    }
  };

  // Function to redirect unauthenticated users to login
  const redirectIfNotAuthenticated = () => {
    if (!loading && !isAuthenticated) {
      router.push("/login");
    }
  };

  // Function to redirect non-admin users
  const redirectIfNotAdmin = () => {
    if (!loading && !isAdmin) {
      router.push("/admin");
    }
  };

  const redirectIfAdmin = () => {
    if (!loading && isAdmin) {
      router.push("/admin/dashboard");
    }
  };

  return {
    user,
    loading,
    isAuthenticated,
    isAdmin,
    redirectIfAuthenticated,
    redirectIfNotAuthenticated,
    redirectIfNotAdmin,
    redirectIfAdmin,
  };
}
