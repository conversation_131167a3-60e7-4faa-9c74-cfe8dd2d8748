import { useRef, useState, useEffect } from 'react';
import { ParcelService } from "@/services/parcel.service";
import { ParcelModel } from "@/data/models/parcel.model";
import { ShippingMethodWithFees } from "@/data/models/shipping_methods.model";

export function useParcelService(parcelModel: ParcelModel) {
  const parcelServiceRef = useRef(new ParcelService());
  const [totalPrice, setTotalPrice] = useState(0);
  const [storageFee, setStorageFee] = useState(0);
  const [shippingOptions, setShippingOptions] = useState<ShippingMethodWithFees[]>([]);

  const updateTotalPrice = () => {
    try {
      const newTotalPrice = parcelServiceRef.current.calculateTotalPrice(parcelModel.id ?? 0);
      setTotalPrice(newTotalPrice);
    } catch (error) {
      console.error("Error calculating total price:", error);
    }
  };

  const getShippingOptions = () => {
    try {
      setShippingOptions(parcelModel.shipping_options ?? []);
    } catch (error) {
      console.error("Error loading shipping options:", error);
    }
  };

  useEffect(() => {
    parcelServiceRef.current.parcels = [parcelModel];

    if (parcelModel && parcelModel.id) {
      getShippingOptions();
      const calcStorageFee = parcelServiceRef.current.calculateParcelStorageFee(parcelModel.id ?? 0);
      setStorageFee(calcStorageFee);
    }
  }, [parcelModel]);

  return {
    parcelService: parcelServiceRef.current,
    totalPrice,
    storageFee,
    shippingOptions,
    updateTotalPrice,
  };
}