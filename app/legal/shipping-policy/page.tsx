import { AlertTriangle, Shield, Package, ScrollText } from "lucide-react";
import Image from "next/image";

export default function ShippingPolicy() {
  return (
    <div className="max-w-5xl mx-auto px-4 py-12">
      {/* Header */}
      <div className="text-center mb-12">
        <div className="mb-8">
          <Image
            src="/assets/imgs/logo.svg"
            alt="MailPallet Logo"
            width={120}
            height={60}
            className="mx-auto"
            priority
          />
        </div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Shipping Policy</h1>
        <p className="text-lg text-gray-600">Important guidelines and restrictions for your shipments</p>
      </div>

      {/* Warning Banner */}
      <div className="bg-amber-50 border-l-4 border-amber-500 p-4 mb-8 rounded-r-lg">
        <div className="flex items-center">
          <AlertTriangle className="h-6 w-6 text-amber-500 mr-3" />
          <p className="text-amber-700">
            <strong>Important Notice:</strong> Failure to comply with these shipping restrictions may result in account termination.
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="space-y-8">
        {/* General Limitations */}
        <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">General Shipping Limitations</h2>
          <p className="text-gray-600 mb-4">
            While we strive to ship nearly any item for our clients, we must adhere to the limitations set by various shipping companies. To determine specific restrictions for your country, please consult the website of the shipping company you intend to use.
          </p>
        </section>

        {/* Prohibited Items */}
        <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center mb-4">
            <Shield className="h-6 w-6 text-red-500 mr-2" />
            <h2 className="text-2xl font-semibold text-gray-900">Prohibited Items for Air Transportation</h2>
          </div>
          <div className="grid md:grid-cols-2 gap-4">
            <ul className="list-disc list-inside text-gray-600 space-y-2">
              <li>Guns, firearms, and ammunition</li>
              <li>Explosive materials</li>
              <li>Batteries in equipment (more than two items per package)</li>
              <li>Loose batteries</li>
              <li>Flammable items</li>
              <li>Aerosols</li>
            </ul>
            <ul className="list-disc list-inside text-gray-600 space-y-2">
              <li>Perfumes with alcohol</li>
              <li>Alcoholic beverages</li>
              <li>Fresh fruits and vegetables</li>
              <li>Medications</li>
              <li>Poison</li>
              <li>Any organic material</li>
            </ul>
          </div>
        </section>

        {/* Country Restrictions */}
        <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center mb-4">
            <Package className="h-6 w-6 text-blue-500 mr-2" />
            <h2 className="text-2xl font-semibold text-gray-900">Country-Specific Restrictions</h2>
          </div>
          <p className="text-gray-600 mb-4">
            Certain items may be prohibited based on local laws and regulations. Below are common examples:
          </p>
          <div className="grid md:grid-cols-3 gap-4">
            <ul className="list-disc list-inside text-gray-600 space-y-2">
              <li>E-cigarettes</li>
              <li>Antiques</li>
              <li>Bulk goods</li>
              <li>Cosmetics</li>
              <li>Currency</li>
            </ul>
            <ul className="list-disc list-inside text-gray-600 space-y-2">
              <li>Drugs</li>
              <li>Food items</li>
              <li>Precious metals</li>
              <li>Dangerous goods</li>
              <li>Computer parts</li>
            </ul>
            <ul className="list-disc list-inside text-gray-600 space-y-2">
              <li>Plants or animals</li>
              <li>Livestock</li>
              <li>Political material</li>
              <li>Objects of art</li>
              <li>Jewelry</li>
            </ul>
          </div>
        </section>

        {/* Packaging and Labelling */}
        <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center mb-4">
            <Package className="h-6 w-6 text-emerald-500 mr-2" />
            <h2 className="text-2xl font-semibold text-gray-900">Packaging and Labelling Requirements</h2>
          </div>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-emerald-50 p-5 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-3">Standard Packaging</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-emerald-100 text-emerald-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Use appropriate box sizes for contents</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-emerald-100 text-emerald-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Ensure adequate padding and protection</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-emerald-100 text-emerald-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Seal all openings securely</span>
                </li>
              </ul>
            </div>

            <div className="bg-emerald-50 p-5 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-3">Labelling</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-emerald-100 text-emerald-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Clear shipping labels with complete address</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-emerald-100 text-emerald-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Required hazard labels when applicable</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-emerald-100 text-emerald-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Special handling instructions if needed</span>
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Security Measures */}
        <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center mb-4">
            <Shield className="h-6 w-6 text-purple-500 mr-2" />
            <h2 className="text-2xl font-semibold text-gray-900">Security Measures</h2>
          </div>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-purple-50 p-5 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-3">Security Protocols</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-purple-100 text-purple-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Tamper-evident seals on packages</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-purple-100 text-purple-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>GPS tracking for high-value shipments</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-purple-100 text-purple-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Secure storage facilities</span>
                </li>
              </ul>
            </div>

            <div className="bg-purple-50 p-5 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-3">Incident Reporting</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-purple-100 text-purple-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Immediate notification of security breaches</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-purple-100 text-purple-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Documentation of damaged shipments</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-purple-100 text-purple-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Investigation procedures for lost items</span>
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Compliance Notice */}
        <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
          <div className="flex items-center">
            <ScrollText className="h-6 w-6 text-gray-500 mr-3" />
            <p className="text-gray-600">
              <strong>Note:</strong> It is the client's responsibility to ensure compliance with local laws and regulations. 
              Failure to comply may result in shipment delays, confiscation, or legal consequences.
            </p>
          </div>
        </div>

        {/* Handling of Special Shipments */}
        <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center mb-4">
            <Package className="h-6 w-6 text-blue-500 mr-2" />
            <h2 className="text-2xl font-semibold text-gray-900">Handling of Special Shipments</h2>
          </div>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-blue-50 p-5 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-3">Temperature-sensitive Shipments</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Temperature monitoring throughout transit</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Specialized cold chain packaging</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Detailed temperature documentation</span>
                </li>
              </ul>
            </div>

            <div className="bg-blue-50 p-5 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-3">High-value Shipments</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Enhanced security measures</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Real-time tracking and monitoring</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Special handling protocols</span>
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Customer Responsibilities */}
        <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center mb-4">
            <ScrollText className="h-6 w-6 text-orange-500 mr-2" />
            <h2 className="text-2xl font-semibold text-gray-900">Customer Responsibilities</h2>
          </div>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-orange-50 p-5 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-3">Preparation for Shipment</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Proper packaging of items</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Accurate documentation</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Clear shipping instructions</span>
                </li>
              </ul>
            </div>

            <div className="bg-orange-50 p-5 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-3">Compliance with Regulations</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Understanding shipping restrictions</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Following local regulations</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Providing required documentation</span>
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Non-compliance Consequences */}
        <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center mb-4">
            <AlertTriangle className="h-6 w-6 text-red-500 mr-2" />
            <h2 className="text-2xl font-semibold text-gray-900">Non-compliance Consequences</h2>
          </div>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-red-50 p-5 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-3">Penalties and Fines</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-red-100 text-red-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">!</span>
                  <span>Account suspension or termination</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-red-100 text-red-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">!</span>
                  <span>Financial penalties</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-red-100 text-red-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">!</span>
                  <span>Service restrictions</span>
                </li>
              </ul>
            </div>

            <div className="bg-red-50 p-5 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-3">Liability</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-red-100 text-red-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">!</span>
                  <span>Customer responsibility for violations</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-red-100 text-red-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">!</span>
                  <span>Legal consequences</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-red-100 text-red-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">!</span>
                  <span>Compensation for damages</span>
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Review and Update */}
        <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center mb-4">
            <ScrollText className="h-6 w-6 text-indigo-500 mr-2" />
            <h2 className="text-2xl font-semibold text-gray-900">Review and Update</h2>
          </div>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-indigo-50 p-5 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-3">Policy Review</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-indigo-100 text-indigo-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Regular policy reviews and updates</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-indigo-100 text-indigo-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Compliance with changing regulations</span>
                </li>
              </ul>
            </div>

            <div className="bg-indigo-50 p-5 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-3">Stakeholder Communication</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-indigo-100 text-indigo-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Timely notification of changes</span>
                </li>
                <li className="flex items-start">
                  <span className="h-5 w-5 rounded-full bg-indigo-100 text-indigo-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                  <span>Clear communication of updates</span>
                </li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
