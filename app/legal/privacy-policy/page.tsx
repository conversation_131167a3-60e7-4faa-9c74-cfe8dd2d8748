import { Shield, Mail, Lock, FileText, AlertCircle } from "lucide-react";
import Image from "next/image";

export default function PrivacyPolicy() {
  return (
    <div className="max-w-5xl mx-auto px-4 py-12">
      {/* Header */}
      <div className="text-center mb-12">
      <div className="mb-8">
          <Image
            src="/assets/imgs/logo.svg"
            alt="MailPallet Logo"
            width={120}
            height={60}
            className="mx-auto"
            priority
          />
        </div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Privacy Policy</h1>
        <p className="text-lg text-gray-600">How we handle and protect your information</p>
      </div>

      {/* Introduction */}
      <section className="bg-white p-8 rounded-xl shadow-sm border border-gray-200 mb-8">
        <div className="flex items-center mb-6">
          <Shield className="h-6 w-6 text-blue-500 mr-3" />
          <h2 className="text-2xl font-semibold text-gray-900">Introduction</h2>
        </div>
        <p className="text-gray-600 leading-relaxed">
          Welcome to MAILPALLET LTD ("we," "us," or "our"). We are committed to protecting your privacy and ensuring your personal data is handled with care. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website or use our services.
        </p>
      </section>

      {/* Information We Collect */}
      <section className="bg-white p-8 rounded-xl shadow-sm border border-gray-200 mb-8">
        <div className="flex items-center mb-6">
          <FileText className="h-6 w-6 text-green-500 mr-3" />
          <h2 className="text-2xl font-semibold text-gray-900">Information We Collect</h2>
        </div>
        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-800">Personal Information</h3>
            <ul className="list-disc list-inside text-gray-600 space-y-2">
              <li>Name and email address</li>
              <li>Phone number</li>
              <li>Mailing address</li>
              <li>Payment information</li>
            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-800">Business Information</h3>
            <ul className="list-disc list-inside text-gray-600 space-y-2">
              <li>Company name</li>
              <li>Business address</li>
              <li>Contact details</li>
              <li>Other relevant business data</li>
            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-800">Logistics Information</h3>
            <ul className="list-disc list-inside text-gray-600 space-y-2">
              <li>Shipment details</li>
              <li>Tracking numbers</li>
              <li>Delivery addresses</li>
              <li>Related logistics data</li>
            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-800">Usage Data</h3>
            <ul className="list-disc list-inside text-gray-600 space-y-2">
              <li>IP address</li>
              <li>Browser type</li>
              <li>Operating system</li>
              <li>Referring URLs</li>
              <li>Pages visited</li>
            </ul>
          </div>
        </div>
      </section>

      {/* How We Use Your Information */}
      <section className="bg-white p-8 rounded-xl shadow-sm border border-gray-200 mb-8">
        <div className="flex items-center mb-6">
          <FileText className="h-6 w-6 text-orange-500 mr-3" />
          <h2 className="text-2xl font-semibold text-gray-900">How We Use Your Information</h2>
        </div>
        <p className="text-gray-600 mb-4">
          We use the information we collect for various purposes, including:
        </p>
        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-orange-50 p-6 rounded-lg">
            <h3 className="font-semibold text-gray-800 mb-3">Service Delivery</h3>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="h-5 w-5 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                <span className="text-gray-600">Providing and managing our logistics services</span>
              </li>
              <li className="flex items-start">
                <span className="h-5 w-5 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                <span className="text-gray-600">Processing and tracking shipments</span>
              </li>
            </ul>
          </div>
          
          <div className="bg-orange-50 p-6 rounded-lg">
            <h3 className="font-semibold text-gray-800 mb-3">Communication</h3>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="h-5 w-5 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                <span className="text-gray-600">Communicating with you about your shipments</span>
              </li>
              <li className="flex items-start">
                <span className="h-5 w-5 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                <span className="text-gray-600">Providing updates about our services</span>
              </li>
            </ul>
          </div>

          <div className="bg-orange-50 p-6 rounded-lg">
            <h3 className="font-semibold text-gray-800 mb-3">Improvement & Compliance</h3>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="h-5 w-5 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                <span className="text-gray-600">Improving our website and services</span>
              </li>
              <li className="flex items-start">
                <span className="h-5 w-5 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                <span className="text-gray-600">Complying with legal obligations</span>
              </li>
            </ul>
          </div>

          <div className="bg-orange-50 p-6 rounded-lg">
            <h3 className="font-semibold text-gray-800 mb-3">Marketing</h3>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="h-5 w-5 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                <span className="text-gray-600">Marketing and promotional purposes</span>
              </li>
              <li className="flex items-start">
                <span className="h-5 w-5 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">✓</span>
                <span className="text-gray-600">With your consent where required</span>
              </li>
            </ul>
          </div>
        </div>
      </section>

      {/* Disclosure of Your Information */}
      <section className="bg-white p-8 rounded-xl shadow-sm border border-gray-200 mb-8">
        <div className="flex items-center mb-6">
          <FileText className="h-6 w-6 text-teal-500 mr-3" />
          <h2 className="text-2xl font-semibold text-gray-900">Disclosure of Your Information</h2>
        </div>
        <p className="text-gray-600 mb-6">
          We may share your information with:
        </p>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="bg-teal-50 p-6 rounded-lg">
            <h3 className="font-semibold text-gray-800 mb-4 flex items-center">
              <span className="h-8 w-8 rounded-full bg-teal-100 text-teal-500 flex items-center justify-center mr-3">1</span>
              Service Providers
            </h3>
            <p className="text-gray-600">
              Third parties that perform services on our behalf, such as payment processors and shipping companies.
            </p>
          </div>

          <div className="bg-teal-50 p-6 rounded-lg">
            <h3 className="font-semibold text-gray-800 mb-4 flex items-center">
              <span className="h-8 w-8 rounded-full bg-teal-100 text-teal-500 flex items-center justify-center mr-3">2</span>
              Business Partners
            </h3>
            <p className="text-gray-600">
              Partners we collaborate with to offer services and products.
            </p>
          </div>

          <div className="bg-teal-50 p-6 rounded-lg">
            <h3 className="font-semibold text-gray-800 mb-4 flex items-center">
              <span className="h-8 w-8 rounded-full bg-teal-100 text-teal-500 flex items-center justify-center mr-3">3</span>
              Legal Authorities
            </h3>
            <p className="text-gray-600">
              When required by law or to protect our rights, safety, or property.
            </p>
          </div>
        </div>

        <div className="mt-6 bg-teal-50 p-4 rounded-lg border border-teal-100">
          <p className="text-teal-700 text-sm flex items-start">
            <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
            <span>
              We only share your information when necessary and with entities that follow appropriate data protection standards.
            </span>
          </p>
        </div>
      </section>

      {/* Data Security */}
      <section className="bg-white p-8 rounded-xl shadow-sm border border-gray-200 mb-8">
        <div className="flex items-center mb-6">
          <Lock className="h-6 w-6 text-purple-500 mr-3" />
          <h2 className="text-2xl font-semibold text-gray-900">Data Security</h2>
        </div>
        <p className="text-gray-600 leading-relaxed mb-4">
          We implement appropriate technical and organizational measures to protect your personal data against unauthorized access, alteration, disclosure, or destruction.
        </p>
        <div className="bg-purple-50 p-4 rounded-lg">
          <p className="text-purple-700 text-sm">
            <AlertCircle className="h-5 w-5 inline mr-2" />
            While we use industry-standard security measures, no data transmission over the internet can be guaranteed to be 100% secure.
          </p>
        </div>
      </section>

      {/* Your Rights */}
      <section className="bg-white p-8 rounded-xl shadow-sm border border-gray-200 mb-8">
        <div className="flex items-center mb-6">
          <Shield className="h-6 w-6 text-indigo-500 mr-3" />
          <h2 className="text-2xl font-semibold text-gray-900">Your Rights</h2>
        </div>
        <ul className="space-y-3 text-gray-600 mb-6">
          <li className="flex items-start">
            <span className="h-6 w-6 rounded-full bg-indigo-100 text-indigo-500 flex items-center justify-center mr-3 mt-0.5">✓</span>
            <span>Access and receive a copy of your personal data</span>
          </li>
          <li className="flex items-start">
            <span className="h-6 w-6 rounded-full bg-indigo-100 text-indigo-500 flex items-center justify-center mr-3 mt-0.5">✓</span>
            <span>Correct or update your personal data</span>
          </li>
          <li className="flex items-start">
            <span className="h-6 w-6 rounded-full bg-indigo-100 text-indigo-500 flex items-center justify-center mr-3 mt-0.5">✓</span>
            <span>Request deletion of your personal data</span>
          </li>
          <li className="flex items-start">
            <span className="h-6 w-6 rounded-full bg-indigo-100 text-indigo-500 flex items-center justify-center mr-3 mt-0.5">✓</span>
            <span>Object to the processing of your personal data</span>
          </li>
          <li className="flex items-start">
            <span className="h-6 w-6 rounded-full bg-indigo-100 text-indigo-500 flex items-center justify-center mr-3 mt-0.5">✓</span>
            <span>Withdraw consent where processing is based on consent</span>
          </li>
        </ul>
        <div className="bg-indigo-50 p-4 rounded-lg">
          <p className="text-indigo-700 text-sm flex items-start">
            <Mail className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
            <span>
              To exercise these rights, please contact us at{' '}
              <a href="mailto:<EMAIL>" className="underline hover:text-indigo-800">
                <EMAIL>
              </a>
            </span>
          </p>
        </div>
      </section>

      {/* Changes to This Privacy Policy */}
      <section className="bg-white p-8 rounded-xl shadow-sm border border-gray-200 mb-8">
        <div className="flex items-center mb-6">
          <FileText className="h-6 w-6 text-violet-500 mr-3" />
          <h2 className="text-2xl font-semibold text-gray-900">Changes to This Privacy Policy</h2>
        </div>
        <div className="space-y-4 text-gray-600">
          <p>
            We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on our website.
          </p>
          <div className="bg-violet-50 p-4 rounded-lg">
            <p className="text-violet-700 text-sm flex items-start">
              <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
              <span>
                Your continued use of our services after such changes constitutes your acceptance of the new Privacy Policy.
              </span>
            </p>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="bg-gray-50 p-8 rounded-xl border border-gray-200">
        <div className="flex items-center mb-6">
          <Mail className="h-6 w-6 text-gray-500 mr-3" />
          <h2 className="text-2xl font-semibold text-gray-900">Contact Us</h2>
        </div>
        <div className="text-gray-600">
          <p className="mb-2">If you have any questions about this Privacy Policy, please contact us at:</p>
          <p className="font-medium">MAILPALLET LTD</p>
          <p>24-26 Regent Place</p>
          <p>Birmingham, England, B1 3NJ</p>
          <p className="mt-2">
            <a href="mailto:<EMAIL>" className="text-blue-500 hover:text-blue-600">
              <EMAIL>
            </a>
          </p>
        </div>
      </section>
    </div>
  );
}
