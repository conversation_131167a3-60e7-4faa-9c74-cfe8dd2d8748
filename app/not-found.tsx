"use client";

import Link from "next/link";
import { Metada<PERSON> } from "next";
import Header from "@/components/index/header";
import Footer from "@/components/index/footer";
import { But<PERSON> } from "@/components/ui/button";

export const metadata: Metadata = {
  title: "MailPallet - Page Not Found",
};

export default function NotFound() {
  return (
    <>
      <Header />
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)] px-4">
        <h1 className="text-4xl md:text-6xl font-bold text-mp-primary mb-4">404</h1>
        <h2 className="text-2xl md:text-3xl font-semibold text-mp-secondary mb-6">Page Not Found</h2>
        <p className="text-gray-600 text-center mb-8 max-w-md">Oops! The page you're looking for doesn't exist or has been moved.</p>
        <Link href="/">
          <Button>Return Home</Button>
        </Link>
      </div>
      <Footer />
    </>
  );
}
