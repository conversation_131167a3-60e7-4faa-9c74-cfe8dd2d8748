"use client";

import { z } from "zod";
import Link from "next/link";
import Image from "next/image";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { Input } from "@/components/ui/input";
import Header from "@/components/index/header";
import { Button } from "@/components/ui/button";
import Branding from "@/components/app/branding";
import { zodResolver } from "@hookform/resolvers/zod";
import { AuthService } from "@/services/auth.service";
import { ButtonLoading } from "@/components/app/button-loading";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { MPLoading } from "@/components/index/mp-loading";

const formSchema = z.object({
  emailAddress: z
    .string()
    .email({
      message: "Email address must be a valid email address",
    })
    .min(1, {
      message: "Email address is required",
    })
    .transform((val) => val.toLowerCase()),
  password: z.string().min(6, {
    message: "Password must be at least 6 characters",
  }),
});

export default function Login() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
      emailAddress: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    console.log(values);
    setIsLoading(true);
    const authService = new AuthService();
    const loginResult = await authService.login(
      values.emailAddress,
      values.password,
    );

    // If login succeeded, prefer redirecting to a `next` query param if present.
    // This preserves flows like returning to checkout after authentication.
    try {
      if (loginResult && loginResult.result === "success") {
        // Read `next` from the URL query string (if present)
        const next =
          typeof window !== "undefined"
            ? new URLSearchParams(window.location.search).get("next")
            : null;

        if (next) {
          // Navigate to the requested next path (relative or absolute)
          window.location.href = next;
          return;
        }

        // No `next` provided — fall back to the existing post-login behavior
        // which handles admin vs regular user redirecting.
        authService.handleLoginResult(loginResult);
        return;
      }

      // Login did not succeed — reuse existing error handling to get message
      const errorMessage = authService.handleLoginResult(loginResult);
      if (typeof errorMessage === "string") {
        setIsLoading(false);
        toast({
          title: "Oops!",
          variant: "destructive",
          description: errorMessage,
        });
      }
    } catch (err) {
      // Ensure we clear loading state and surface an error if something unexpected happens
      setIsLoading(false);
      toast({
        title: "Error",
        variant: "destructive",
        description:
          err instanceof Error
            ? err.message
            : "An unexpected error occurred during login.",
      });
    }
  };

  return (
    <div className="h-screen w-screen">
      <Header />
      <div className="w-full h-[90%] lg:grid lg:grid-cols-2">
        {/* Left column with image - hidden on mobile */}
        <div className="hidden lg:block">
          <Branding />
        </div>

        {/* Right column with form */}
        <div className="flex flex-col items-center justify-center h-full p-4 lg:p-0">
          {/* Mobile logo and text */}
          <div className="mb-8 text-center lg:hidden">
            <Image
              src="/assets/imgs/logo.svg"
              alt="Logo"
              width={100}
              height={100}
              className="mx-auto mb-2"
            />
            <p className="text-lg font-semibold">Shop it, We Ship it</p>
          </div>

          <div className="w-full max-w-[350px] space-y-6">
            <div className="text-center space-y-2">
              <h1 className="text-2xl font-bold sm:text-3xl">Login</h1>
              <p className="text-sm text-muted-foreground sm:text-base">
                Enter your email below to login to your account
              </p>
            </div>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="emailAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          {...field}
                          inputMode="email"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center justify-between">
                        <FormLabel>Password</FormLabel>
                        <Link
                          href="/forgot"
                          className="text-xs underline sm:text-sm"
                        >
                          Forgot your password?
                        </Link>
                      </div>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="••••••••"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {!isLoading ? (
                  <Button type="submit" className="w-full">
                    Login
                  </Button>
                ) : (
                  <ButtonLoading />
                )}
              </form>
            </Form>
            <div className="text-center text-sm">
              Don&apos;t have an account?{" "}
              <Link href="/signup" className="underline">
                Sign up
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
