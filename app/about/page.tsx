"use client";

import React, { useEffect } from "react";
import Image from "next/image";
import Header from "@/components/index/header";
import Footer from "@/components/index/footer";
import { MPFAQ } from "@/components/index/mp-faq";
import MPCarousel from "@/components/index/mp-carousel";
import { MPMarquee } from "@/components/index/mp-marquee";
import { MPOrbitGlobe } from "@/components/index/mp-orbit-globe";
import { MPAnnouncementBanner } from "@/components/index/mp-announcement-banner";
import { useAuth } from "@/hooks/use-auth";
import { MPLoading } from "@/components/index/mp-loading";

export default function Index() {
  const carouselImages = [
    {
      src: "/assets/imgs/banners/Purchase-UK-Products.png",
      alt: "Shop in the UK. We deliver World Wide.",
    },
    {
      src: "/assets/imgs/banners/Save-90.png",
      alt: "Save 90% on shipping by consolidating your parcels.",
    },
  ];

  const carouselImagesMobile = [
    {
      src: "/assets/imgs/banners/Purchase-UK-Products-Mobile.png",
      alt: "Image 2 description",
    },
    {
      src: "/assets/imgs/banners/Save-90-Mobile.png",
      alt: "Image 1 description",
    },
  ];

  const marqueeImages = [
    {
      src: "/assets/imgs/sliders/Adidas.png",
      alt: "adidas",
      url: "https://www.adidas.co.uk/",
    },
    {
      src: "/assets/imgs/sliders/Amazon.png",
      alt: "amazon",
      url: "https://www.amazon.co.uk/",
    },
    {
      src: "/assets/imgs/sliders/ASOS.png",
      alt: "asos",
      url: "https://www.asos.com/",
    },
    {
      src: "/assets/imgs/sliders/Ebay.png",
      alt: "ebay",
      url: "https://www.ebay.co.uk/",
    },
    {
      src: "/assets/imgs/sliders/Etsy.png",
      alt: "etsy",
      url: "https://www.etsy.com/uk/",
    },
    {
      src: "/assets/imgs/sliders/Frasers_Group.png",
      alt: "fg",
      url: "https://frasers.group/brands",
    },
    {
      src: "/assets/imgs/sliders/H_M.png",
      alt: "hm",
      url: "https://www2.hm.com/en_gb/index.html",
    },
    {
      src: "/assets/imgs/sliders/JD.png",
      alt: "jd",
      url: "https://www.jdsports.co.uk/?gad_source=1&gclid=Cj0KCQiAi_G5BhDXARIsAN5SX7o_0v0tBzDKMJdeodfYEE2K7F9OyedsEOM2Cwzwpc7f3T30j4uVEb4aAncfEALw_wcB",
    },
    {
      src: "/assets/imgs/sliders/John_Lewis.png",
      alt: "jl",
      url: "https://www.johnlewis.com/",
    },
    {
      src: "/assets/imgs/sliders/Next.png",
      alt: "next",
      url: "https://www.next.co.uk/",
    },
    {
      src: "/assets/imgs/sliders/Nike.png",
      alt: "nike",
      url: "https://www.nike.com/gb/",
    },
    {
      src: "/assets/imgs/sliders/OnBuy.png",
      alt: "onbuy",
      url: "https://www.onbuy.com/gb/",
    },
    {
      src: "/assets/imgs/sliders/Shein.png",
      alt: "shein",
      url: "https://www.shein.co.uk/?url_from=ukgooglebrandshein_sheinsheinuk04_srsa_20210124&cid=889108526&setid=59535306602&adid=494298637096&kwd=kwd-320158055436&pf=GOOGLE&gad_source=1&gclid=Cj0KCQiAi_G5BhDXARIsAN5SX7qIvR3ZwHjX0gwh42XQVOJed3fSqjHEqCWDBLlcvxLmNW35bhAkJQYaAqW8EALw_wcB",
    },
    {
      src: "/assets/imgs/sliders/Sports_Direct.png",
      alt: "sport-direct",
      url: "https://www.sportsdirect.com/",
    },
    {
      src: "/assets/imgs/sliders/Temu.png",
      alt: "temu",
      url: "https://www.sportsdirect.com/",
    },
    {
      src: "/assets/imgs/sliders/Zara.png",
      alt: "zara",
      url: "https://www.zara.com/uk/",
    },
  ];

  const orbitGlobeImages = [
    {
      src: "/assets/imgs/sliders/Amazon.png",
      alt: "amazon",
      url: "https://www.amazon.co.uk/",
    },
    {
      src: "/assets/imgs/sliders/Ebay.png",
      alt: "ebay",
      url: "https://www.ebay.co.uk/",
    },
    {
      src: "/assets/imgs/sliders/Temu.png",
      alt: "temu",
      url: "https://www.temu.com/uk",
    },
    {
      src: "/assets/imgs/sliders/Shein.png",
      alt: "shein",
      url: "https://www.shein.co.uk/?url_from=ukgooglebrandshein_sheinsheinuk04_srsa_20210124&cid=889108526&setid=59535306602&adid=494298637096&kwd=kwd-320158055436&pf=GOOGLE&gad_source=1&gclid=Cj0KCQiAoae5BhCNARIsADVLzZfu1clk2u8Z5YTqvnBNTOhh6kIqd9GowG7SzTeBhWNQVFV0_qVCqC0aAhGfEALw_wcB",
    },
  ];

  const announcements = [
    "🚚 We also accept local goods in the UK to our warehouse.",
    "⚡ Special offer: 15% off on shipping. Use code MAILPALLET15",
    "🎉 Coming Soon! Shop online with us and access products from multiple locations, including China, the US, and the UK, with delivery world wide.",
  ];

    // const { redirectIfAuthenticated, isAuthenticated, loading } = useAuth();

    // // Redirect if authenticated
    // useEffect(() => {
    //   const handleRedirect = () => {
    //     redirectIfAuthenticated();
    //   };
    //   handleRedirect();
    // }, [loading, isAuthenticated, redirectIfAuthenticated]);

    // // Show loading while checking authentication
    // if (loading && !isAuthenticated) {
    //   return (
    //     <MPLoading />
    //   )
    // }

  return (
    <div className={`transition-opacity duration-1000 ease-in-out`}>
      <Header />
      <MPAnnouncementBanner announcements={announcements} />
      <article className="flex-grow">
        <div>
          <div>
            <MPCarousel images={carouselImages} mobileImages={carouselImagesMobile} />
          </div>
          <div className="">
            <MPMarquee images={marqueeImages} />
          </div>
          {/* DON'T MOVE WE'LL HANDLE EVERYTHING */}
          <div className="flex flex-col justify-normal bg-mp-background gap-[90px] px-[2%] md:px-[8%]">
            <div className="text-center pt-[20px]">
              <h2 className="text-mp-primary text-[28px] md:text-[42px] font-semibold">Don't Move We'll Handle Everything</h2>
            </div>
            <div className="flex flex-col md:flex-row items-center align-center justify-evenly pb-[10px] gap-[60px] md:gap-[0px]">
              <div className="flex flex-col items-center justify-center text-center">
                <div className="mb-[15px]">
                  <span className="">
                    <svg aria-hidden="true" className="fill-mp-background w-[50px] h-[50px] relative block" viewBox="0 0 448 512" xmlns="http://www.w3.org/2000/svg">
                      <path d="M352 160v-32C352 57.42 294.579 0 224 0 153.42 0 96 57.42 96 128v32H0v272c0 44.183 35.817 80 80 80h288c44.183 0 80-35.817 80-80V160h-96zm-192-32c0-35.29 28.71-64 64-64s64 28.71 64 64v32H160v-32zm160 120c-13.255 0-24-10.745-24-24s10.745-24 24-24 24 10.745 24 24-10.745 24-24 24zm-192 0c-13.255 0-24-10.745-24-24s10.745-24 24-24 24 10.745 24 24-10.745 24-24 24z"></path>
                    </svg>
                  </span>
                </div>
                <div>
                  <h3 className="text-mp-primary text-[1.75em] font-semibold mt-[8px] mb-[16px]">
                    <span>Shop</span>
                  </h3>
                  <p className="text-[#7A7A7A] font-normal">
                    Enjoy shopping in the UK with your free <br />
                    UK address and deliver World Wide.
                  </p>
                </div>
              </div>

              <div className="flex flex-col items-center justify-center text-center">
                <div className="mb-[15px]">
                  <span>
                    <svg aria-hidden="true" className="fill-mp-background w-[50px] h-[50px] relative block" viewBox="0 0 640 512" xmlns="http://www.w3.org/2000/svg">
                      <path d="M624 352h-16V243.9c0-12.7-5.1-24.9-14.1-33.9L494 110.1c-9-9-21.2-14.1-33.9-14.1H416V48c0-26.5-21.5-48-48-48H112C85.5 0 64 21.5 64 48v48H8c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8h272c4.4 0 8 3.6 8 8v16c0 4.4-3.6 8-8 8H40c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8h208c4.4 0 8 3.6 8 8v16c0 4.4-3.6 8-8 8H8c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8h208c4.4 0 8 3.6 8 8v16c0 4.4-3.6 8-8 8H64v128c0 53 43 96 96 96s96-43 96-96h128c0 53 43 96 96 96s96-43 96-96h48c8.8 0 16-7.2 16-16v-32c0-8.8-7.2-16-16-16zM160 464c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48zm320 0c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48zm80-208H416V144h44.1l99.9 99.9V256z"></path>
                    </svg>
                  </span>
                </div>
                <div>
                  <h3 className="text-mp-primary text-[1.75em] font-semibold mt-[8px] mb-[16px]">
                    <span>Ship</span>
                  </h3>
                  <p className="text-[#7A7A7A] font-normal">
                    Once we receive your order, <br />
                    make payment into your personal account.
                  </p>
                </div>
              </div>

              <div className="flex flex-col items-center justify-center text-center">
                <div className="mb-[15px]">
                  <span>
                    <svg aria-hidden="true" className="fill-mp-background w-[50px] h-[50px] relative block" viewBox="0 0 640 512" xmlns="http://www.w3.org/2000/svg">
                      <path d="M425.7 256c-16.9 0-32.8-9-41.4-23.4L320 126l-64.2 106.6c-8.7 14.5-24.6 23.5-41.5 23.5-4.5 0-9-.6-13.3-1.9L64 215v178c0 14.7 10 27.5 24.2 31l216.2 54.1c10.2 2.5 20.9 2.5 31 0L551.8 424c14.2-3.6 24.2-16.4 24.2-31V215l-137 39.1c-4.3 1.3-8.8 1.9-13.3 1.9zm212.6-112.2L586.8 41c-3.1-6.2-9.8-9.8-16.7-8.9L320 64l91.7 152.1c3.8 6.3 11.4 9.3 18.5 7.3l197.9-56.5c9.9-2.9 14.7-13.9 10.2-23.1zM53.2 41L1.7 143.8c-4.6 9.2.3 20.2 10.1 23l197.9 56.5c7.1 2 14.7-1 18.5-7.3L320 64 69.8 32.1c-6.9-.8-13.5 2.7-16.6 8.9z"></path>
                    </svg>
                  </span>
                </div>
                <div>
                  <h3 className="text-mp-primary text-[1.75em] font-semibold mt-[8px] mb-[16px]">
                    <span>Enjoy</span>
                  </h3>
                  <p className="text-[#7A7A7A] font-normal">
                    Receive your order within <br />7 - 10* business days
                  </p>
                </div>
              </div>
            </div>
          </div>
          {/* All your favourite brands in one click section */}
          <div className="flex flex-col-reverse md:flex-row items-center justify-around px-[8%] text-center space-y-[2em] md:space-y-0 md:space-x-[5em]">
            <div className="w-full md:w-2/4">
              <MPOrbitGlobe images={orbitGlobeImages} />
            </div>
            <div className="w-full md:w-2/4">
              <p className="text-3xl md:text-4xl font-semibold">All Your Favourite Brands In One Click.</p>
            </div>
          </div>
          {/* The process is easy section */}
          <div className="mx-auto px-4 sm:px-6 lg:px-8 py-12 bg-[#F2FCFE]">
            <h2 className="text-mp-primary text-3xl sm:text-4xl lg:text-5xl font-semibold text-center mb-12">The process is easy!</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
              {[
                {
                  step: 1,
                  title: "Free UK Delivery Address",
                  description: "Shop for your product and deliver it to our warehouse.",
                },
                {
                  step: 2,
                  title: "Pay For Physical Weight Only",
                  description: "Save on shipping by paying for the physical weight only and not for the parcel size.",
                },
                {
                  step: 3,
                  title: "Package Consolidation",
                  description: "Shop on multiple sites and combine your packages to save on shipping.",
                },
                {
                  step: 4,
                  title: "Dedicated Customer Service",
                  description: "Our friendly customer support team is ready to help.",
                },
              ].map(({ step, title, description }) => (
                <div key={step} className="flex flex-col items-center p-6 bg-white rounded-lg shadow-md transition-transform hover:scale-105 h-full">
                  <div className="w-12 h-12 bg-black text-white rounded-full flex items-center justify-center mb-4 font-bold text-xl">{step}</div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-3 flex items-center justify-center h-16 text-center w-full">{title}</h3>
                  <p className="text-sm text-gray-600 mt-auto self-start">{description}</p>
                </div>
              ))}
            </div>
          </div>
          {/* That is how it feels section */}
          <div className="mx-auto px-[8%] py-[2%]">
            <h2 className="text-mp-primary text-[28px] md:text-[42px] font-semibold text-center">That is how it feels!</h2>
            <div className="mt-[50px] md:mt-[50px] flex flex-col items-center justify-center">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 w-full max-w-[1200px]">
                <Image src="/assets/imgs/How-It-Feels-01.png" alt="how it feels" width={300} height={300} className="w-full h-auto object-cover" />
                <Image src="/assets/imgs/How-It-Feels-02.png" alt="how it feels" width={300} height={300} className="w-full h-auto object-cover" />
                <Image src="/assets/imgs/How-It-Feels-03.png" alt="how it feels" width={300} height={300} className="w-full h-auto object-cover" />
                <Image src="/assets/imgs/How-It-Feels-04.png" alt="how it feels" width={300} height={300} className="w-full h-auto object-cover" />
              </div>
            </div>
          </div>

          {/* We've helped thousands of people section */}
          <div className="mx-auto px-[8%] py-[5%] bg-mp-background">
            <h2 className="text-mp-primary text-[28px] md:text-[42px] font-semibold text-center">We've helped thousands of people!</h2>
            <MPFAQ />
          </div>
        </div>
      </article>
      <Footer />
    </div>
  );
}
