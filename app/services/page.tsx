"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import Footer from "@/components/index/footer";
import Header from "@/components/index/header";
import { Button } from "@/components/ui/button";
import { MPMeteors } from "@/components/index/mp-meteors";

export default function Services() {
  const router = useRouter();

  const services = [
    {
      id: 1,
      title: "Shop Online UK Stores, Ship Worldwide",
      description:
        "Shop on any online store available in the UK and order items to our warehouse address. As long as they ship to the UK, you can order from any site in the UK and we'll deliver to Africa, India, Pakistan, and UAE!",
      image: "/assets/imgs/shop-online-ship-worldwide.webp",
      features: ["Free UK warehouse address", "Shop from thousands of UK retailers", "Global delivery coverage", "Package consolidation available"],
      buttonText: "Start Shopping",
      gradient: "from-blue-500 to-purple-600",
    },
    {
      id: 2,
      title: "Send Local Goods From UK Worldwide",
      description:
        "Already have goods in the UK? Register with us and send them to our address. Once we receive your items, we'll ship them to your destination. It's that simple and efficient!",
      image: "/assets/imgs/send-local-goods.webp",
      features: ["Local UK goods shipping", "Secure warehouse handling", "Fast processing times", "Tracking from pickup to delivery"],
      buttonText: "Send Goods",
      gradient: "from-green-500 to-teal-600",
    },
  ];

  const faqItems = [
    {
      question: "Do we have a physical office?",
      answer:
        "No, we operate digitally which allows us to eliminate additional overhead costs. This enables us to pass the savings directly to you, offering the most competitive rates available in the market.",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
          />
        </svg>
      ),
    },
    {
      question: "Which countries do you ship to?",
      answer:
        "Our shipping services currently cover multiple African countries including Ghana and Nigeria, plus we now deliver to India, Pakistan, and UAE. We're constantly expanding our reach.",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      ),
    },
    {
      question: "How long does shipping usually take?",
      answer:
        "Shipping times vary by destination and method. Typically 7-10 business days for express air shipping and 4-8 weeks for economical sea shipping. We provide tracking for all shipments.",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
    {
      question: "How are shipping rates calculated?",
      answer:
        "Our competitive shipping rates depend on package weight and destination. Use our online shipping calculator for instant estimates, or contact our team for custom quotes on large shipments.",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 012-2v16a2 2 0 01-2 2z"
          />
        </svg>
      ),
    },
    {
      question: "Do you offer package consolidation?",
      answer: "Yes! You can shop from multiple UK stores and we'll consolidate your packages into one shipment, saving you up to 90% on shipping costs compared to individual shipments.",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
    },
    {
      question: "Is my package insured during shipping?",
      answer:
        "We offer comprehensive insurance options for your peace of mind. All packages are handled with care, and we provide various insurance levels to protect your valuable items during transit.",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
          />
        </svg>
      ),
    },
  ];

  const stats = [
    { number: "50K+", label: "Happy Customers", icon: "👥" },
    { number: "90%", label: "Shipping Savings", icon: "💰" },
    { number: "7-10", label: "Days Delivery", icon: "🚚" },
    { number: "24/7", label: "Customer Support", icon: "🛟" },
  ];

  return (
    <>
      <Header />
      <article className="flex-grow">
        {/* Hero Section */}
        <div className="relative text-white overflow-hidden" style={{ backgroundColor: "rgb(19,28,77)" }}>
          <div className="relative z-10 px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">Our Services</h1>
              <p className="text-lg sm:text-xl mb-8 opacity-90 leading-relaxed">Comprehensive shipping solutions connecting you to UK retailers with worldwide delivery</p>

              {/* Stats */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
                {stats.map((stat, index) => (
                  <div key={index} className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 hover:bg-white/20 transition-all duration-300">
                    <div className="text-2xl mb-2">{stat.icon}</div>
                    <div className="text-2xl font-bold">{stat.number}</div>
                    <div className="text-sm opacity-80">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Services Section */}
        <div className="py-16 lg:py-24 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4" style={{ color: "rgb(19,28,77)" }}>
                What We Offer
              </h2>
              <p className="text-lg max-w-2xl mx-auto" style={{ color: "rgba(19,28,77,0.8)" }}>
                Professional shipping services designed to make international shopping effortless
              </p>
            </div>

            <div className="space-y-16">
              {services.map((service, index) => (
                <div key={service.id} className={`flex flex-col ${index % 2 === 0 ? "lg:flex-row" : "lg:flex-row-reverse"} items-center gap-12`}>
                  {/* Image */}
                  <div className="lg:w-1/2">
                    <div className="relative group">
                      <div
                        className="absolute -inset-4 bg-gradient-to-r opacity-30 blur-lg transition-all duration-300 group-hover:opacity-50 rounded-2xl"
                        style={{ background: `linear-gradient(135deg, rgb(19,28,77), rgba(19,28,77,0.6))` }}
                      ></div>
                      <div className="relative bg-white rounded-2xl overflow-hidden shadow-xl">
                        <Image src={service.image} alt={service.title} width={600} height={400} className="w-full h-80 object-contain mx-auto" />
                      </div>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="lg:w-1/2 space-y-6">
                    <h3 className="text-2xl sm:text-3xl lg:text-4xl font-bold leading-tight" style={{ color: "rgb(19,28,77)" }}>
                      {service.title}
                    </h3>
                    <p className="text-lg leading-relaxed" style={{ color: "rgba(19,28,77,0.8)" }}>
                      {service.description}
                    </p>

                    {/* Features */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {service.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-3">
                          <div className="w-5 h-5 rounded-full flex items-center justify-center" style={{ backgroundColor: "rgb(19,28,77)" }}>
                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                          <span className="text-sm font-medium" style={{ color: "rgba(19,28,77,0.7)" }}>
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>

                    {/* CTA Button */}
                    <div className="pt-4">
                      <button
                        onClick={() => router.push("/signup")}
                        className="px-8 py-3 text-white rounded-lg font-semibold text-lg hover:opacity-90 transition-all duration-300 hover:scale-105 shadow-lg"
                        style={{ backgroundColor: "rgb(19,28,77)" }}
                      >
                        {service.buttonText}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="py-16 lg:py-24" style={{ backgroundColor: "rgba(19,28,77,0.05)" }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4" style={{ color: "rgb(19,28,77)" }}>
                Frequently Asked Questions
              </h2>
              <p className="text-lg max-w-2xl mx-auto" style={{ color: "rgba(19,28,77,0.8)" }}>
                Everything you need to know about our shipping services
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {faqItems.map((item, index) => (
                <div key={index} className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center text-white" style={{ backgroundColor: "rgb(19,28,77)" }}>
                      {item.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold mb-3" style={{ color: "rgb(19,28,77)" }}>
                        {item.question}
                      </h3>
                      <p className="text-sm leading-relaxed" style={{ color: "rgba(19,28,77,0.7)" }}>
                        {item.answer}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="py-16 text-white" style={{ backgroundColor: "rgb(19,28,77)" }}>
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">Ready to Start Shipping?</h2>
            <p className="text-lg sm:text-xl mb-8 opacity-90">Join thousands of satisfied customers and get your free UK address today</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => router.push("/signup")}
                className="px-8 py-4 bg-white rounded-lg font-semibold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg hover:scale-105"
                style={{ color: "rgb(19,28,77)" }}
              >
                Get Started Now
              </button>
              <button
                className="px-8 py-4 border-2 border-white text-white rounded-lg font-semibold text-lg hover:bg-white transition-all duration-300"
                onMouseEnter={(e) => ((e.target as HTMLElement).style.color = "rgb(19,28,77)")}
                onMouseLeave={(e) => ((e.target as HTMLElement).style.color = "white")}
                onClick={() => window.open("mailto:<EMAIL>", "_self")}
              >
                Contact Support
              </button>
            </div>
          </div>
        </div>
      </article>
      <Footer />
    </>
  );
}
