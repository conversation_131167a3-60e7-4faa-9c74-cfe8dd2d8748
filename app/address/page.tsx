"use client";

import { z } from "zod";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Branding from "@/components/app/branding";
import { zodResolver } from "@hookform/resolvers/zod";
import { ButtonLoading } from "@/components/app/button-loading";
import { AddressService, UserAddressModel } from "@/services/address.service";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectGroup, SelectLabel, SelectItem } from "@/components/ui/select";
import { countryList } from "@/lib/constants";

const formSchema = z.object({
  company: z.string(),
  address: z.string().min(1, {
    message: "Address is required",
  }),
  city: z.string().min(1, {
    message: "City is required",
  }),
  state: z.string().min(1, {
    message: "State is required",
  }),
  postalCode: z.string(),
  deliverTo: z.string().min(1, {
    message: "Delivery country is required",
  }),
  firstName: z.string(),
  lastName: z.string(),
  email: z.string(),
});

export default function Address() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      city: "",
      state: "",
      address: "",
      company: "",
      deliverTo: "",
      postalCode: "",
      firstName: "",
      lastName: "",
      email: "",
    },
  });

  useEffect(() => {
    form.setValue("firstName", sessionStorage.getItem("first_name") ?? "");
    form.setValue("lastName", sessionStorage.getItem("last_name") ?? "");
    form.setValue("email", sessionStorage.getItem("email_address") ?? "");
  }, []);

  const getAddressData = (values: z.infer<typeof formSchema>): UserAddressModel => {
    return {
      FirstName: values.firstName,
      LastName: values.lastName,
      EmailAddress: values.email,
      Company: values.company,
      Address: values.address,
      City: values.city,
      StateProvince: values.state,
      PostalCode: values.postalCode,
      DeliverTo: values.deliverTo,
    };
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setLoading(true);
    const addressService = new AddressService();
    const addressData = getAddressData(values);
    const result = await addressService.createNewAddress(addressData);
    setLoading(false);
    if (typeof result === "string") {
      toast({
        title: "Oops!",
        description: result,
        variant: "destructive",
      });
    }
    if (result === true) {
      window.location.href = "/home";
    }
  };

  return (
    <div className="w-full min-h-screen lg:grid lg:grid-cols-2">
      {/* Left column with image */}
      <div className="hidden lg:block">
        <Branding />
      </div>

      {/* Right column with form */}
      <div className="flex items-center justify-center h-full">
        <div className="mx-auto grid w-[450px] gap-6">
          <div className="grid gap-2 text-center">
            <h1 className="text-3xl font-bold">Setup Your Address</h1>
            <div className="items-start">
              <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
                <p className="text-blue-700 text-start">
                  <strong>Important Notice!</strong> <br /> <br />
                  The address at the time we receive the parcel in our warehouse is the address we will use when delivering. Please ensure the delivery address below is correct before
                  placing an order.
                </p>
              </div>
            </div>
          </div>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid gap-4">
                <FormField
                  control={form.control}
                  name="deliverTo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Delivery Country</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Deliver To" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Countries</SelectLabel>
                            {countryList.map((country) => (
                              <SelectItem key={country.value} value={country.value}>
                                {country.label}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="company"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>City</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>State/Province</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="postalCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Postal Code</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {!loading ? (
                  <Button type="submit" className="w-full mt-4">
                    Complete Registration
                  </Button>
                ) : (
                  <ButtonLoading />
                )}
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
