"use client";

import { z } from "zod";
import Image from "next/image";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";
import Header from "@/components/index/header";
import { Button } from "@/components/ui/button";
import Branding from "@/components/app/branding";
import { zodResolver } from "@hookform/resolvers/zod";
import { AuthService } from "@/services/auth.service";
import { ButtonLoading } from "@/components/app/button-loading";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { supabase } from "@/lib/supabase_client";
import { isDevEnvironment } from "@/lib/utils";

const formSchema = z.object({
  emailAddress: z
    .string()
    .email({
      message: "Email address must be a valid email address",
    })
    .min(1, {
      message: "Email address is required",
    }),
});

export default function Forgot() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      emailAddress: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    console.log(values);
    setIsLoading(true);
    const authService = new AuthService();
    const userExists = await authService.checkUserExists(values.emailAddress);
    if (!userExists) {
      setIsLoading(false);
      toast({
        title: "Oops!",
        variant: "destructive",
        description: "There is no account associated with the email address provided.",
      });
    } else {
      toast({
        title: "Congratulations",
        description: "Kindly check your email address for a link to reset your password",
      });
    }
  };

  return (
    <div className="h-screen w-screen">
      <Header />
      <div className="w-full h-[90%] lg:grid lg:grid-cols-2">
        {/* Left column with image - hidden on mobile */}
        <div className="hidden lg:block">
          <Branding />
        </div>

        {/* Right column with form */}
        <div className="flex flex-col items-center justify-center h-full p-4 lg:p-0">
          {/* Mobile logo and text */}
          <div className="mb-8 text-center lg:hidden">
            <Image src="/assets/imgs/logo.svg" alt="Logo" width={100} height={100} className="mx-auto mb-2" />
            <p className="text-lg font-semibold">Shop it, We Ship it</p>
          </div>

          <div className="w-full max-w-[350px] space-y-6">
            <div className="text-center space-y-2">
              <h1 className="text-2xl font-bold sm:text-3xl">Forgot Password</h1>
              <p className="text-sm text-muted-foreground sm:text-base">Enter the email address you registered with.</p>
            </div>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="emailAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} inputMode="email" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {!isLoading ? (
                  <Button type="submit" className="w-full">
                    Verify Account
                  </Button>
                ) : (
                  <ButtonLoading />
                )}
              </form>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
}
