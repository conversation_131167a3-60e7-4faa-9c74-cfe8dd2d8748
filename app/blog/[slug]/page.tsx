import fs from "fs";
import path from "path";
import matter from "gray-matter";
import { notFound } from "next/navigation";
import ReactMarkdown from "react-markdown";
import { Metadata } from "next";

interface BlogPostProps {
  params: {
    slug: string;
  };
}

interface PostData {
  frontMatter: {
    title: string;
    date: string;
    description: string;
    keywords: string[];
    author: string;
  };
  content: string;
}

async function getPost(slug: string): Promise<PostData | null> {
  try {
    const markdownWithMeta = fs.readFileSync(path.join("posts", `${slug}.md`), "utf-8");
    const { data: frontMatter, content } = matter(markdownWithMeta);
    return { frontMatter, content } as PostData;
  } catch (e) {
    return null;
  }
}

function generateArticleSchema(post: PostData, slug: string) {
  return {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    headline: post.frontMatter.title,
    description: post.frontMatter.description,
    keywords: post.frontMatter.keywords.join(", "),
    datePublished: post.frontMatter.date,
    dateModified: post.frontMatter.date,
    author: {
      "@type": "Person",
      name: post.frontMatter.author,
    },
    publisher: {
      "@type": "Organization",
      name: "MailPallet",
      logo: {
        "@type": "ImageObject",
        url: "https://mailpallet.com/logo.png",
      },
    },
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": `https://mailpallet.com/blog/${slug}`,
    },
  };
}

export async function generateStaticParams() {
  const files = fs.readdirSync(path.join("posts"));
  return files.map((filename) => ({
    slug: filename.replace(".md", ""),
  }));
}

export async function generateMetadata({ params }: BlogPostProps): Promise<Metadata> {
  const post = await getPost(params.slug);

  if (!post) {
    return {
      title: "Post Not Found | MailPallet",
      robots: {
        index: false,
        follow: false,
      },
    };
  }

  return {
    title: `${post.frontMatter.title} | MailPallet`,
    description: post.frontMatter.description,
    keywords: post.frontMatter.keywords,
    authors: [{ name: post.frontMatter.author }],
    openGraph: {
      title: post.frontMatter.title,
      description: post.frontMatter.description,
      type: "article",
      publishedTime: post.frontMatter.date,
      authors: [post.frontMatter.author],
      url: `https://mailpallet.com/blog/${params.slug}`,
      siteName: "MailPallet Blog",
    },
    twitter: {
      card: "summary_large_image",
      title: post.frontMatter.title,
      description: post.frontMatter.description,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    alternates: {
      canonical: `https://mailpallet.com/blog/${params.slug}`,
    },
  };
}

export default async function BlogPost({ params }: BlogPostProps) {
  const post = await getPost(params.slug);

  if (!post) {
    notFound();
  }

  const { frontMatter, content } = post;

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generateArticleSchema(post, params.slug)),
        }}
      />
      <div className="min-h-screen bg-gray-50 py-16">
        <article className="max-w-3xl mx-auto px-6">
          <header className="mb-12 text-center">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight">{frontMatter.title}</h1>

            <div className="flex items-center justify-center space-x-4 text-gray-600">
              <div className="flex items-center">
                <span className="inline-flex items-center justify-center h-10 w-10 rounded-full bg-blue-100 text-blue-600 font-medium text-lg">{frontMatter.author.charAt(0)}</span>
                <span className="ml-2 font-medium">{frontMatter.author}</span>
              </div>
              <span className="text-gray-300">•</span>
              <time dateTime={frontMatter.date} className="font-medium">
                {new Date(frontMatter.date).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </time>
            </div>
          </header>

          <div className="prose prose-lg max-w-none mx-auto bg-white rounded-xl shadow-sm p-8 sm:p-12">
            <ReactMarkdown
              components={{
                // Customize heading styles
                h1: ({ children }) => <h1 className="text-4xl font-bold text-gray-900 mt-8 mb-6">{children}</h1>,
                h2: ({ children }) => <h2 className="text-3xl font-bold text-gray-900 mt-12 mb-6">{children}</h2>,
                h3: ({ children }) => <h3 className="text-2xl font-bold text-gray-900 mt-8 mb-4">{children}</h3>,
                // Customize paragraph styles
                p: ({ children }) => <p className="text-gray-600 leading-relaxed mb-6">{children}</p>,
                // Customize list styles
                ul: ({ children }) => <ul className="list-disc pl-6 mb-6 space-y-2 text-gray-600">{children}</ul>,
                ol: ({ children }) => <ol className="list-decimal pl-6 mb-6 space-y-2 text-gray-600">{children}</ol>,
                li: ({ children }) => <li className="text-gray-600 leading-relaxed">{children}</li>,
                // Customize blockquote styles
                blockquote: ({ children }) => <blockquote className="border-l-4 border-blue-200 pl-6 py-2 my-6 italic text-gray-700">{children}</blockquote>,
                // Customize strong/bold text
                strong: ({ children }) => <strong className="font-semibold text-gray-900">{children}</strong>,
                // Customize links
                a: ({ href, children }) => (
                  <a href={href} className="text-blue-600 hover:underline font-medium" target="_blank" rel="noopener noreferrer">
                    {children}
                  </a>
                ),
                // Add proper spacing for horizontal rules
                hr: () => <hr className="my-8 border-gray-200" />,
                // Customize code blocks
                code: ({ className, children, inline, ...props }: any) => {
                  if (inline) {
                    return (
                      <code className="bg-gray-100 rounded px-1 py-0.5 text-sm font-mono text-gray-800" {...props}>
                        {children}
                      </code>
                    );
                  }
                  return (
                    <pre className="bg-gray-100 rounded-lg p-4 overflow-x-auto mb-6">
                      <code className="text-sm font-mono text-gray-800" {...props}>
                        {children}
                      </code>
                    </pre>
                  );
                },
              }}
              className="
                prose-headings:text-gray-900 prose-headings:font-bold 
                prose-h2:text-3xl prose-h2:mt-12 prose-h2:mb-6
                prose-h3:text-2xl prose-h3:mt-10 prose-h3:mb-4
                prose-p:text-gray-600 prose-p:leading-relaxed prose-p:mb-6
                prose-a:text-blue-600 prose-a:font-medium prose-a:no-underline hover:prose-a:underline
                prose-strong:text-gray-900 prose-strong:font-semibold
                prose-ul:my-6 prose-ul:list-disc prose-ul:pl-6
                prose-ol:my-6 prose-ol:list-decimal prose-ol:pl-6
                prose-li:text-gray-600 prose-li:mb-2
                prose-blockquote:border-l-4 prose-blockquote:border-blue-200 
                prose-blockquote:pl-6 prose-blockquote:italic prose-blockquote:text-gray-700
                prose-img:rounded-lg prose-img:shadow-md
                prose-pre:bg-gray-100 prose-pre:rounded-lg prose-pre:p-4
                prose-code:text-gray-800 prose-code:bg-gray-100 prose-code:rounded
                prose-code:px-1 prose-code:py-0.5 prose-code:text-sm prose-code:font-mono
              "
            >
              {content}
            </ReactMarkdown>
          </div>
        </article>
      </div>
    </>
  );
}
