import fs from "fs";
import path from "path";
import matter from "gray-matter";
import { Metadata } from "next";
import BlogContent from "@/app/blog/BlogContent";

interface Post {
  slug: string;
  title: string;
  date: string;
  description: string;
  author: string;
}

async function getBlogPosts(): Promise<Post[]> {
  const files = fs.readdirSync(path.join("posts"));

  const posts = files.map((filename) => {
    const slug = filename.replace(".md", "");
    const markdownWithMeta = fs.readFileSync(path.join("posts", filename), "utf-8");
    const { data: frontMatter } = matter(markdownWithMeta);

    return {
      slug,
      title: frontMatter.title as string,
      date: frontMatter.date as string,
      description: frontMatter.description as string,
      author: frontMatter.author as string,
    };
  });

  return posts.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
}

function generateBlogListSchema(posts: Post[]) {
  return {
    "@context": "https://schema.org",
    "@type": "Blog",
    name: "MailPallet Blog",
    description: "Latest insights about shipping and logistics",
    blogPost: posts.map((post) => ({
      "@type": "BlogPosting",
      headline: post.title,
      description: post.description,
      datePublished: post.date,
      author: {
        "@type": "Person",
        name: post.author,
      },
      url: `https://mailpallet.com/blog/${post.slug}`,
    })),
  };
}

export const metadata: Metadata = {
  title: "Blog Posts | MailPallet",
  description: "Latest insights about shipping and logistics",
  openGraph: {
    title: "Blog Posts | MailPallet",
    description: "Latest insights about shipping and logistics",
    type: "website",
    url: "https://mailpallet.com/blog",
    siteName: "MailPallet Blog",
  },
  twitter: {
    card: "summary_large_image",
    title: "Blog Posts | MailPallet",
    description: "Latest insights about shipping and logistics",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  alternates: {
    canonical: "https://mailpallet.com/blog",
  },
};

export default async function BlogPage() {
  const posts = await getBlogPosts();

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generateBlogListSchema(posts)),
        }}
      />
      <main className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-6 py-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4 text-center">Latest Articles</h1>
          <p className="text-xl text-gray-600 text-center mb-16">Insights and updates from the MailPallet team</p>
          <BlogContent posts={posts} />
        </div>
      </main>
    </>
  );
}
