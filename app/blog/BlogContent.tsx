"use client";

import Link from "next/link";

interface Post {
  slug: string;
  title: string;
  date: string;
  description: string;
  author: string;
}

export default function BlogContent({ posts }: { posts: Post[] }) {
  return (
    <div className="grid gap-12">
      {posts.map((post) => (
        <article key={post.slug} className="group relative bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden">
          <Link href={`/blog/${post.slug}`} className="block p-8">
            <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
              <div className="flex items-center gap-2">
                <span className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 text-blue-600 font-medium">{post.author.charAt(0)}</span>
                <span>{post.author}</span>
              </div>
              <span className="text-gray-300">•</span>
              <time dateTime={post.date} className="flex items-center gap-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {new Date(post.date).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </time>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-200">{post.title}</h2>

            <p className="text-gray-600 leading-relaxed mb-6">{post.description}</p>

            <div className="flex items-center text-blue-600 font-medium">
              Read more
              <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </div>
          </Link>
        </article>
      ))}
    </div>
  );
}
