"use client";
import Header from "@/components/index/header";

export default function HomeLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="bg-white text-black">
      {/* Site header */}
      <Header />
      {/* Main content area remains the same */}
      {/* className={`w-full ${pathname === "/home/<USER>" || pathname === "/home/<USER>" ? "max-w-[1050px]" : "max-w-[800px]"} mx-auto px-4 md:px-0`} */}
      <div>
        <main>{children}</main>
      </div>
    </div>
  );
}
