"use client";

import { Copy, Info } from "lucide-react";
import React, { useEffect, useState } from "react";
import { HomeSkeleton } from "@/components/skeletons/home";
import { UserAddressModel } from "@/services/address.service";
import { AddressService, MailpalletLocation } from "@/services/address.service";

const AddressField = ({ label, value }: { label: string; value: string }) => {
  const [showTooltip, setShowTooltip] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(value);
    setShowTooltip(true);
    setTimeout(() => setShowTooltip(false), 2000);
  };

  return (
    <div className="flex justify-between items-center py-4 relative">
      <span className="text-gray-600 font-medium">{label}:</span>
      <div className="flex items-center gap-3">
        <span className="text-gray-800 font-semibold text-right">{value}</span>
        <button onClick={handleCopy} className="text-gray-500 hover:text-gray-700 transition-colors relative" aria-label={`Copy ${label}`}>
          <Copy size={18} className="stroke-2" />
          {showTooltip && <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg">Copied!</div>}
        </button>
      </div>
    </div>
  );
};

export default function Home() {
  const [loading, setLoading] = useState(true);
  const [addressDetails, setAddressDetails] = useState<UserAddressModel | null>(null);
  const [locationDetails, setLocationDetails] = useState<MailpalletLocation | null>(null);

  const getAddressDetails = async () => {
    setLoading(true);
    const addressService = new AddressService();
    const userAddressData = await addressService.fetchUserAddress();
    const locationsData = await addressService.fetchUserLocations();

    setAddressDetails(userAddressData);
    if (locationsData.length > 0) {
      setLocationDetails(locationsData[0]);
    }
    setLoading(false);
  };

  useEffect(() => {
    const fetchData = async () => {
      getAddressDetails();
    };
    fetchData();
  }, []);

  return loading ? (
    <HomeSkeleton />
  ) : (
    <div className="w-full md:w-[800px] mx-auto p-6 bg-white">
      <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200">
        <div className="bg-gray-800 text-white p-6">
          <h2 className="text-3xl font-bold">UK Warehouse Address</h2>
          <p className="mt-2 text-gray-300">Use this Address to deliver to our warehouse</p>
        </div>

        <div className="p-6">
          <div className="bg-gray-100 border-l-4 border-gray-800 p-4 mb-6 rounded-r-lg">
            <p className="text-gray-700 flex items-center">
              <Info size={20} className="mr-2 flex-shrink-0" />
              <span>
                <strong>Note:</strong> This is your dedicated UK shipping address for all your orders.
              </span>
            </p>
          </div>

          <div className="space-y-3 divide-y divide-gray-200">
            <AddressField label="First Name" value={addressDetails?.FirstName || ""} />
            <AddressField label="Last Name" value={addressDetails?.LastName || ""} />
            <AddressField label="Address Line 1" value={locationDetails?.address_line_one || ""} />
            <AddressField label="Address Line 2" value={`${locationDetails?.address_line_two || ""} MailPallet${addressDetails?.user_id?.toString()}`.trim()} />
            <AddressField label="City" value={locationDetails?.city || ""} />
            <AddressField label="Post Code" value={locationDetails?.postcode || ""} />
            <AddressField label="Country" value={locationDetails?.country || ""} />
            <AddressField label="Phone Number" value={locationDetails?.phone_number || ""} />
          </div>

          <div className="mt-6 flex items-center text-gray-600 text-sm bg-gray-100 p-3 rounded-lg">
            <Info size={18} className="mr-2 flex-shrink-0" />
            <span>Phone number format: Country code + number</span>
          </div>
        </div>
      </div>
    </div>
  );
}
