import type { Metadata } from "next";
import Footer from "@/components/index/footer";
import CurrencyProvider from "@/components/app/CurrencyProvider";
import dynamic from "next/dynamic";

export const metadata: Metadata = {
  title: "Shop UK Products | MailPallet - Electronics, Fashion, Beauty & More",
  description: "Discover thousands of UK products including electronics, fashion, beauty, tools, baby items and more. Free UK address, package consolidation, and fast delivery to Ghana & Nigeria. Shop Samsung, Apple, Nike, ASOS, and top UK brands.",
  keywords: "uk products, electronics, fashion, beauty products, baby items, tools, samsung, apple, nike, asos, uk brands, ghana delivery, nigeria delivery, lagos, accra",
  openGraph: {
    title: "Shop UK Products - Electronics, Fashion, Beauty | MailPallet",
    description: "Browse thousands of UK products with delivery to Ghana & Nigeria. Electronics, fashion, beauty & more from top brands.",
    type: "website",
  },
  twitter: {
    title: "Shop UK Products | MailPallet Ecommerce",
    description: "Electronics, fashion, beauty products from UK with delivery to Ghana & Nigeria. Browse thousands of products now!",
  },
};

const CurrencyFab = dynamic(() => import("@/components/app/CurrencyFab"), {
  ssr: false,
});

export default function ShopLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Main content area */}
      <div className="flex-1">
        <CurrencyProvider>
          {children}
          <CurrencyFab />
        </CurrencyProvider>
      </div>
      {/* Footer */}
      <Footer />
    </div>
  );
}
