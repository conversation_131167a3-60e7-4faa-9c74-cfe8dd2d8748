import { formatDate } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import { ParcelModel } from "@/data/models/parcel.model";
import ShippingDetails from "@/components/app/shipping-details";
import ParcelFileUpload from "@/components/app/parcel-file-upload";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

interface ParcelCardProps {
  userEmail: string;
  packages: ParcelModel[];
  isSelecting?: boolean;
  selectedParcels?: number[];
  onParcelSelect?: (id: number) => void;
}

const formatConsolidationStatus = (status: number) => {
  const statusMap: { [key: string]: { text: string; classes: string } } = {
    1: {
      text: "Pending",
      classes: "bg-yellow-100 text-yellow-800",
    },
    2: {
      text: "Approved",
      classes: "bg-blue-100 text-blue-800",
    },
    3: {
      text: "Completed",
      classes: "bg-green-100 text-green-800",
    },
    4: {
      text: "Rejected",
      classes: "bg-red-100 text-red-800",
    },
    5: {
      text: "Cancelled",
      classes: "bg-gray-100 text-gray-800",
    },
  };

  return statusMap[status] || { text: status, classes: "bg-gray-100 text-gray-800" };
};

export function ParcelCard({ userEmail, packages, isSelecting = false, selectedParcels = [], onParcelSelect }: ParcelCardProps) {
  const handleFileUploadSuccess = () => {
    window.location.reload();
  };
  return (
    <div className="grid gap-4">
      {packages.map((pkg, index) => (
        <div key={index} className="relative">
          {isSelecting && !pkg.is_payed_for && (
            <div className="absolute left-4 top-1/2 -translate-y-1/2 z-10">
              <Checkbox checked={selectedParcels.includes(pkg.id!)} onCheckedChange={() => onParcelSelect?.(pkg.id!)} className="h-5 w-5" />
            </div>
          )}
          <Card className={`${isSelecting && !pkg.is_payed_for ? "pl-12" : ""} overflow-hidden w-full relative`}>
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value={`package-${pkg.tracking_id}`} className="border-b-0">
                <AccordionTrigger className="hover:no-underline w-full px-6 py-2">
                  <div className="flex flex-col w-full text-left">
                    <div className="flex items-center mb-1">
                      <Badge className="mr-2 bg-primary text-primary-foreground" variant="secondary">
                        {index + 1}
                      </Badge>
                      <span className="text-xs break-all flex-1">{pkg.tracking_id ?? "N/A"}</span>
                      {pkg.is_payed_for && (
                        <Badge variant="secondary" className="ml-2 bg-green-100 text-green-800 text-xs">
                          Paid
                        </Badge>
                      )}
                      {pkg.consolidation_status && (
                        <Badge variant="secondary" className={`ml-2 text-xs ${formatConsolidationStatus(pkg.consolidation_status).classes}`}>
                          Consolidation - {formatConsolidationStatus(pkg.consolidation_status).text}
                        </Badge>
                      )}
                    </div>
                    <div className="space-y-1">
                      <div className="text-xs">
                        <strong>Received:</strong> {pkg.received_at}
                      </div>
                      <div className="text-xs">
                        <strong>From:</strong> {pkg.bought_from}
                      </div>
                      <div className="text-xs">
                        <strong>Est. Arrival:</strong> {formatDate(new Date(pkg.est_due_date ?? "")) ?? "Pending"}
                      </div>
                      <div className="text-xs">
                        <strong>Status:</strong> {pkg.status}
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <CardContent className="pt-5 px-2 space-y-4">
                    {!pkg.items_pdf || !pkg.items_pdf.url ? (
                      <ParcelFileUpload parcelModel={pkg} userEmail={userEmail} onUploadSuccess={handleFileUploadSuccess} />
                    ) : (
                        <ShippingDetails parcelModel={pkg} userEmail={userEmail} />
                    )}
                  </CardContent>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </Card>
        </div>
      ))}
    </div>
  );
}
