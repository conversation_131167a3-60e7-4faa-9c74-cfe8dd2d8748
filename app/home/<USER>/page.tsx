"use client";

import Address<PERSON>ard from "./address";
import React, { useEffect, useState } from "react";
import { HomeSkeleton } from "@/components/skeletons/home";
import { UserAddressModel } from "@/services/address.service";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AddressService, MailpalletLocation } from "@/services/address.service";
import { Info } from "lucide-react";

export default function VirtualAddress() {
  const [loading, setLoading] = useState(true);
  const [addressDetails, setAddressDetails] = useState<UserAddressModel | null>(null);
  const [ukLocationDetails, setUkLocationDetails] = useState<MailpalletLocation | null>(null);
  const [usaLocationDetails, setUsaLocationDetails] = useState<MailpalletLocation | null>(null);
  const [chinaLocationDetails, setChinaLocationDetails] = useState<MailpalletLocation | null>(null);

  const getAddressDetails = async () => {
    setLoading(true);
    const addressService = new AddressService();
    const userAddressData = await addressService.fetchUserAddress();
    const locationsData = await addressService.fetchUserLocations();

    setAddressDetails(userAddressData);
    if (locationsData.length > 0) {
      setChinaLocationDetails(locationsData.filter((location) => location.country === "China")[0]);
      setUkLocationDetails(locationsData.filter((location) => location.country === "United Kingdom")[0]);
      setUsaLocationDetails(locationsData.filter((location) => location.country === "United States")[0]);
    }
    setLoading(false);
  };

  useEffect(() => {
    const fetchData = async () => {
      getAddressDetails();
    };
    fetchData();
  }, []);

  const ServiceNotice = () => (
    <div className="max-w-5xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
    <div className="bg-[#2563eb] text-white p-5 text-center">
      <h1 className="text-1.8rem font-semibold">Important Notice About Our Service</h1>
    </div>
    <div className="p-7">
      <div className="mb-6">
        <h2 className="text-1.4rem font-medium text-[#2563eb] mb-3.75">We're Growing to Serve You Better</h2>
        <p className="mb-3">We sincerely apologize for any inconvenience you may be experiencing. Due to unprecedented demand, we are currently expanding our operations to a larger warehouse facility.</p>
        <p className="mb-3">During this transition, our address service is temporarily unavailable on the website.</p>
        <div className="bg-[#dbeafe] border-l-4 border-[#2563eb] p-3 rounded">
          <span className="inline-block w-5 h-5 bg-[#2563eb] rounded-full text-white text-center leading-5 mr-2 font-bold">i</span>
          <strong>Rest assured, this is only temporary.</strong> We're working diligently to complete this expansion as quickly as possible to better serve your logistics needs.
        </div>
      </div>
      <div className="mb-6">
        <h2 className="text-1.4rem font-medium text-[#2563eb] mb-3">What This Means For You</h2>
        <ul className="list-none">
          <li className="mb-2.5 pl-7 relative before:content-['✓'] before:absolute before:left-0 before:text-[#2563eb] before:font-bold">
            If you've already shipped your items, there's no need to worry. Your shipment is being processed normally.
          </li>
          <li className="mb-2.5 pl-7 relative before:content-['✓'] before:absolute before:left-0 before:text-[#2563eb] before:font-bold">
            All existing shipments will be updated in your account as they progress through our system.
          </li>
          <li className="mb-2.5 pl-7 relative before:content-['✓'] before:absolute before:left-0 before:text-[#2563eb] before:font-bold">
            You'll receive an email notification shortly with more details about when our address services will be fully restored.
          </li>
        </ul>
      </div>
      <div className="mb-6">
        <h2 className="text-1.4rem font-medium text-[#2563eb] mb-3">Next Steps</h2>
        <p className="mb-3">We appreciate your patience during this growth period. Our customer service team remains available to assist with any urgent inquiries through our <a href="/blog/contact-us" className="text-[#2563eb] font-medium">contact page</a>.</p>
          <p className="mb-3">Thank you for your understanding as we improve our infrastructure to deliver even better service in the future.</p>
        </div>
      </div>
    </div>
  );

  return loading ? (
    <HomeSkeleton />
  ) : (
    <div className="w-full max-w-[800px] mx-auto p-6 bg-white">
      <Tabs defaultValue="uk" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="uk" className="flex items-center">
            UK
          </TabsTrigger>
          <TabsTrigger value="usa" className="flex items-center">
            USA
          </TabsTrigger>
          <TabsTrigger value="china" className="flex items-center">
            China
          </TabsTrigger>
        </TabsList>

        <TabsContent value="uk" className="mt-4">
          {/* Content for UK */}
          <AddressCard title="UK" addressDetails={addressDetails} locationDetails={ukLocationDetails} />
        </TabsContent>
        <TabsContent value="usa" className="mt-4">
          {/* Content for USA */}
          {/* <AddressCard title="USA" addressDetails={addressDetails} locationDetails={usaLocationDetails} /> */}
          <ServiceNotice />
        </TabsContent>
        <TabsContent value="china" className="mt-4">
          {/* Content for China */}
          {/* <AddressCard title="China" addressDetails={addressDetails} locationDetails={chinaLocationDetails} /> */}
          <ServiceNotice />
        </TabsContent>
      </Tabs>
    </div>
  );
}
