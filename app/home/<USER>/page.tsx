"use client";

import { z } from "zod";
import { ArrowLeft } from "lucide-react";
import { useForm } from "react-hook-form";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { zodResolver } from "@hookform/resolvers/zod";
import AddressSkeleton from "@/components/skeletons/address";
import { ButtonLoading } from "@/components/app/button-loading";
import { AddressService, UserAddressModel } from "@/services/address.service";
import { Form, FormField, FormItem, FormControl, FormMessage, FormLabel } from "@/components/ui/form";
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@/components/ui/select";
import { countryList } from "@/lib/constants";

const formSchema = z.object({
  firstName: z.string().min(1, {
    message: "First name is required",
  }),
  lastName: z.string().min(1, {
    message: "Last name is required",
  }),
  emailAddress: z
    .string()
    .email({
      message: "Email address must be a valid email address",
    })
    .min(1, {
      message: "Email address is required",
    }),
  company: z.string(),
  address: z.string().min(1, {
    message: "Address is required",
  }),
  city: z.string().min(1, {
    message: "City is required",
  }),
  state: z.string(),
  postalCode: z.string(),
  deliverTo: z.string().min(1, {
    message: "Delivery country is required",
  }),
  withinAccra: z.boolean().optional(),
  outsideAccra: z.boolean().optional(),
  withinLagos: z.boolean().optional(),
  outsideLagos: z.boolean().optional(),
});

export default function Address() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState<boolean>(false);
  const [selectedCountry, setSelectedCountry] = useState<string | undefined>(undefined);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      city: "",
      state: "",
      address: "",
      company: "",
      lastName: "",
      firstName: "",
      deliverTo: "",
      postalCode: "",
      emailAddress: "",
      withinAccra: false,
      outsideAccra: false,
      withinLagos: false,
      outsideLagos: false,
    },
  });

  const watchedDeliverTo = form.watch("deliverTo");

  const getAddressDetails = async () => {
    setLoading(true);
    const addressService = new AddressService();
    const userAddressData = await addressService.fetchUserAddress();

    // Set form values with fetched address data
    form.reset({
      city: userAddressData?.City || "",
      company: userAddressData?.Company || "",
      address: userAddressData?.Address || "",
      lastName: userAddressData?.LastName || "",
      firstName: userAddressData?.FirstName || "",
      state: userAddressData?.StateProvince || "",
      deliverTo: userAddressData?.DeliverTo || "",
      postalCode: userAddressData?.PostalCode || "",
      emailAddress: userAddressData?.EmailAddress || "",
      withinAccra: userAddressData?.WithinAccra || false,
      outsideAccra: userAddressData?.OutsideAccra || false,
      withinLagos: userAddressData?.WithinLagos || false,
      outsideLagos: userAddressData?.OutsideLagos || false,
    });

    // Set the selected country in the state
    setSelectedCountry(userAddressData?.DeliverTo || "");
    setLoading(false);
  };

  const getAddressModel = () => {
    const formData = form.getValues();
    const address: UserAddressModel = {
      City: formData.city,
      Company: formData.company,
      Address: formData.address,
      LastName: formData.lastName,
      FirstName: formData.firstName,
      DeliverTo: formData.deliverTo,
      StateProvince: formData.state,
      PostalCode: formData.postalCode,
      EmailAddress: formData.emailAddress,
      WithinAccra: formData.withinAccra,
      OutsideAccra: formData.outsideAccra,
      WithinLagos: formData.withinLagos,
      OutsideLagos: formData.outsideLagos,
    };
    return address;
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    console.log(values);
    setUpdating(true);
    const addressModel = getAddressModel();
    const addressService = new AddressService();
    const result = await addressService.updateUserAddress(addressModel);
    if (result === true) {
      setUpdating(false);
      showSuccessToast();
    } else {
      setUpdating(false);
      showErrorToast(result as string);
    }
  };

  const showSuccessToast = () => {
    toast({
      title: "Completed",
      description: "Address have been successfully updated",
    });
  };

  const showErrorToast = (message: string) => {
    toast({
      title: "Oops!",
      description: message,
      variant: "destructive",
    });
  };

  const handleBack = () => {
    router.back();
  };

  useEffect(() => {
    getAddressDetails();
  }, []);

  // Reset location fields when country changes
  useEffect(() => {
    if (watchedDeliverTo) {
      form.setValue("withinAccra", false);
      form.setValue("outsideAccra", false);
      form.setValue("withinLagos", false);
      form.setValue("outsideLagos", false);
    }
  }, [watchedDeliverTo, form]);

  return (
    <main className="flex flex-1 flex-col gap-2 p-4 lg:p-6 w-full overflow-x-hidden">
      <div className="w-full max-w-[800px] mx-auto mb-2 flex items-center gap-4">
        <Button 
          variant="ghost" 
          onClick={handleBack}
          className="h-8 w-8 p-0"
          aria-label="Go back"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-lg font-semibold md:text-2xl">Address</h1>
      </div>
      {loading ? (
        <AddressSkeleton />
      ) : (
        <div className="flex flex-1 justify-center rounded-lg shadow-sm mt-10">
          <div className="flex flex-col gap-1 w-full max-w-[550px] px-2 sm:px-0">
            <div className="flex h-full w-full">
              <div className="w-full space-y-4">
                <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
                  <p className="text-blue-700">
                    <strong>Important Notice!</strong> <br /> <br />
                    The address at the time we receive the parcel in our warehouse is the address we will use when delivering. Please ensure the delivery address below is correct
                    before placing an order.
                  </p>
                </div>

                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>First Name *</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Max" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Last Name *</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Robinson" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="deliverTo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Deliver to *</FormLabel>
                          <FormControl>
                            <Select
                              value={selectedCountry}
                              onValueChange={(value) => {
                                setSelectedCountry(value);
                                form.setValue("deliverTo", value);
                              }}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select country" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectGroup>
                                  <SelectLabel>Countries</SelectLabel>
                                  {/* {countryList.map((country) => (
                                    <SelectItem key={country.value} value={country.value}>
                                      {country.label}
                                    </SelectItem>
                                  ))} */}
                                  {countryList.filter((country) => country.value === "Ghana" || country.value === "Nigeria").map((country) => (
                                    <SelectItem key={country.value} value={country.value}>
                                      {country.label}
                                    </SelectItem>
                                  ))}
                                </SelectGroup>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Conditional location dropdown for Ghana and Nigeria */}
                    {(watchedDeliverTo === "Ghana" || watchedDeliverTo === "Nigeria") && (
                      <FormField
                        control={form.control}
                        name={watchedDeliverTo === "Ghana" ? "withinAccra" : "withinLagos"}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {watchedDeliverTo === "Ghana" ? "Location in Ghana" : "Location in Nigeria"}
                            </FormLabel>
                            <FormControl>
                              <Select
                                value={
                                  watchedDeliverTo === "Ghana"
                                    ? form.getValues("withinAccra")
                                      ? "within"
                                      : form.getValues("outsideAccra")
                                      ? "outside"
                                      : ""
                                    : form.getValues("withinLagos")
                                    ? "within"
                                    : form.getValues("outsideLagos")
                                    ? "outside"
                                    : ""
                                }
                                onValueChange={(value) => {
                                  if (watchedDeliverTo === "Ghana") {
                                    form.setValue("withinAccra", value === "within");
                                    form.setValue("outsideAccra", value === "outside");
                                  } else {
                                    form.setValue("withinLagos", value === "within");
                                    form.setValue("outsideLagos", value === "outside");
                                  }
                                }}
                              >
                                <SelectTrigger className="w-full">
                                  <SelectValue placeholder={`Select location in ${watchedDeliverTo}`} />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectGroup>
                                    <SelectLabel>Location Options</SelectLabel>
                                    <SelectItem value="within">
                                      {watchedDeliverTo === "Ghana" ? "Within Accra" : "Within Lagos"}
                                    </SelectItem>
                                    <SelectItem value="outside">
                                      {watchedDeliverTo === "Ghana" ? "Outside Accra" : "Outside Lagos"}
                                    </SelectItem>
                                  </SelectGroup>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Warning message for Ghana and Nigeria */}
                    {(watchedDeliverTo === "Ghana" || watchedDeliverTo === "Nigeria") && (
                      <Alert className="border-orange-200 bg-orange-50">
                        <AlertDescription className="text-orange-800">
                          Please ensure that all information provided above is accurate. If the delivery address is changed after payment has been made, any additional costs incurred will be the responsibility of the customer.
                        </AlertDescription>
                      </Alert>
                    )}

                    <FormField
                      control={form.control}
                      name="company"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Company</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Address *</FormLabel>
                          <FormControl>
                            <Input {...field} required />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="city"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>City *</FormLabel>
                            <FormControl>
                              <Input {...field} required />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="state"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>State/Province</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="postalCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Postal Code</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="emailAddress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email Address *</FormLabel>
                          <FormControl>
                            <Input {...field} type="email" required />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {!updating ? (
                      <Button type="submit" className="w-full">
                        Save Address
                      </Button>
                    ) : (
                      <ButtonLoading />
                    )}
                  </form>
                </Form>
              </div>
            </div>
          </div>
        </div>
      )}
    </main>
  );
}
