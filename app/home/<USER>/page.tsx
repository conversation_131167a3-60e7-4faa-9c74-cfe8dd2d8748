"use client";

import { Pa<PERSON>elCard } from "./parcel-card";
import { useEffect, useState, Suspense } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ParcelService } from "@/services/parcel.service";
import ParcelSkeleton from "@/components/skeletons/parcel";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Package2, ChevronLeft, ChevronRight, PackagePlus, X, Boxes, ArrowLeft, Info } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { User } from "@/data/models/user.model";
import { ParcelModel } from "@/data/models/parcel.model";
import { useRouter, useSearchParams } from "next/navigation";

function ParcelsContent() {
  const router = useRouter();
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const parcelService = new ParcelService();

  const [loading, setLoading] = useState<boolean>(false);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [isSelecting, setIsSelecting] = useState<boolean>(false);
  const [packages, setPackages] = useState<Array<ParcelModel>>([]);
  const [selectedParcels, setSelectedParcels] = useState<number[]>([]);
  const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false);
  const [selectedCountry, setSelectedCountry] = useState<string>(searchParams.get("origin")!);
  const [activeTab, setActiveTab] = useState<string>(searchParams.get("origin")!.toLowerCase());

  const userEmail = JSON.parse(localStorage.getItem("__mp_user_data")!).email ?? "";

  const getParcels = async (country: string, page: number) => {
    setLoading(true);
    const { parcels, total } = await parcelService.fetchParcels(country, page);
    setPackages(parcels.filter((parcel) => !parcel.is_consolidated && !parcel.is_deleted));
    setTotalPages(Math.ceil(total / 10));
    setLoading(false);
  };

  useEffect(() => {
    getParcels(selectedCountry, currentPage);
  }, [currentPage, selectedCountry]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    switch (value) {
      case "uk":
        setSelectedCountry("UK");
        break;
      case "usa":
        setSelectedCountry("USA");
        break;
      case "china":
        setSelectedCountry("China");
        break;
    }
    setCurrentPage(1); // Reset to first page when changing tabs
  };

  const handleParcelSelection = (parcelId: number) => {
    setSelectedParcels((prev) => {
      if (prev.includes(parcelId)) {
        return prev.filter((id) => id !== parcelId);
      } else {
        return [...prev, parcelId];
      }
    });
  };

  const handleConsolidateConfirm = async () => {
    try {
      const userData = JSON.parse(localStorage.getItem("__mp_user_data")!) as User;
      const result = await parcelService.addConsolidationRequest(selectedParcels, userData.id!);

      if (result.success) {
        toast({
          title: "Consolidation Request Sent",
          description: (
            <div className="flex flex-col gap-1">
              <p>Your consolidation request has been submitted successfully.</p>
              <p className="text-muted-foreground text-sm">We'll send you an email with updates on the consolidation progress. You can also check back here to view the status.</p>
            </div>
          ),
          duration: 5000,
          className: "bg-background border-green-600",
        });

        setIsSelecting(false);
        setSelectedParcels([]);
        setShowConfirmDialog(false);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to create consolidation request",
          variant: "destructive",
          duration: 5000,
        });
      }
    } catch (error) {
      console.error("Error in handleConsolidateConfirm:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
        duration: 5000,
      });
    }
  };

  const shouldDisableConsolidate = packages.length < 2 || (isSelecting && selectedParcels.length < 2);

  const EmptyState = () => (
    <div className="flex flex-col items-center justify-center h-[60vh] text-center">
      <Package2 className="w-16 h-16 text-gray-400 mb-4" />
      <h2 className="text-2xl font-semibold text-gray-700 mb-2">No parcels yet</h2>
      <p className="text-gray-500 max-w-sm">You currently have no parcels. When you receive a parcel, it will appear here.</p>
    </div>
  );

  const ServiceNotice = () => (
    <div className="max-w-5xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
    <div className="bg-[#2563eb] text-white p-5 text-center">
      <h1 className="text-1.8rem font-semibold">Important Notice About Our Service</h1>
    </div>
    <div className="p-7">
      <div className="mb-6">
        <h2 className="text-1.4rem font-medium text-[#2563eb] mb-3.75">We're Growing to Serve You Better</h2>
        <p className="mb-3">We sincerely apologize for any inconvenience you may be experiencing. Due to unprecedented demand, we are currently expanding our operations to a larger warehouse facility.</p>
        <p className="mb-3">During this transition, our address service is temporarily unavailable on the website.</p>
        <div className="bg-[#dbeafe] border-l-4 border-[#2563eb] p-3 rounded">
          <span className="inline-block w-5 h-5 bg-[#2563eb] rounded-full text-white text-center leading-5 mr-2 font-bold">i</span>
          <strong>Rest assured, this is only temporary.</strong> We're working diligently to complete this expansion as quickly as possible to better serve your logistics needs.
        </div>
      </div>
      <div className="mb-6">
        <h2 className="text-1.4rem font-medium text-[#2563eb] mb-3">What This Means For You</h2>
        <ul className="list-none">
          <li className="mb-2.5 pl-7 relative before:content-['✓'] before:absolute before:left-0 before:text-[#2563eb] before:font-bold">
            If you've already shipped your items, there's no need to worry. Your shipment is being processed normally.
          </li>
          <li className="mb-2.5 pl-7 relative before:content-['✓'] before:absolute before:left-0 before:text-[#2563eb] before:font-bold">
            All existing shipments will be updated in your account as they progress through our system.
          </li>
          <li className="mb-2.5 pl-7 relative before:content-['✓'] before:absolute before:left-0 before:text-[#2563eb] before:font-bold">
            You'll receive an email notification shortly with more details about when our address services will be fully restored.
          </li>
        </ul>
      </div>
      <div className="mb-6">
        <h2 className="text-1.4rem font-medium text-[#2563eb] mb-3">Next Steps</h2>
        <p className="mb-3">We appreciate your patience during this growth period. Our customer service team remains available to assist with any urgent inquiries through our <a href="/blog/contact-us" className="text-[#2563eb] font-medium">contact page</a>.</p>
          <p className="mb-3">Thank you for your understanding as we improve our infrastructure to deliver even better service in the future.</p>
        </div>
      </div>
    </div>
  );

  return (
    <main className="flex flex-col gap-4 p-2 sm:p-4 lg:gap-6 lg:p-6 w-full overflow-x-hidden">
      <div className="w-full max-w-[1050px] mx-auto">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-start gap-4">
            <Button variant="ghost" size="icon" onClick={() => router.back()} className="hover:bg-accent">
              <ArrowLeft className="h-5 w-5" />
              <span className="sr-only">Go back</span>
            </Button>
            <div className="flex flex-col">
              <h1 className="text-lg font-semibold md:text-2xl">Parcels</h1>
              <a href="/blog/not_seeing_your_parcel" className="underline text-blue-500">Not seeing your parcel?</a>
            </div>
          </div>
          <div className="flex gap-2">
            {isSelecting && (
              <Button
                variant="outline"
                onClick={() => {
                  setIsSelecting(false);
                  setSelectedParcels([]);
                }}
                className="flex items-center gap-2 hover:bg-destructive/10"
              >
                <X className="h-4 w-4" />
                Cancel
              </Button>
            )}
            <div className="flex items-center gap-2">
              <Button
                variant={isSelecting && selectedParcels.length > 0 ? "destructive" : "default"}
                onClick={() => {
                  if (isSelecting && selectedParcels.length > 0) {
                    setShowConfirmDialog(true);
                  } else {
                    setIsSelecting(true);
                  }
                }}
                disabled={shouldDisableConsolidate}
                className={`flex items-center gap-2 ${isSelecting && selectedParcels.length > 0 ? "bg-green-600 hover:bg-green-700" : "bg-primary hover:bg-primary/90"}`}
              >
                {isSelecting ? (
                  <>
                    <Boxes className="h-4 w-4" />
                    Consolidate ({selectedParcels.length})
                  </>
                ) : (
                  <>
                    <PackagePlus className="h-4 w-4" />
                    Consolidate Parcels
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-1 flex-col items-center w-full">
        <div className="w-full max-w-[1050px] space-y-4">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="uk" className="flex items-center">
                UK
              </TabsTrigger>
              <TabsTrigger value="usa" className="flex items-center">
                USA
              </TabsTrigger>
              <TabsTrigger value="china" className="flex items-center">
                China
              </TabsTrigger>
            </TabsList>

            {loading ? (
              <ParcelSkeleton />
            ) : (
              <>
                <TabsContent value="uk" className="mt-4">
                  {packages.length === 0 ? (
                    <EmptyState />
                  ) : (
                    <ParcelCard userEmail={userEmail} packages={packages} isSelecting={isSelecting} selectedParcels={selectedParcels} onParcelSelect={handleParcelSelection} />
                  )}
                </TabsContent>
                <TabsContent value="usa"  className="mt-4">
                  {packages.length === 0 ? (
                    <EmptyState />
                  ) : (
                    // <ParcelCard userEmail={userEmail} packages={packages} isSelecting={isSelecting} selectedParcels={selectedParcels} onParcelSelect={handleParcelSelection} />
                    <ServiceNotice />
                  )}
                </TabsContent>
                <TabsContent value="china"  className="mt-4">
                  {packages.length === 0 ? (
                    <EmptyState />
                  ) : (
                    // <ParcelCard userEmail={userEmail} packages={packages} isSelecting={isSelecting} selectedParcels={selectedParcels} onParcelSelect={handleParcelSelection} />
                    <ServiceNotice />
                  )}
                </TabsContent>
              </>
            )}
          </Tabs>
        </div>
        {packages.length > 0 && (
          <div className="flex justify-center mt-8">
            <nav className="inline-flex items-center rounded-md" aria-label="Pagination">
              <Button
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                variant="outline"
                size="sm"
                className="px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
              >
                <span className="sr-only">Previous</span>
                <ChevronLeft className="h-5 w-5" aria-hidden="true" />
              </Button>
              <div className="px-2 sm:px-4 py-2 border-t border-b border-gray-300 bg-white text-xs font-medium text-gray-700">
                Page {currentPage} of {totalPages}
              </div>
              <Button
                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                variant="outline"
                size="sm"
                className="px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
              >
                <span className="sr-only">Next</span>
                <ChevronRight className="h-5 w-5" aria-hidden="true" />
              </Button>
            </nav>
          </div>
        )}
      </div>

      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Boxes className="h-5 w-5 text-primary" />
              Confirm Consolidation
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-2">
              <p>
                You are about to consolidate <span className="font-medium text-primary">{selectedParcels.length}</span> parcels into one shipment.
              </p>
              <p className="text-muted-foreground">This will allow you to ship multiple parcels together, potentially reducing shipping costs.</p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="flex items-center gap-2">
              <X className="h-4 w-4" />
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleConsolidateConfirm} className="flex items-center gap-2 bg-green-600 hover:bg-green-700">
              <Boxes className="h-4 w-4" />
              Confirm Consolidation
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </main>
  );
}

export default function Parcels() {
  return (
    <Suspense fallback={<ParcelSkeleton />}>
      <ParcelsContent />
    </Suspense>
  );
}
