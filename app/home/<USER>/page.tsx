"use client";

import { z } from "zod";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "@/hooks/use-toast";
import { MapPinIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import ProfileSkeleton from "@/components/skeletons/profile";
import { UserService, UserData } from "@/services/user.service";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  FormLabel,
  FormDescription,
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@/components/ui/form";

const FormSchema = z.object({
  marketing_emails: z.boolean().default(false),
});

export default function Profile() {
  const router = useRouter();
  const {
    isAuthenticated,
    redirectIfNotAuthenticated,
    loading: authLoading,
  } = useAuth();
  const [loading, setLoading] = useState<boolean>(false);
  const [userData, setUserData] = useState<UserData | undefined>(undefined);

  const handleBack = () => {
    router.back();
  };

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      marketing_emails: false,
    },
  });

  useEffect(() => {
    // Only attempt to load profile when auth status is known.
    // If the user is not authenticated, redirect them to login and preserve
    // the intended destination (handled by middleware or login `next` param).
    if (!authLoading && !isAuthenticated) {
      redirectIfNotAuthenticated();
      return;
    }

    if (isAuthenticated) {
      getProfileDetails();
    }
  }, [authLoading, isAuthenticated]);

  useEffect(() => {
    if (userData) {
      form.setValue("marketing_emails", userData.receive_marketing);
    }
  }, [userData, form]);

  const getProfileDetails = async () => {
    setLoading(true);
    try {
      const userService = new UserService();
      const profile = await userService.getUserProfile();
      setUserData(profile);
    } catch (err) {
      console.error("Failed to load profile:", err);
      // If fetching profile fails (e.g., session expired), redirect to login.
      try {
        redirectIfNotAuthenticated();
      } catch (e) {
        // swallow if router not available
      }
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: z.infer<typeof FormSchema>) => {
    setLoading(true);
    try {
      const userService = new UserService();

      const done = await userService.addOrRemoveFromMarketingList(
        userData?.email || "",
        userData?.first_name || "",
        userData?.last_name || "",
        data.marketing_emails,
      );

      const updateResult = await userService.updateUserMarketingPreference(
        data.marketing_emails,
      );

      if (done && updateResult) {
        setUserData((prevData) => ({
          ...prevData!,
          receive_marketing: data.marketing_emails,
        }));

        toast({
          title: data.marketing_emails
            ? "Subscribed to marketing emails"
            : "Unsubscribed from marketing emails",
          description: data.marketing_emails
            ? "You'll now receive emails about new products, features, and more."
            : "You've been unsubscribed from marketing emails.",
        });
      } else {
        toast({
          title: "Error",
          description:
            "Failed to update your marketing preferences. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating marketing preferences:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <main className="flex flex-1 flex-col gap-2 p-4 lg:p-6 w-full overflow-x-hidden">
      <div className="w-full max-w-[800px] mx-auto mb-2 flex items-center gap-4">
        <Button
          variant="ghost"
          onClick={handleBack}
          className="h-8 w-8 p-0"
          aria-label="Go back"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-lg font-semibold md:text-2xl">Profile</h1>
      </div>
      {loading ? (
        <ProfileSkeleton />
      ) : (
        <div className="flex flex-1 justify-center rounded-lg shadow-sm mt-10">
          <div className="flex flex-col gap-1 w-full max-w-[800px] px-2 sm:px-0">
            <div className="flex h-full w-full">
              <div className="w-full space-y-4">
                <div className="grid gap-2 text-center">
                  <div className="items-start">
                    <Avatar className="w-24 h-24 sm:w-36 sm:h-36">
                      <AvatarFallback className="text-4xl font-bold">
                        {userData
                          ? `${userData.first_name[0]}${userData.last_name[0]}`.toUpperCase()
                          : "CN"}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                </div>
                <div className="grid gap-4">
                  <Form {...form}>
                    <form>
                      <div className="space-y-4">
                        {[
                          "First Name",
                          "Last Name",
                          "Email Address",
                          "Phone Number",
                        ].map((item, index) => (
                          <FormItem
                            key={index}
                            className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm"
                          >
                            <div className="space-y-0.5">
                              <FormDescription>{item}</FormDescription>
                              <FormLabel>
                                {userData &&
                                  (item === "Email Address"
                                    ? userData.email
                                    : userData[
                                        item
                                          .toLowerCase()
                                          .replace(" ", "_") as keyof UserData
                                      ] || "N/A")}
                              </FormLabel>
                            </div>
                            {/* <EditProfileDialog
                                trigger={
                                  <Button variant="ghost">
                                    <Edit2Icon className="w-4 h-4" />
                                  </Button>
                                }
                                initialData={{
                                  first_name: userData?.first_name || '',
                                  last_name: userData?.last_name || '',
                                  email: userData?.email || '',
                                  phone_number: userData?.phone_number || '',
                                  receive_marketing: userData?.receive_marketing || false,
                                }}
                                /> */}
                          </FormItem>
                        ))}
                      </div>
                    </form>
                  </Form>
                  <Form {...form}>
                    <form
                      onSubmit={form.handleSubmit(onSubmit)}
                      className="w-full space-y-6"
                    >
                      <div>
                        <div className="space-y-4">
                          <FormField
                            control={form.control}
                            name="marketing_emails"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                                <div className="space-y-0.5">
                                  <FormLabel>Marketing emails</FormLabel>
                                  <FormDescription>
                                    Receive emails about new products, features,
                                    and more.
                                  </FormDescription>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={(checked) => {
                                      field.onChange(checked);
                                      form.handleSubmit(onSubmit)();
                                    }}
                                    disabled={loading}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </form>
                  </Form>
                </div>

                <div className="mt-6">
                  <Link href="/home/<USER>" className="block">
                    <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-100 rounded-full">
                          <MapPinIcon className="w-6 h-6 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-base sm:text-lg">
                            Manage Addresses
                          </h3>
                          <p className="text-xs sm:text-sm text-gray-600">
                            View and edit your personal address.
                          </p>
                        </div>
                      </div>
                      <svg
                        className="w-4 h-4 sm:w-6 sm:h-6 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </div>
                  </Link>
                </div>

                <div className="mt-6">
                  <Link href="/home/<USER>" className="block">
                    <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-100 rounded-full">
                          <MapPinIcon className="w-6 h-6 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-base sm:text-lg">
                            Virtual Addresses
                          </h3>
                          <p className="text-xs sm:text-sm text-gray-600">
                            View details about your UK, USA & China addresses.
                          </p>
                        </div>
                      </div>
                      <svg
                        className="w-4 h-4 sm:w-6 sm:h-6 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </main>
  );
}
