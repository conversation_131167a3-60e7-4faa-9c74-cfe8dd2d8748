"use client";

import React, { useState, useEffect, useMemo, Suspense } from "react";
import Image from "next/image";
import { useRouter } from "next/router";
import { ChevronDown, Filter, X } from "lucide-react";
import SearchParamsClientWrapper from "@/components/app/listings/suspense-wrap/SearchParamsClientWrapper";
import { ProductService } from "@/services/product.service";
import {
  Product,
  ExternalProductDetails,
  InternalProductDetails,
} from "@/data/models/product.model";
import { useCartDispatch, addToCart } from "@/lib/CartContext";
import { categories, country_currencies } from "@/lib/constants";
import { AddressService, UserAddressModel } from "@/services/address.service";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { formatCurrency, formatCurrencyFallback } from "@/lib/currency";
import { useCurrency } from "@/components/app/CurrencyProvider";
import { Skeleton } from "@/components/ui/skeleton";

// Product listing / results page component
const ProductListingContent: React.FC<{
  searchParams: URLSearchParams | null;
}> = ({ searchParams }) => {
  const dispatch = useCartDispatch();
  const [products, setProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Currency is provided by CurrencyProvider
  const {
    currencyCode: userCurrencyCode,
    exchangeRate,
    isLoadingExchangeRate,
  } = useCurrency();

  // Service instances
  const addressService = new AddressService();

  // Helper function to get display pricing (considering variants)
  const getDisplayPricing = (product: Product) => {
    if (product.variants?.variants && product.variants.variants.length > 0) {
      const firstVariant = product.variants.variants[0];
      return {
        price: firstVariant.price || product.primary_data.price,
        sale_price: firstVariant.sale_price || product.primary_data.sale_price,
        hasVariants: true,
      };
    }
    return {
      price: product.primary_data.price,
      sale_price: product.primary_data.sale_price,
      hasVariants: false,
    };
  };

  // Helper function to convert price using stored exchange rate
  const convertPrice = (price: number) => {
    if (userCurrencyCode === "GBP" || !exchangeRate) {
      return price;
    }
    return price * exchangeRate;
  };

  // Helper function to get currency code by country name
  const getCurrencyCodeByCountryName = (countryName: string): string => {
    const currencyCode = Object.keys(country_currencies).find(
      (code) =>
        country_currencies[code as keyof typeof country_currencies] ===
        countryName,
    );
    return currencyCode || "GBP";
  };

  // Load user address (provider handles currency bootstrapping)
  useEffect(() => {
    const loadUserAddress = async () => {
      try {
        await addressService.fetchUserAddress();
      } catch (error) {
        console.error("Failed to load user address (info only):", error);
      }
    };

    loadUserAddress();
  }, []);

  // Pull search query from URL and execute search
  useEffect(() => {
    const query = (searchParams && searchParams.get("query")) || "";
    setSearchQuery(query);

    if (query.trim()) {
      const executeSearch = async () => {
        setIsLoading(true);
        try {
          const productService = new ProductService();
          const searchResults = await productService.searchProductsByName(
            query.trim(),
          );
          setProducts(searchResults);
        } catch (error) {
          console.error("Error searching products:", error);
          setProducts([]);
        } finally {
          setIsLoading(false);
        }
      };

      executeSearch();
    } else {
      setProducts([]);
      setIsLoading(false);
    }
  }, [searchParams, userCurrencyCode]);

  // Pagination and filtering state
  const [currentPage, setCurrentPage] = useState(1);
  const resultsPerPage = 10;

  const [sortBy, setSortBy] = useState("Price: Low to High");
  const [showFilters, setShowFilters] = useState(false);
  const [minPrice, setMinPrice] = useState("");
  const [maxPrice, setMaxPrice] = useState("");
  const [selectedCondition, setSelectedCondition] = useState<string[]>([]);
  const [selectedOrigin, setSelectedOrigin] = useState<string[]>([]);
  const [selectedShippingLocation, setSelectedShippingLocation] = useState<
    string[]
  >([]);
  const [selectedType, setSelectedType] = useState<string[]>([]);

  // Filtering & sorting derived list
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products.filter((product) => {
      const pricing = getDisplayPricing(product);
      const convertedPrice = convertPrice(pricing.sale_price || pricing.price);

      if (minPrice && convertedPrice < parseFloat(minPrice)) return false;
      if (maxPrice && convertedPrice > parseFloat(maxPrice)) return false;

      if (
        selectedCondition.length &&
        !selectedCondition.includes(product.condition)
      )
        return false;

      if (
        selectedOrigin.length &&
        !selectedOrigin.includes(product.origin_location)
      )
        return false;

      if (selectedShippingLocation.length) {
        if (!product.shipping_rates || product.shipping_rates.length === 0)
          return false;
        const hasValidShipping = product.shipping_rates.some((rate) =>
          selectedShippingLocation.includes(rate.to_country),
        );
        if (!hasValidShipping) return false;
      }

      if (selectedType.length) {
        const productType =
          product.type === "internal" ? "Internal" : "External";
        if (!selectedType.includes(productType)) return false;
      }

      return true;
    });

    return [...filtered].sort((a, b) => {
      const pricingA = getDisplayPricing(a);
      const pricingB = getDisplayPricing(b);
      const priceA = convertPrice(pricingA.sale_price || pricingA.price);
      const priceB = convertPrice(pricingB.sale_price || pricingB.price);
      return sortBy === "Price: Low to High"
        ? priceA - priceB
        : priceB - priceA;
    });
  }, [
    products,
    minPrice,
    maxPrice,
    selectedCondition,
    selectedOrigin,
    selectedShippingLocation,
    selectedType,
    sortBy,
    exchangeRate,
    userCurrencyCode,
  ]);

  const totalResults = filteredAndSortedProducts.length;
  const totalPages = Math.ceil(totalResults / resultsPerPage);
  const showPagination = totalResults > resultsPerPage;

  const currentProducts = useMemo(() => {
    const startIndex = (currentPage - 1) * resultsPerPage;
    return filteredAndSortedProducts.slice(
      startIndex,
      startIndex + resultsPerPage,
    );
  }, [filteredAndSortedProducts, currentPage]);

  useEffect(() => {
    setCurrentPage(1);
  }, [
    minPrice,
    maxPrice,
    selectedCondition,
    selectedOrigin,
    selectedShippingLocation,
    selectedType,
    sortBy,
    userCurrencyCode,
  ]);

  // Helpers for UI
  const toggleCondition = (c: string) =>
    setSelectedCondition((prev) =>
      prev.includes(c) ? prev.filter((p) => p !== c) : [...prev, c],
    );
  const toggleOrigin = (o: string) =>
    setSelectedOrigin((prev) =>
      prev.includes(o) ? prev.filter((p) => p !== o) : [...prev, o],
    );
  const toggleShippingLocation = (s: string) =>
    setSelectedShippingLocation((prev) =>
      prev.includes(s) ? prev.filter((p) => p !== s) : [...prev, s],
    );
  const toggleType = (t: string) =>
    setSelectedType((prev) =>
      prev.includes(t) ? prev.filter((p) => p !== t) : [...prev, t],
    );

  // Category helper
  const getCategoryHierarchy = (
    categoryId: string,
    subcategoryId?: string | null,
  ) => {
    const cat = categories.find((c) => c.id === categoryId);
    const sub = categories.find((c) => c.id === subcategoryId);
    if (!cat) return "";
    if (!sub) return cat.name;
    return `${cat.name} › ${sub.name}`;
  };

  // Render
  return (
    <div className="max-w-7xl mx-auto px-3 sm:px-4 py-6 sm:py-8 lg:px-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Search results</h2>
        <div className="flex items-center gap-2">
          <div className="text-sm text-gray-500">
            {totalResults} result{totalResults !== 1 ? "s" : ""}
          </div>
          <div>
            <button
              onClick={() => setShowFilters((s) => !s)}
              className="inline-flex items-center gap-2 px-3 py-1 border rounded-md text-sm hover:bg-gray-50"
            >
              <Filter className="w-4 h-4" />
              Filters
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filters / sidebar */}
        <aside className={`hidden lg:block ${showFilters ? "" : ""}`}>
          <div className="p-4 bg-white border rounded-md">
            <div className="mb-3">
              <Label>Price range</Label>
              <div className="flex items-center gap-2 mt-2">
                <input
                  type="number"
                  value={minPrice}
                  onChange={(e) => setMinPrice(e.target.value)}
                  placeholder="Min"
                  className="w-1/2 px-2 py-1 border rounded"
                />
                <input
                  type="number"
                  value={maxPrice}
                  onChange={(e) => setMaxPrice(e.target.value)}
                  placeholder="Max"
                  className="w-1/2 px-2 py-1 border rounded"
                />
              </div>
            </div>

            <div className="mb-3">
              <Label>Condition</Label>
              <div className="flex flex-col mt-2">
                <label className="flex items-center gap-2">
                  <Checkbox
                    checked={selectedCondition.includes("New")}
                    onCheckedChange={() => toggleCondition("New")}
                  />
                  <span className="text-sm">New</span>
                </label>
                <label className="flex items-center gap-2 mt-1">
                  <Checkbox
                    checked={selectedCondition.includes("Used")}
                    onCheckedChange={() => toggleCondition("Used")}
                  />
                  <span className="text-sm">Used</span>
                </label>
              </div>
            </div>

            <div className="mb-3">
              <Label>Type</Label>
              <div className="flex flex-col mt-2">
                <label className="flex items-center gap-2">
                  <Checkbox
                    checked={selectedType.includes("Internal")}
                    onCheckedChange={() => toggleType("Internal")}
                  />
                  <span className="text-sm">Internal</span>
                </label>
                <label className="flex items-center gap-2 mt-1">
                  <Checkbox
                    checked={selectedType.includes("External")}
                    onCheckedChange={() => toggleType("External")}
                  />
                  <span className="text-sm">External</span>
                </label>
              </div>
            </div>
          </div>
        </aside>

        {/* Results grid */}
        <div className="lg:col-span-3">
          {isLoading ? (
            <div className="py-12 text-center text-gray-500">
              Loading results...
            </div>
          ) : products.length === 0 ? (
            <div className="py-12 text-center text-gray-600">
              <div className="mb-3 text-lg font-medium">No products found</div>
              <div className="text-sm max-w-xl mx-auto">
                Can't find what you're looking for? Request a product and we'll
                try to source it for you. Please use our{" "}
                <Link
                  href="https://docs.google.com/forms/d/e/1FAIpQLSefeL_sb6SV6TQUGk3qbB-zgSDx1aakGTgiQu4DNBUZ4qYB1w/viewform?usp=sharing&ouid=113597109975813091882"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 underline"
                >
                  Product Request Form
                </Link>
                .
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {currentProducts.map((product) => {
                const pricing = getDisplayPricing(product);
                return (
                  <div
                    key={product.id}
                    className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden"
                  >
                    <div className="relative">
                      <Link
                        href={`/home/<USER>/products/${product.id}`}
                        className="block"
                      >
                        <div className="w-full h-40 overflow-hidden flex items-center justify-center bg-gray-50">
                          <Image
                            src={
                              product.variants?.variants?.[0]?.images?.[0]
                                ?.url ||
                              product.primary_image?.url ||
                              "https://placehold.co/400x300"
                            }
                            alt={product.title}
                            width={400}
                            height={300}
                            className="object-contain object-center w-full h-full"
                          />
                        </div>
                      </Link>
                    </div>

                    {/* Product Info */}
                    <div className="p-4 flex flex-col flex-grow">
                      <div className="flex-grow">
                        <h3 className="font-medium text-gray-900 mb-1">
                          <Link
                            href={`/home/<USER>/products/${product.id}`}
                            className="hover:text-blue-700 hover:underline"
                          >
                            {product.title}
                          </Link>
                        </h3>
                        <p className="text-sm text-gray-600 mb-2">
                          {getCategoryHierarchy(
                            product.category,
                            product.subcategory,
                          )}
                        </p>

                        {/* Price */}
                        <div className="flex items-center gap-2 mb-2">
                          {pricing.sale_price &&
                          pricing.sale_price < pricing.price ? (
                            <>
                              {isLoadingExchangeRate &&
                              userCurrencyCode !== "GBP" ? (
                                <Skeleton className="w-24 h-6" />
                              ) : (
                                <span className="text-lg font-semibold text-gray-900">
                                  {formatCurrency(
                                    convertPrice(pricing.sale_price),
                                    userCurrencyCode,
                                  )}
                                </span>
                              )}
                              {isLoadingExchangeRate &&
                              userCurrencyCode !== "GBP" ? (
                                <Skeleton className="w-20 h-4" />
                              ) : (
                                <span className="text-sm text-gray-500 line-through">
                                  {formatCurrency(
                                    convertPrice(pricing.price),
                                    userCurrencyCode,
                                  )}
                                </span>
                              )}
                              <span className="text-sm text-green-600 font-medium">
                                Save{" "}
                                {Math.round(
                                  ((pricing.price - pricing.sale_price) /
                                    pricing.price) *
                                    100,
                                )}
                                %
                              </span>
                            </>
                          ) : isLoadingExchangeRate &&
                            userCurrencyCode !== "GBP" ? (
                            <Skeleton className="w-24 h-6" />
                          ) : (
                            <span className="text-lg font-semibold text-gray-900">
                              {formatCurrency(
                                convertPrice(pricing.price),
                                userCurrencyCode,
                              )}
                            </span>
                          )}
                        </div>

                        <div className="text-sm text-gray-600 mb-1">
                          Ships from: {product.origin_location}
                        </div>
                      </div>

                      <div className="mt-3 flex items-center justify-between">
                        <button
                          onClick={() => addToCart(dispatch, product)}
                          className="px-3 py-2 bg-blue-600 text-white rounded-md text-sm"
                        >
                          Add to cart
                        </button>
                        <div className="text-sm text-gray-500">
                          {product.type === "internal"
                            ? "In Stock"
                            : "External"}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Pagination */}
      {showPagination && (
        <div className="mt-8 flex flex-col items-center space-y-4">
          {/* Results info */}
          <div className="text-sm text-gray-600 text-center">
            Showing {(currentPage - 1) * resultsPerPage + 1} to{" "}
            {Math.min(currentPage * resultsPerPage, totalResults)} of{" "}
            {totalResults} results
          </div>

          {/* Pagination controls */}
          <div className="flex items-center justify-center">
            {/* Mobile pagination - simplified */}
            <div className="flex md:hidden items-center space-x-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="flex items-center justify-center w-10 h-10 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                aria-label="Previous page"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>

              <div className="flex items-center space-x-1 px-3 py-2 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium text-gray-900">
                  {currentPage}
                </span>
                <span className="text-sm text-gray-500">of</span>
                <span className="text-sm font-medium text-gray-900">
                  {totalPages}
                </span>
              </div>

              <button
                onClick={() =>
                  setCurrentPage(Math.min(totalPages, currentPage + 1))
                }
                disabled={currentPage === totalPages}
                className="flex items-center justify-center w-10 h-10 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                aria-label="Next page"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>

            {/* Desktop pagination - full controls */}
            <div className="hidden md:flex items-center space-x-1">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Previous
              </button>

              {/* Page numbers */}
              {(() => {
                const pages = [];
                const maxVisiblePages = 7;
                let startPage = Math.max(
                  1,
                  currentPage - Math.floor(maxVisiblePages / 2),
                );
                let endPage = Math.min(
                  totalPages,
                  startPage + maxVisiblePages - 1,
                );

                // Adjust start page if we're near the end
                if (endPage - startPage + 1 < maxVisiblePages) {
                  startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }

                // Always show first page
                if (startPage > 1) {
                  pages.push(
                    <button
                      key={1}
                      onClick={() => setCurrentPage(1)}
                      className="flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      1
                    </button>,
                  );
                  if (startPage > 2) {
                    pages.push(
                      <span
                        key="ellipsis1"
                        className="flex items-center justify-center w-10 h-10 text-gray-500"
                      >
                        ...
                      </span>,
                    );
                  }
                }

                // Page numbers
                for (let i = startPage; i <= endPage; i++) {
                  pages.push(
                    <button
                      key={i}
                      onClick={() => setCurrentPage(i)}
                      className={`flex items-center justify-center w-10 h-10 text-sm font-medium rounded-lg transition-colors ${
                        currentPage === i
                          ? "bg-blue-600 text-white border border-blue-600"
                          : "text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
                      }`}
                    >
                      {i}
                    </button>,
                  );
                }

                // Always show last page
                if (endPage < totalPages) {
                  if (endPage < totalPages - 1) {
                    pages.push(
                      <span
                        key="ellipsis2"
                        className="flex items-center justify-center w-10 h-10 text-gray-500"
                      >
                        ...
                      </span>,
                    );
                  }
                  pages.push(
                    <button
                      key={totalPages}
                      onClick={() => setCurrentPage(totalPages)}
                      className="flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      {totalPages}
                    </button>,
                  );
                }

                return pages;
              })()}

              <button
                onClick={() =>
                  setCurrentPage(Math.min(totalPages, currentPage + 1))
                }
                disabled={currentPage === totalPages}
                className="flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default function ResultsPage() {
  return (
    <SearchParamsClientWrapper fallback={null}>
      {(searchParams) => <ProductListingContent searchParams={searchParams} />}
    </SearchParamsClientWrapper>
  );
}
