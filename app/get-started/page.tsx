"use client";

import { 
  UserPlus, 
  Mail, 
  LogIn, 
  MapPin, 
  ShoppingCart, 
  Package, 
  ClipboardList, 
  Truck, 
  CreditCard, 
  CheckCircle,
  ArrowRight,
  Sparkles
} from "lucide-react";
import Header from "@/components/index/header";
import Footer from "@/components/index/footer";

export default function GetStarted() {
  const steps = [
    {
      step: 1,
      title: "Create an account",
      description: "Sign up at www.mailpallet.com to create your account.",
      icon: UserPlus,
      highlight: false,
      image: "/assets/imgs/get-started/register.png",
    },
    {
      step: 2,
      title: "Verify your email address",
      description: "Login to your email account and verify your email address.",
      icon: Mail,
      highlight: false,
      image: "/assets/imgs/get-started/verify-email.webp",
    },
    {
      step: 3,
      title: "Login to your account",
      description: "Login to the account you just created with us.",
      icon: LogIn,
      highlight: true,
      warning: "Please make sure you use the exact address provided to you. Each address is unique to the user.",
      image: "/assets/imgs/get-started/login.png",
    },
    {
      step: 4,
      title: "Get Your UK Address",
      description: "View your unique UK address on the 'Dashboard' page.",
      icon: MapPin,
      highlight: true,
      warning: "Make sure to use the UK based website to use our service",
      image: "/assets/imgs/get-started/address.png",
    },
    {
      step: 5,
      title: "Shop on UK Websites",
      description: "When shopping on a UK website, deliver your goods to our warehouse.",
      icon: ShoppingCart,
      highlight: false,
      image: "/assets/imgs/get-started/address-on-ecommerce.png",
    },
    {
      step: 6,
      title: "We Receive Your Items",
      description: "Once we receive your items, we will send you an email notification.",
      icon: Package,
      highlight: false,
      image: "/assets/imgs/get-started/warehouse.webp",
    },
    {
      step: 7,
      title: "Check Your Parcels",
      description: "Visit the 'My Parcels' page to view your items. Update the cost of the item.",
      icon: ClipboardList,
      highlight: false,
      image: "/assets/imgs/get-started/item-value.png",
    },
    {
      step: 8,
      title: "Choose Shipping Method",
      description: "Choose from either Air or Sea shipping and decide whether you want to insure your parcel.",
      icon: Truck,
      highlight: false,
      image: "/assets/imgs/get-started/select-shipping.png",
    },
    {
      step: 9,
      title: "Make Payment",
      description: 'Complete the payment for your items and shipping. Make sure to use "MAILPALLET15" to get 15% off on the shipping price.',
      icon: CreditCard,
      highlight: true,
      promo: true,
      image: "/assets/imgs/get-started/make-payment.png",
    },
    {
      step: 10,
      title: "Receive Your Items",
      description: "Your items will be delivered to your country.",
      icon: CheckCircle,
      highlight: false,
      image: "/assets/imgs/get-started/receive-delivery.webp",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <Header />
      
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-purple-600/5"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24">
          <div className="text-center">
            <div className="inline-flex items-center space-x-2 bg-blue-50 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <Sparkles className="w-4 h-4" />
              <span>Simple • Fast • Reliable</span>
            </div>
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              How to use{" "}
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                MailPallet
              </span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Follow these simple steps to start using our service and have your UK purchases 
              delivered to you anywhere in the world.
            </p>
          </div>
        </div>
      </section>

      {/* Steps Section */}
      <section className="py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-12 sm:space-y-16">
            {steps.map((item, index) => (
              <div key={index} className="relative">
                {/* Connection Line */}
                {index < steps.length - 1 && (
                  <div className="absolute left-6 top-16 w-0.5 h-12 bg-gradient-to-b from-blue-200 to-transparent hidden sm:block"></div>
                )}
                
                <div className={`relative bg-white rounded-2xl shadow-sm border border-gray-100 p-6 sm:p-8 transition-all duration-300 hover:shadow-lg hover:scale-[1.01] ${
                  item.highlight ? 'ring-2 ring-blue-100' : ''
                }`}>
                  <div className="flex flex-col lg:flex-row items-start gap-6">
                    {/* Step Icon and Number */}
                    <div className="flex items-start gap-4 lg:w-2/3">
                      <div className={`relative flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center ${
                        item.highlight 
                          ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg' 
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        <item.icon className="w-6 h-6" />
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-600 text-white text-xs font-bold rounded-full flex items-center justify-center">
                          {item.step}
                        </div>
                      </div>
                      
                      <div className="flex-1">
                        <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
                          {item.title}
                        </h3>
                        <p className="text-gray-600 leading-relaxed mb-4">
                          {item.description}
                        </p>
                        
                        {/* Promo Badge */}
                        {item.promo && (
                          <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 text-green-700 px-3 py-1 rounded-full text-sm font-medium">
                            <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                            <span>Use code: MAILPALLET15</span>
                          </div>
                        )}
                        
                        {/* Warning Message */}
                        {item.warning && (
                          <div className="mt-4 bg-amber-50 border-l-4 border-amber-400 p-4 rounded-r-lg">
                            <div className="flex items-start">
                              <div className="flex-shrink-0">
                                <svg className="w-5 h-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <div className="ml-3">
                                <p className="text-sm text-amber-700 font-medium">
                                  {item.warning}
                                </p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* Image */}
                    <div className="w-full lg:w-1/3">
                      <div className="rounded-xl overflow-hidden shadow-lg bg-white p-2">
                        {item.image.endsWith(".webp") || item.image.endsWith(".webm") ? (
                          item.image.endsWith(".webm") ? (
                            <video 
                              src={item.image} 
                              className="w-full h-auto object-contain rounded-lg hover:scale-105 transition-transform duration-300" 
                              autoPlay 
                              loop 
                              muted 
                              playsInline 
                            />
                          ) : (
                            <img 
                              src={item.image} 
                              alt={`Screenshot for Step ${item.step}`} 
                              className="w-full h-auto object-contain rounded-lg hover:scale-105 transition-transform duration-300" 
                            />
                          )
                        ) : (
                          <img 
                            src={item.image} 
                            alt={`Screenshot for Step ${item.step}`} 
                            className="w-full h-auto object-contain rounded-lg hover:scale-105 transition-transform duration-300" 
                          />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 sm:py-24 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-6">
            Ready to get started?
          </h2>
          <p className="text-xl text-blue-100 mb-8 leading-relaxed">
            Join thousands of satisfied customers who trust MailPallet for their UK shopping needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="/login" 
              className="inline-flex items-center justify-center space-x-2 bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <span>Get Started Now</span>
              <ArrowRight className="w-5 h-5" />
            </a>
            <a 
              href="#" 
              className="inline-flex items-center justify-center space-x-2 border-2 border-white text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white hover:text-blue-600 transition-all duration-300"
            >
              <span>Watch Demo</span>
            </a>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}