import { Metadata } from "next";

export const metadata: Metadata = {
  title: "How to Get Started with MailPallet - Shop UK, Deliver to Africa",
  description:
    "Learn how to use MailPallet in 8 easy steps. Register, get your UK address, shop on UK websites, and have your parcels delivered to Africa. Start your international shopping journey with MailPallet today!",
  openGraph: {
    title: "MailPallet Guide: How to Shop UK and Deliver to Africa",
    siteName: "MailPallet",
    description:
      "Step-by-step guide on using MailPallet to shop from UK websites and get your parcels delivered to Africa. Easy registration, UK address allocation, and seamless international shipping.",
    authors: ["MailPallet Team"],
    tags: ["UK shopping", "Africa delivery", "international shipping", "how-to guide"],
  },
  twitter: {
    title: "Shop UK, Deliver to Africa with MailPallet",
    description: "Shop in the UK with MailPallet and get your items delivered to Africa. Free UK address, easy shipping, and great savings up to 15%!",
    creator: "@mailpallet",
    site: "https://mailpallet.com",
  },
  keywords: "uk shopping, africa delivery, mailpallet, international shipping, online shopping, easy shipping, package forwarding",
};

export default function GetStartedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
