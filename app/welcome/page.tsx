"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { CheckCircle, ArrowRight } from "lucide-react";

export default function Welcome() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-white text-slate-900 flex flex-col items-center justify-center p-4">
      <div className="w-40 h-12 mb-12">
        <Image src="/assets/imgs/logo.svg" alt="Company Logo" width={160} height={48} className="object-contain" priority />
      </div>

      <div className="relative w-full max-w-lg">
        {/* Background blur effects */}
        <div className="absolute top-0 -left-4 w-72 h-72 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute top-0 -right-4 w-72 h-72 bg-sky-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>

        {/* Content */}
        <div className="relative backdrop-blur-sm border border-slate-200 bg-white/80 rounded-3xl p-10 shadow-xl">
          <div className="flex items-center justify-center w-20 h-20 mx-auto mb-8 rounded-full bg-emerald-100">
            <CheckCircle className="text-emerald-600 h-10 w-10" />
          </div>

          <h1 className="text-4xl font-bold text-center mb-4">You're all set! 🎉</h1>

          <p className="text-slate-600 text-center mb-8">Your account has been successfully created. Check your email to verify your account.</p>

          <Button
            size="lg"
            onClick={() => router.push("/login")}
            className="w-full bg-slate-900 text-white hover:bg-slate-800 font-medium rounded-xl p-6 flex items-center justify-center gap-2 group"
          >
            Continue to Login
            <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      </div>
    </div>
  );
}
