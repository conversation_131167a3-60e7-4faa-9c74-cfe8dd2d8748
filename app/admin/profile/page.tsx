"use client";

import { UserCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Admin } from "@/data/models/admin.model";
import React, { useState, useEffect } from "react";
import { AuthService } from "@/services/auth.service";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface ConfirmationModalProps {
  open: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  message: string;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({ open, onConfirm, onCancel, message }) => {
  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogContent className="p-6">
        <DialogHeader>
          <DialogTitle className="text-lg font-bold">Confirm Logout</DialogTitle>
        </DialogHeader>
        <p className="mt-2">{message}</p>
        <div className="flex justify-end mt-4">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button className="ml-2" onClick={onConfirm}>
            Confirm
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default function ProfilePage() {
  const router = useRouter();
  const [adminData, setAdminData] = useState<Admin | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [user, setUser] = useState({
    fullName: "",
    email: "",
    role: "Admin",
    lastLogin: new Date().toLocaleDateString(),
  });

useEffect(() => {
  // Helper function to get cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || null;
    }
    return null;
  };

  const authCookie = getCookie("_auth_admin");
  
  if (authCookie) {
    try {
      const parsedData = JSON.parse(authCookie);
      setAdminData(parsedData);
      setUser({
        fullName: `${parsedData.first_name} ${parsedData.last_name}`,
        email: parsedData.email,
        role: "Admin",
        lastLogin: new Date().toLocaleDateString(),
      });
    } catch (error) {
      console.error("Error parsing admin cookie:", error);
      router.push("/admin");
    }
  } else {
    router.push("/admin");
  }
  
  setIsLoading(false);
}, [router]);

  const timeOfDay = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 17) return "Good afternoon";
    return "Good evening";
  };

  const handleLogout = () => {
    const authService = new AuthService();
    authService.signOut();
    window.location.href = "/admin";
    if (typeof window !== 'undefined') {
    document.cookie = '_auth_admin=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
  }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!adminData) {
    return null;
  }

  return (
    <div className="p-6 space-y-6">
      {/* Greeting Section */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          {timeOfDay()}, {user.fullName}
        </h1>
        <p className="text-muted-foreground">Welcome back to your workspace</p>
      </div>

      {/* Profile Card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center">
              <UserCircle className="h-8 w-8 text-primary" />
            </div>

            <div className="space-y-4 flex-1">
              {/* User Details */}
              <div className="space-y-1">
                <h2 className="text-xl font-semibold">{user.fullName}</h2>
                <div className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-sm font-semibold text-primary">{user.role}</div>
              </div>

              {/* Contact & Additional Info */}
              <div className="space-y-2">
                <div>
                  <p className="text-sm text-muted-foreground">Email address</p>
                  <p className="text-sm font-medium">{user.email}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Last login</p>
                  <p className="text-sm font-medium">{user.lastLogin}</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Logout Button */}
      <div className="flex justify-end">
        <Button onClick={() => setIsModalOpen(true)} className="bg-red-500 hover:bg-red-600">
          Logout
        </Button>
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal open={isModalOpen} onConfirm={handleLogout} onCancel={() => setIsModalOpen(false)} message="Are you sure you want to logout?" />
    </div>
  );
};
