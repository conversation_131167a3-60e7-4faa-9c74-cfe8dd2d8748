"use client";

import { useAuth } from "@/hooks/use-auth";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import React, { useEffect, useState } from "react";
import ConfirmationModal from "./confirmation-modal";
import { ParcelModel } from "@/data/models/parcel.model";
import { ParcelService } from "@/services/parcel.service";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ConsolidationRequest } from "@/data/models/consolidation_requests.models";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Package2,
  Clock,
  CheckCircle2,
  AlertCircle,
  ChevronRight,
  Truck,
  DollarSign,
  ScaleIcon,
  BoxIcon,
  LucideProps,
  Trash,
  XCircle,
} from "lucide-react";
import { useRouter } from "next/navigation";

const parcelService = new ParcelService();

const ConsolidationDashboard = () => {
  // useAuth does not accept parameters; call without args.
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dataFetched, setDataFetched] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [actionType, setActionType] = useState<string | null>(null);
  const [selectedRequest, setSelectedRequest] =
    useState<ConsolidationRequest | null>(null);
  const [consolidationRequestList, setConsolidationRequestList] = useState<
    ConsolidationRequest[]
  >([]);
  const router = useRouter();
  // Format currency helper
  const formatCurrency = (amount?: number) => {
    if (amount === undefined) return "-";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "GBP",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  // Format date helper
  const formatDate = (date?: string) => {
    if (!date) return "-";
    return new Date(date).toLocaleDateString();
  };

  const InfoRow = ({
    label,
    value,
  }: {
    label: string;
    value: React.ReactNode;
  }) => (
    <div className="grid grid-cols-2 gap-2 py-2 border-b border-gray-100">
      <span className="text-sm font-medium text-gray-600">{label}:</span>
      <span className="text-sm">{value}</span>
    </div>
  );

  const getActiveRequests = async () => {
    const requests = await parcelService.fetchActiveRequests();
    const updatedRequests = requests.map((request) => ({
      ...request,
      parcels: request.parcels.map((parcel) => ({
        ...parcel,
        total_due: getTotalDue(parcel),
      })),
    }));
    setConsolidationRequestList(updatedRequests);
    setDataFetched(true);
  };

  useEffect(() => {
    if (!dataFetched) {
      getActiveRequests();
    }
  }, [dataFetched]);

  const calculateShipmentPrice = (parcel: ParcelModel) => {
    const weight =
      (parcel.package_weight ?? 0) < 1 ? 1 : (parcel.package_weight ?? 0);
    const pricingModel = parcel.shipping_options?.filter(
      (o) => o.id === parcel.selected_shipping_method_id,
    )[0];
    return (
      (pricingModel?.shippingFee ?? 0) * weight +
      (pricingModel?.consolidationFee ?? 0) +
      (parcel.storage_fee ?? 0) +
      (pricingModel?.handlingFee ?? 0) +
      (parcel.dangerous_goods_price ?? 0) +
      (parcel.duty ?? 0)
    );
  };

  const calculateInsurance = (parcel: ParcelModel) => {
    return parcel.has_insurance ? calculateShipmentPrice(parcel) * 0.03 : 0;
  };

  const calculateTotalPrice = (parcel: ParcelModel) => {
    return (
      calculateShipmentPrice(parcel) +
      (parcel?.has_insurance ? calculateInsurance(parcel) : 0)
    );
  };

  // Lets calculate for total_due
  const getTotalDue = (parcel: ParcelModel) => {
    return calculateTotalPrice(parcel);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<
      string,
      {
        class: string;
        icon: React.ForwardRefExoticComponent<
          Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>
        >;
      }
    > = {
      Pending: { class: "bg-yellow-500", icon: Clock },
      Approved: { class: "bg-blue-500", icon: Package2 },
      Completed: { class: "bg-green-500", icon: CheckCircle2 },
      Rejected: { class: "bg-red-500", icon: XCircle },
      Cancelled: { class: "bg-gray-500", icon: Trash },
    };

    const config = statusConfig[status];

    if (!config) {
      return null;
    }

    const Icon = config.icon;
    return (
      <Badge className={`${config.class} text-white flex gap-1 items-center`}>
        <Icon className="w-3 h-3" />
        {status}
      </Badge>
    );
  };

  // New summary component for the accordion header
  const ParcelSummary = ({ parcel }: { parcel: ParcelModel }) => (
    <div className="grid grid-cols-4 gap-4 w-full text-sm">
      <div className="flex items-center gap-2">
        <Package2 className="w-4 h-4 text-gray-500" />
        <span>{parcel.tracking_id}</span>
      </div>
      <div className="flex items-center gap-2">
        <ScaleIcon className="w-4 h-4 text-gray-500" />
        <span>{parcel.package_weight} kg</span>
      </div>
      <div className="flex items-center gap-2">
        <BoxIcon className="w-4 h-4 text-gray-500" />
        <span>{parcel.package_content}</span>
      </div>
      <div className="flex items-center gap-2">
        <DollarSign className="w-4 h-4 text-gray-500" />
        <span>{formatCurrency(getTotalDue(parcel))}</span>
      </div>
    </div>
  );

  const updateRequestStatusInList = (newStatus: string) => {
    setConsolidationRequestList((prevList) =>
      prevList.map((request) => {
        if (request.code === selectedRequest!.code) {
          const updatedRequest = { ...request, status: newStatus };
          setSelectedRequest(updatedRequest); // Update the selected request to reflect the new status
          return updatedRequest;
        }
        return request;
      }),
    );
  };

  const handleAction = async () => {
    setIsLoading(true);
    try {
      if (actionType === "startProcessing") {
        // Update the consolidation request status
        await parcelService.updateRequestStatus(selectedRequest!.code, 2);

        // Update is_consolidated flag for each parcel
        // Guard parcel.id so we only call the service with a defined numeric id.
        // For parcels without an id, return a resolved promise so Promise.all can proceed.
        const updatePromises = selectedRequest!.parcels.map((parcel) =>
          parcel.id
            ? parcelService.updateParcelConsolidationStatus(parcel.id, true)
            : Promise.resolve(),
        );
        try {
          await Promise.all(updatePromises);
          updateRequestStatusInList("Approved");
          router.push("/admin/parcels");
        } catch (error) {
          console.error("Error updating parcel consolidation status:", error);
        }
      } else if (actionType === "reject") {
        await parcelService.updateRequestStatus(selectedRequest!.code, 4);
        updateRequestStatusInList("Rejected");
      } else if (actionType === "markComplete") {
        await parcelService.updateRequestStatus(selectedRequest!.code, 3);
        updateRequestStatusInList("Completed");
      }
    } finally {
      setIsModalOpen(false);
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Consolidation Requests</h1>
        <Badge variant="outline" className="flex gap-2 items-center">
          <AlertCircle className="w-4 h-4" />
          {
            consolidationRequestList.filter((r) => r.status === "Pending")
              .length
          }{" "}
          Pending Requests
        </Badge>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Active Requests</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Request ID</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Parcels</TableHead>
                <TableHead>Request Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {consolidationRequestList.map((request) => (
                <TableRow
                  key={request.id}
                  className="cursor-pointer hover:bg-slate-50"
                >
                  <TableCell className="font-medium">{request.code}</TableCell>
                  <TableCell>{request.userName}</TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {request.parcels.length} parcels
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDate(request.requestDate)}</TableCell>
                  <TableCell>{getStatusBadge(request.status)}</TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedRequest(request);
                        setDialogOpen(true);
                      }}
                    >
                      View Details
                      <ChevronRight className="w-4 h-4 ml-2" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Consolidation Request Details</DialogTitle>
          </DialogHeader>

          {selectedRequest && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-semibold">
                    Request {selectedRequest.code}
                  </h3>
                  <p className="text-sm text-gray-500">
                    Submitted by {selectedRequest.userName} on{" "}
                    {formatDate(selectedRequest.requestDate)}
                  </p>
                </div>
                {getStatusBadge(selectedRequest.status)}
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">
                    Parcels to be Consolidated
                  </CardTitle>
                  <CardDescription>
                    {selectedRequest.parcels.length} parcels in this request
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Accordion
                    type="single"
                    collapsible
                    className="w-full space-y-4"
                  >
                    {selectedRequest.parcels.map((parcel, index) => (
                      <AccordionItem
                        key={parcel.id}
                        value={`parcel-${index}`}
                        className="border rounded-lg p-2"
                      >
                        <AccordionTrigger className="hover:no-underline">
                          <ParcelSummary parcel={parcel} />
                        </AccordionTrigger>
                        <AccordionContent className="pt-4">
                          <div className="space-y-6">
                            {/* Quick Overview Section */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <Card>
                                <CardContent className="pt-6">
                                  <div className="flex flex-col gap-2">
                                    <div className="flex items-center gap-2 text-blue-600">
                                      <Package2 className="w-5 h-5" />
                                      <h4 className="font-semibold">
                                        Package Details
                                      </h4>
                                    </div>
                                    <p className="text-sm text-gray-600">
                                      Content: {parcel.package_content}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                      Weight: {parcel.package_weight} kg
                                    </p>
                                    <p className="text-sm text-gray-600">
                                      Dimensions: {parcel.dimensions}
                                    </p>
                                  </div>
                                </CardContent>
                              </Card>

                              <Card>
                                <CardContent className="pt-6">
                                  <div className="flex flex-col gap-2">
                                    <div className="flex items-center gap-2 text-green-600">
                                      <Truck className="w-5 h-5" />
                                      <h4 className="font-semibold">
                                        Shipping
                                      </h4>
                                    </div>
                                    <p className="text-sm text-gray-600">
                                      Method:{" "}
                                      {parcel.is_air_cargo
                                        ? "Air Cargo"
                                        : "Sea Cargo"}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                      Est. Delivery:{" "}
                                      {formatDate(parcel.est_due_date)}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                      Country: {parcel.country}
                                    </p>
                                  </div>
                                </CardContent>
                              </Card>

                              <Card>
                                <CardContent className="pt-6">
                                  <div className="flex flex-col gap-2">
                                    <div className="flex items-center gap-2 text-purple-600">
                                      <DollarSign className="w-5 h-5" />
                                      <h4 className="font-semibold">Costs</h4>
                                    </div>
                                    <p className="text-sm text-gray-600">
                                      Items Value:{" "}
                                      {formatCurrency(parcel.items_value)}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                      Shipping:{" "}
                                      {formatCurrency(
                                        parcel.shipping_options?.[index]
                                          ?.shippingFee || 0,
                                      )}
                                    </p>
                                    <p className="text-sm font-medium">
                                      Total: {formatCurrency(parcel.total_due)}
                                    </p>
                                  </div>
                                </CardContent>
                              </Card>
                            </div>

                            {/* Detailed Information Tabs */}
                            <Tabs defaultValue="shipping" className="w-full">
                              <TabsList className="grid w-full grid-cols-3">
                                <TabsTrigger value="shipping">
                                  Shipping Details
                                </TabsTrigger>
                                <TabsTrigger value="costs">
                                  Costs & Fees
                                </TabsTrigger>
                                <TabsTrigger value="status">
                                  Status & Tracking
                                </TabsTrigger>
                              </TabsList>

                              <TabsContent value="shipping" className="mt-4">
                                <Card>
                                  <CardContent className="pt-6">
                                    <div className="grid grid-cols-2 gap-4">
                                      <InfoRow
                                        label="Shipping Address"
                                        value={parcel.shipping_address || "-"}
                                      />
                                      <InfoRow
                                        label="Country"
                                        value={parcel.country || "-"}
                                      />
                                      <InfoRow
                                        label="Received Date"
                                        value={formatDate(parcel.received_on)}
                                      />
                                      <InfoRow
                                        label="Estimated Delivery"
                                        value={formatDate(parcel.est_due_date)}
                                      />
                                      <InfoRow
                                        label="Shipping Method"
                                        value={
                                          parcel.is_air_cargo
                                            ? "Air Cargo"
                                            : "Sea Cargo"
                                        }
                                      />
                                      <InfoRow
                                        label="Insurance"
                                        value={
                                          parcel.is_insured
                                            ? "Insured"
                                            : "Not Insured"
                                        }
                                      />
                                    </div>
                                  </CardContent>
                                </Card>
                              </TabsContent>

                              <TabsContent value="costs" className="mt-4">
                                <Card>
                                  <CardContent className="pt-6">
                                    <div className="grid grid-cols-2 gap-4">
                                      <InfoRow
                                        label="Shipment Price"
                                        value={formatCurrency(
                                          parcel.shipping_options?.[index]
                                            ?.shippingFee || 0,
                                        )}
                                      />
                                      <InfoRow
                                        label="Storage Fee"
                                        value={formatCurrency(
                                          parcel.storage_fee,
                                        )}
                                      />
                                      <InfoRow
                                        label="Handling Fee"
                                        value={formatCurrency(
                                          parcel.shipping_options?.[index]
                                            ?.handlingFee || 0,
                                        )}
                                      />
                                      <InfoRow
                                        label="Consolidation Fee"
                                        value={formatCurrency(
                                          parcel.shipping_options?.[index]
                                            ?.consolidationFee || 0,
                                        )}
                                      />
                                      <InfoRow
                                        label="Insurance Cost"
                                        value={formatCurrency(
                                          parcel.shipping_options?.[index]
                                            ?.insuranceFee || 0,
                                        )}
                                      />
                                      <InfoRow
                                        label="Total Due"
                                        value={formatCurrency(parcel.total_due)}
                                      />
                                    </div>
                                  </CardContent>
                                </Card>
                              </TabsContent>

                              <TabsContent value="status" className="mt-4">
                                <Card>
                                  <CardContent className="pt-6">
                                    <div className="grid grid-cols-2 gap-4">
                                      <InfoRow
                                        label="Tracking ID"
                                        value={parcel.tracking_id || "-"}
                                      />
                                      <InfoRow
                                        label="Vendor Tracking"
                                        value={parcel.vendor_tracking || "-"}
                                      />
                                      <InfoRow
                                        label="Status"
                                        value={parcel.status || "-"}
                                      />
                                      <InfoRow
                                        label="Bought From"
                                        value={parcel.bought_from || "-"}
                                      />
                                      <InfoRow
                                        label="Payment Status"
                                        value={
                                          parcel.is_payed_for
                                            ? "Paid"
                                            : "Pending"
                                        }
                                      />
                                      <InfoRow
                                        label="Payment Method"
                                        value={parcel.payment_method || "-"}
                                      />
                                    </div>
                                  </CardContent>
                                </Card>
                              </TabsContent>
                            </Tabs>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>

              <div className="flex justify-end gap-3">
                <Button variant="outline" onClick={() => setDialogOpen(false)}>
                  Cancel
                </Button>

                {selectedRequest.status === "Pending" && (
                  <>
                    <Button
                      className={`bg-blue-500 hover:bg-blue-600 ${isLoading ? "opacity-50 cursor-not-allowed" : ""}`}
                      onClick={() => {
                        setActionType("startProcessing");
                        setIsModalOpen(true);
                      }}
                      disabled={isLoading}
                    >
                      {isLoading ? "Processing..." : "Start Processing"}
                    </Button>

                    <Button
                      className="bg-red-500 hover:bg-red-600"
                      onClick={() => {
                        setActionType("reject");
                        setIsModalOpen(true);
                      }}
                      disabled={isLoading}
                    >
                      Reject
                    </Button>
                  </>
                )}

                {selectedRequest.status !== "Pending" &&
                  selectedRequest.status !== "Rejected" && (
                    <Button
                      className="bg-green-500 hover:bg-green-600"
                      onClick={() => {
                        setActionType("markComplete");
                        setIsModalOpen(true);
                      }}
                    >
                      Mark as Complete
                    </Button>
                  )}
              </div>

              <ConfirmationModal
                open={isModalOpen}
                onConfirm={handleAction}
                onCancel={() => setIsModalOpen(false)}
                message={`Are you sure you want to ${actionType === "startProcessing" ? "start processing" : actionType === "reject" ? "reject" : "mark as complete"} this request?`}
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ConsolidationDashboard;
