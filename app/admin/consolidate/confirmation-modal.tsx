import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface ConfirmationModalProps {
  open: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  message: string;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({ open, onConfirm, onCancel, message }) => {
  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogContent className="p-6">
        <DialogHeader>
          <DialogTitle className="text-lg font-bold">Confirm Action</DialogTitle>
        </DialogHeader>
        <p className="mt-2">{message}</p>
        <div className="flex justify-end mt-4">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button className="ml-2" onClick={onConfirm}>
            Confirm
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmationModal;