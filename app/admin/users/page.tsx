"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Search, User, Mail, Phone, MapPin, Building, Shield, CheckCircle, XCircle, Printer } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { UserService, UserDetails } from "@/services/user.service";

const UsersPage = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [searchId, setSearchId] = useState<string>("");
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  const userService = new UserService();
  //Needs looking into
  const handleFetchUserDetails = async (id: string) => {
    if (!id.trim()) {
      toast({
        title: "Invalid Input",
        description: "Please enter a valid user ID or parcel ID.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      let details;
      const userIdRegex = /^\d+$/; // Regex to check if the string contains only digits

      if (userIdRegex.test(id)) {
        // If input is numeric, treat as user ID
        details = await userService.getUserDetailsById(id);
      } else {
        // If input is not numeric, treat as parcel ID
        details = await userService.getUserDetailsByParcelId(id);
      }

      setUserDetails(details);

      if (!details) {
        toast({
          title: "User Not Found",
          description: "No user found with the provided ID.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching user details:", error);
      toast({
        title: "Error",
        description: "Failed to fetch user details. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">User Search</h1>
        <p className="text-muted-foreground">Search for a user by User ID or Parcel ID to view their details</p>
      </div>

      {/* Search Box */}
      <Card className={"animate-in fade-in slide-in-from-bottom duration-300"}>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Enter user ID or parcel ID..."
                className="pl-9"
                value={searchId}
                onChange={(e) => setSearchId(e.target.value)}
                onKeyUp={async (e: React.KeyboardEvent<HTMLInputElement>) => {
                  if (e.key === "Enter") {
                    await handleFetchUserDetails((e.target as HTMLInputElement).value);
                  }
                }}
              />
            </div>
            <Button
              onClick={async () => {
                await handleFetchUserDetails(searchId);
              }}
              disabled={isLoading}
            >
              {isLoading ? "Searching..." : "Search User"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* User Details */}
      {userDetails && (
        <Card key={userDetails.id}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                User Details
              </CardTitle>
              <div className="flex gap-2">
                {userDetails.receive_marketing && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Mail className="h-3 w-3" />
                    Marketing Enabled
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Info */}
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-1">
                <label className="text-sm text-muted-foreground">User ID</label>
                <p className="font-medium">{userDetails.id}</p>
              </div>
              <div className="space-y-1">
                <label className="text-sm text-muted-foreground">First Name</label>
                <p className="font-medium">{userDetails.first_name}</p>
              </div>
              <div className="space-y-1">
                <label className="text-sm text-muted-foreground">Last Name</label>
                <p className="font-medium">{userDetails.last_name}</p>
              </div>
            </div>

            <Separator />

            {/* Contact Details */}
            <div className="space-y-4">
              <h3 className="font-semibold flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Contact Information
              </h3>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Email</p>
                    <p className="font-medium">{userDetails.email}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Phone Number</p>
                    <p className="font-medium">
                      {userDetails.phone_number && userDetails.phone_number.length > 4
                        ? `${userDetails.phone_number.slice(0, 4)} ${userDetails.phone_number.slice(4)}`
                        : userDetails.phone_number}
                    </p>
                  </div>
                </div>
                {userDetails.country_code && (
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Country Code</p>
                    <p className="font-medium">{userDetails.country_code}</p>
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {/* Address Details */}
            <div className="space-y-4">
              <h3 className="font-semibold flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Address Information
              </h3>
              {userDetails.address || userDetails.city || userDetails.deliverTo ? (
                <div className="grid gap-4 md:grid-cols-2">
                  {userDetails.deliverTo && (
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Deliver To</p>
                      <p className="font-medium">{userDetails.deliverTo}</p>
                    </div>
                  )}
                  {userDetails.company && (
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm text-muted-foreground">Company</p>
                        <p className="font-medium">{userDetails.company}</p>
                      </div>
                    </div>
                  )}
                  {userDetails.address && (
                    <div className="space-y-1 md:col-span-2">
                      <p className="text-sm text-muted-foreground">Address</p>
                      <p className="font-medium">{userDetails.address}</p>
                    </div>
                  )}
                  {userDetails.city && (
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">City</p>
                      <p className="font-medium">{userDetails.city}</p>
                    </div>
                  )}
                  {userDetails.stateProvince && (
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">State/Province</p>
                      <p className="font-medium">{userDetails.stateProvince}</p>
                    </div>
                  )}
                  {userDetails.postalCode && (
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Postal Code</p>
                      <p className="font-medium">{userDetails.postalCode}</p>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-muted-foreground">No address information available</p>
              )}
            </div>

            <Separator />

            {/* Policy Agreements */}
            <div className="space-y-4">
              <h3 className="font-semibold flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Policy Agreements
              </h3>
              <div className="flex flex-wrap items-center justify-between gap-2">
                <div className="flex flex-wrap items-center gap-2">
                  <Badge variant={userDetails.signed_shipping_policy ? "secondary" : "outline"} className="flex items-center gap-1">
                    {userDetails.signed_shipping_policy ? <CheckCircle className="h-3 w-3 text-green-600" /> : <XCircle className="h-3 w-3 text-red-600" />}
                    Shipping Policy
                  </Badge>
                  <Badge variant={userDetails.signed_privacy_policy ? "secondary" : "outline"} className="flex items-center gap-1">
                    {userDetails.signed_privacy_policy ? <CheckCircle className="h-3 w-3 text-green-600" /> : <XCircle className="h-3 w-3 text-red-600" />}
                    Privacy Policy
                  </Badge>
                  <Badge variant={userDetails.receive_marketing ? "secondary" : "outline"} className="flex items-center gap-1">
                    {userDetails.receive_marketing ? <CheckCircle className="h-3 w-3 text-green-600" /> : <XCircle className="h-3 w-3 text-red-600" />}
                    Marketing Communications
                  </Badge>
                </div>
                <Button variant="default" className="flex items-center gap-2" disabled>
                  <Printer className="h-4 w-4" />
                  Print Label
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Results Message */}
      {!userDetails && !isLoading && searchId && (
        <div className="text-center py-8">
          <p className="text-muted-foreground">No user found with the provided ID</p>
        </div>
      )}
    </div>
  );
};

export default UsersPage;
