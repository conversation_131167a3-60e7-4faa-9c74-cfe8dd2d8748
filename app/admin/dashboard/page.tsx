"use client";

import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import UserOverview from "@/components/app/admin/user-overview";
import ParcelOverview from "@/components/app/admin/parcel-overview";
import { AdminService } from "@/services/admin.service";
import { DashboardParcelData } from "@/data/models/dashboard.model";

export default function DashboardPage() {
  const [userData, setUserData] = useState<CountryData[] | null>(null);
  const [parcelData, setParcelData] = useState<DashboardParcelData | null>(
    null,
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const adminService = new AdminService();

    Promise.all([
      adminService.getDashboardUserData(),
      adminService.getDashboardParcelData(),
    ])
      .then(([userData, parcelData]) => {
        setUserData(userData);
        setParcelData(parcelData);
        setLoading(false);
      })
      .catch((err) => {
        setError(err.message || "Failed to load dashboard data.");
        setLoading(false);
      });
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-3xl font-bold mb-6">Dashboard</h1>
        <Card className="col-span-2">
          <CardContent>
            <h2>Loading dashboard data...</h2>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-3xl font-bold mb-6">Dashboard</h1>
        <Card className="col-span-2">
          <CardContent>Error: {error}</CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <UserOverview data={userData} />
        {parcelData && <ParcelOverview data={parcelData} />}
      </div>
    </div>
  );
}
