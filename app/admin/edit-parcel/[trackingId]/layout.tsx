import {supabase} from "@/lib/supabase_client.ts";
import React from "react";

export default async function EditParcelLayout({children}: { children: React.ReactNode }) {
  return children;
}

export async function generateStaticParams() {
  const {data, error} = await supabase.from("users_parcels_details").select("tracking_id");
  return data?.map((item) => ({
    trackingId: item.tracking_id,
  })) || [];
}