"use client";

import { supabase } from "@/lib/supabase_client.ts";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { z } from "zod";
import { CreditCard, DollarSign, Package2, SlidersVertical, Truck } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select.tsx";
import { Checkbox } from "@/components/ui/checkbox.tsx";
import { convertToPascalCasing } from "@/lib/utils.ts";
import { FeeField, ShippingMethod, ShippingMethodWithFees } from "@/data/models/shipping_methods.model.ts";
import { ParcelService } from "@/services/parcel.service.ts";
import { Admin } from "@/data/models/admin.model.ts";
import { getParcelActionNotification } from "@/lib/email.ts";
import { AdminService } from "@/services/admin.service";


const formSchema = z
  .object({
    tracking_id: z.string().min(1, { message: "Tracking ID is required" }),
    user_id: z.number().min(1, { message: "User ID is required" }),
    package_content: z.string().min(1, { message: "Package content is required" }),
    shipping_address: z.string().min(1, { message: "Shipping address is required" }),
    vendor_tracking: z.string().min(1, { message: "Vendor tracking number is required" }),
    bought_from: z.string().min(1, { message: "Bought from is required" }),
    status: z.string().min(1, { message: "Status is required" }),
    package_weight: z.number(),
    length: z.coerce.number(),
    width: z.coerce.number(),
    height: z.coerce.number(),
    received_at: z.string().min(1, { message: "Received at is required" }),
    received_on: z.string().date().min(1, { message: "Received on is required" }),
    country: z.string().min(1, { message: "Country is required" }),
    duty: z.number(),
    invoice: z.string().optional().nullable(),
    est_due_date: z.string().optional().nullable(),
    payment_date: z.string().optional().nullable(),
    delivered_date: z.string().optional().nullable(),
    storage_fee: z.number().nullable(),
    payment_method: z.string().optional().nullable(),
    total_due:z.number(),
    is_dangerous_goods: z.boolean(),
    dangerous_goods_price: z.number().optional().nullable(),
    is_insured: z.boolean(),
    is_discounted: z.boolean(),
    discount_amount: z.number().optional().nullable(),
    items_value: z.number(),
    is_payed_for: z.boolean(),
    third_party_tracking: z.string().optional(),
    shipping_options: z.array(z.object({
      id: z.number(),
      method_name: z.string(),
      handlingFee: z.number(),
      consolidationFee: z.number(),
      shippingFee: z.number(),
      insuranceFee: z.number()
    }))
  })
  .refine(
    (data) => {
      return !(data.is_dangerous_goods && !data.dangerous_goods_price);
    },
    {
      message: "Dangerous goods fee is required since item is marked as dangerous",
      path: ["dangerous_goods_price"] // path of error
    }
  )
  .refine((data) => {
    return data.shipping_options.length > 0;
  }, { message: "Select at least one shipping option", path: ["shipping_options"] });

const parcelService = new ParcelService();
const adminService = new AdminService();

export default function EditParcelDetailsPage({ params }: { params: { trackingId: string } }) {
  const id = params.trackingId;
  const { toast } = useToast();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [adminData, setAdminData] = useState<Admin | null>(null);
  const [parcelData, setParcelData] = useState(null);
  const [shippingOptions, setShippingOptions] = useState<ShippingMethod[]>([]);
  const [selectedShippingMethods, setSelectedShippingMethods] = useState<ShippingMethodWithFees[]>([]);
  // @ts-ignore


  const handleShippingMethodChange = (method: ShippingMethod, isChecked: boolean): void => {
    if (isChecked) {
      setSelectedShippingMethods(prev => [...prev, {
        ...method,
        handlingFee: 0,
        consolidationFee: 0,
        shippingFee: 0,
        insuranceFee: 0
      }]);
    } else {
      setSelectedShippingMethods(prev =>
        prev.filter(selectedMethod => selectedMethod.id !== method.id)
      );
    }
  };

  const handleShippingFeeChange = (methodId: number, field: FeeField, value: string): void => {
    setSelectedShippingMethods(prev =>
      prev.map(method =>
        method.id === methodId
          ? { ...method, [field]: value }
          : method
      )
    );
  };

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema)
  });

  useEffect(() => {
    async function fetchParcelData() {
      let { data, error } = await supabase.from("users_parcels_details").select("*").eq("tracking_id", id).single();
      if (error) {
        console.error("Error fetching parcel data:", error);
        return;
      }
      data = {
        ...data,
        length: Number(data.dimensions.split("x")[0].replace("cm", "")),
        width: Number(data.dimensions.split("x")[1].replace("cm", "")),
        height: Number(data.dimensions.split("x")[2].replace("cm", "")),
      };
      console.log("Parcel data:", data);
      setSelectedShippingMethods(data.shipping_options);
      setParcelData(data);
      form.reset(data);
    }

    fetchParcelData();
  }, [id]);

  useEffect(() => {
    const authData = sessionStorage.getItem("_auth_admin");
    if (authData) {
      const parsedAuthData = JSON.parse(authData);
      setAdminData(parsedAuthData);
      // Get shipping options after admin data is set
      const getShippingOptions = async () => {
        try {
          const shippingOptions = await parcelService.getShippingMethods(parsedAuthData.permissions.country_list[0]);
          console.log("shipping_options: ", shippingOptions);
          setShippingOptions(shippingOptions);
        } catch (error) {
          console.error("Error loading shipping options:", error);
        }
      };
      getShippingOptions();
    } else {
      router.push("/admin");
    }
  }, [router]);

  const onSubmit = async (data: z.infer<typeof formSchema>) => {

    if (selectedShippingMethods.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one shipping option.",
        variant: "destructive"
      });
      return;
    }
    const shippingMethods = selectedShippingMethods.map(method => ({
      id: method.id,
      method_name: method.method_name,
      handlingFee: parseFloat(method.handlingFee.toString() || ""),
      consolidationFee: parseFloat(method.consolidationFee.toString() || ""),
      shippingFee: parseFloat(method.shippingFee.toString() || ""),
      insuranceFee: parseFloat(method.insuranceFee.toString() || "")
    }));

    let processedData = {
      id: parcelData.id,
      uuid: parcelData.uuid,
      user_id: data.user_id,
      invoice: data.invoice,
      est_due_date: data.est_due_date,
      payment_date: data.payment_date,
      delivered_date: data.delivered_date,
      payment_method: data.payment_method,
      total_due: data.total_due,
      items_value: data.items_value,
      is_insured: data.is_insured,
      is_payed_for: data.is_payed_for,
      is_discounted: data.is_discounted,
      discount_amount: data.discount_amount,
      country: data.country,
      package_content: data.package_content,
      received_at: data.received_at,
      received_on: data.received_on,
      shipping_address: data.shipping_address,
      vendor_tracking: data.vendor_tracking,
      bought_from: data.bought_from,
      status: data.status,
      duty: data.duty,
      third_party_tracking: data.third_party_tracking,
      is_dangerous_goods: data.is_dangerous_goods,
      dangerous_goods_price: data.dangerous_goods_price,
      tracking_id: data.tracking_id,
      dimensions: `${data.length}cmx${data.width}cmx${data.height}cm`,
      package_weight: data.package_weight,
      shipping_options: shippingMethods,
      storage_fee: data.storage_fee,
      created_at: parcelData.created_at,
      updated_at: new Date().toISOString()
    };

    console.log(`Form data:`, processedData);
    try {
      setIsLoading(true);
      const { error } = await supabase.from("users_parcels_details").update(processedData).eq("tracking_id", id);
      if (error) {
        toast({
        title: "Update Failed",
        description: "Error updating parcel details." + error.message,
        variant: "destructive"
      });
        throw error;
      }
      toast({
        title: "Update Successful",
        description: "Parcel details updated successfully.",
        variant: "default"
      });
      const userDetails = await adminService.getUserDetails(processedData?.uuid);
      const actionEmail = getParcelActionNotification(userDetails?.first_name, userDetails?.last_name, "Updated", processedData.tracking_id);
      await parcelService.sendEmail(userDetails?.email, "Parcel Updated", actionEmail);
      setIsLoading(false);
      router.push("/admin/edit-parcel");
    } catch (error: any) {
      console.error("Update failed:", error);
      setIsLoading(false);
      toast({
        title: "Update Failed",
        description: error.message || "An error occurred. Please try again.",
        variant: "destructive"
      });
    }
  };

  if (!parcelData) {
    return <h1 className="m-8 text-3xl font-bold tracking-tight">Fetching Parcel Data...</h1>;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit, (errors) => console.log(errors))}>
        <div className="max-w-7xl mx-auto p-6">
          <div className="mb-8 text-start">
            <h1 className="text-3xl font-bold tracking-tight">Edit Parcel</h1>
            <p className="text-muted-foreground mt-2">Update parcel details and shipping information</p>
          </div>

          <div className="grid lg:grid-cols-2 gap-6">

            <div className={"flex flex-col gap-6"}>
              {/* Parcel Information Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package2 className="h-5 w-5" />
                    Parcel Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="tracking_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tracking ID</FormLabel>
                        <FormControl>
                          <Input placeholder="Tracking ID" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="user_id"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormLabel>User ID</FormLabel>
                        <FormControl>
                          <Input type={"number"} placeholder="Enter User ID" {...field} className="w-full" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="package_content"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Package Content</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Package contents" className="min-h-[100px]" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="shipping_address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Shipping Address</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Shipping address" className="min-h-[100px]" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div>
                    <label className="text-sm font-medium mb-1 block">Dimensions (L x W x H in cm)</label>
                    <div className="grid grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name="length"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input type="number" placeholder="Length (cm)" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="width"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input type={"number"} placeholder="Width (cm)" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="height"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input type={"number"} placeholder="Height (cm)" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="package_weight"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Weight (kg)</FormLabel>
                          <FormControl>
                            <Input type="number" placeholder="Weight" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="items_value"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Items Value</FormLabel>
                          <FormControl>
                            <Input type="number" placeholder="Value" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="received_at"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Received At</FormLabel>
                          <FormControl>
                            <Input placeholder="Location" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="received_on"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Received Date</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country</FormLabel>
                        <FormControl>
                          <Input type={"text"} placeholder="Country" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Payment Information Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Payment Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="invoice"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Invoice Reference</FormLabel>
                        <FormControl>
                          <Input placeholder="Invoice reference" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="payment_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Payment Date</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="payment_method"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Payment Method</FormLabel>
                          <FormControl>
                            <Input placeholder="Payment method" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="is_payed_for"
                    render={({ field }) => (
                      <FormItem className="flex items-center gap-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel>Payment Completed (is_payed_for)</FormLabel>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>


            </div>
            <div className={"flex flex-col gap-6"}>
              {/* Shipping & Delivery Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Truck className="h-5 w-5" />
                    Shipping & Delivery
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="vendor_tracking"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Vendor Tracking</FormLabel>
                          <FormControl>
                            <Input placeholder="Vendor tracking" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="bought_from"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bought From</FormLabel>
                          <FormControl>
                            <Input placeholder="Seller/store" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Status</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="Received">Received</SelectItem>
                              <SelectItem value="Processing">Processing</SelectItem>
                              <SelectItem value="In Progress">In progress</SelectItem>
                              <SelectItem value="Shipped">Shipped</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="third_party_tracking"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Third Party Tracking</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter third party tracking" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="delivered_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Delivered Date</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="est_due_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Estimated Due Date</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="duty"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Duty Amount</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="storage_fee"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Storage Fee</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="total_due"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Total Due</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="space-y-4">
                    <div className="flex place-items-center gap-4">
                      <FormField
                        control={form.control}
                        name="is_dangerous_goods"
                        render={({ field }) => (
                          <FormItem className="flex items-center gap-2">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <FormLabel className={"pb-1"}>Dangerous Goods</FormLabel>
                          </FormItem>
                        )}
                      />
                      {form.watch("is_dangerous_goods") && (
                        <FormField
                          control={form.control}
                          name="dangerous_goods_price"
                          render={({ field }) => (
                            <FormItem className="flex-1">
                              <FormControl>
                                <Input type={"number"} placeholder="Dangerous goods fee" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>

                    <FormField
                      control={form.control}
                      name="is_insured"
                      render={({ field }) => (
                        <FormItem className="flex items-center gap-2">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <FormLabel className={"pb-1"}>Is Insured</FormLabel>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="is_discounted"
                      render={({ field }) => (
                        <FormItem className="flex items-center gap-2">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <FormLabel className={"pb-1"}>Is Discounted</FormLabel>
                        </FormItem>
                      )}
                    />
                    {form.watch("is_discounted") && (
                      <FormField
                        control={form.control}
                        name="discount_amount"
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormControl>
                              <Input type={"number"} placeholder="Discount amount" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Shipping Options Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <SlidersVertical className="h-5 w-5" />
                    Shipping Options
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-4 mb-6">
                    {shippingOptions.map((option) => (
                      <div className="flex items-center" key={option.id}>
                        <Checkbox
                          id={`shipping-option-${option.id}`}
                          checked={selectedShippingMethods.some(m => m.id === option.id)}
                          onCheckedChange={(isChecked: boolean) =>
                            handleShippingMethodChange(option, isChecked)
                          }
                        />
                        <label htmlFor={`shipping-option-${option.id}`} className="ml-2">
                          {convertToPascalCasing(option.method_name)}
                        </label>
                      </div>
                    ))}
                  </div>

                  {selectedShippingMethods.map((method) => (
                    <div key={method.id} className="mb-8">
                      <h3 className="text-lg font-semibold mb-4">
                        {convertToPascalCasing(method.method_name)} Fees
                      </h3>
                      <div className="grid grid-cols-2 gap-4">
                        <FormItem>
                          <FormLabel>Handling Fee</FormLabel>
                          <FormControl>
                            <Input
                              type="text"
                              placeholder="Enter handling fee"
                              value={method.handlingFee}
                              onChange={(e) =>
                                handleShippingFeeChange(method.id, "handlingFee", e.target.value)
                              }
                            />
                          </FormControl>
                        </FormItem>
                        <FormItem>
                          <FormLabel>Consolidation Fee</FormLabel>
                          <FormControl>
                            <Input
                              type="text"
                              placeholder="Enter consolidation fee"
                              value={method.consolidationFee}
                              onChange={(e) =>
                                handleShippingFeeChange(method.id, "consolidationFee", e.target.value)
                              }
                            />
                          </FormControl>
                        </FormItem>
                        <FormItem>
                          <FormLabel>Shipping Fee</FormLabel>
                          <FormControl>
                            <Input
                              type="text"
                              placeholder="Enter shipping fee"
                              value={method.shippingFee}
                              onChange={(e) =>
                                handleShippingFeeChange(method.id, "shippingFee", e.target.value)
                              }
                            />
                          </FormControl>
                        </FormItem>
                        <FormItem>
                          <FormLabel>Insurance Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="text"
                              placeholder="Enter insurance amount"
                              value={method.insuranceFee}
                              onChange={(e) =>
                                handleShippingFeeChange(method.id, "insuranceFee", e.target.value)
                              }
                            />
                          </FormControl>
                        </FormItem>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="mt-6 flex gap-2 justify-end">
            <Button variant={"outline"} className="w-full sm:w-auto" onClick={() => {
              router.push("/admin/edit-parcel");
            }}>
              Cancel Changes
            </Button>
            <Button className="w-full sm:w-auto" type="submit">
              Save Changes
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}




