"use client";

import React, { useState } from "react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Search, Package2, MapPin, Calendar, Weight, Box, DollarSign, Truck, Shield, AlertTriangle } from "lucide-react";
import { supabase } from "@/lib/supabase_client";
import { toast } from "@/hooks/use-toast";
import { ParcelService } from "@/services/parcel.service";
import { AdminService } from "@/services/admin.service";
import { getParcelActionNotification } from "@/lib/email";

const EditParcelPage = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [searchId, setSearchId] = useState<string>("");
  const [parcels, setParcels] = useState<any>(null);
  const parcelService = new ParcelService();
  const adminService = new AdminService();

  const isValidUUID = (id: string) => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  };

  const getParcelDetails = async (id: string) => {
    let data, error;
    const userIdRegex = /^\d+$/; // Regex to check if the string contains only digits

    if (userIdRegex.test(id)) {
      const userId = parseInt(id, 10);
      const response = await supabase.from("users_parcels_details").select().eq("user_id", userId).order("received_on", { ascending: false });
      data = response.data;
      error = response.error;
    } else if (isValidUUID(id)) {
      const response = await supabase.from("users_parcels_details").select().eq("uuid", id).order("received_on", { ascending: false });
      data = response.data;
      error = response.error;
    } else {
      const response = await supabase.from("users_parcels_details").select().eq("tracking_id", id).order("received_on", { ascending: false });
      data = response.data;
      error = response.error;
    }

    if (error) {
      console.error("Supabase query error:", error); // Keep error logging for now
      throw new Error(`Error fetching parcel details: ${error.message}`);
    }

    // Return data or an empty array if no results found
    return data || [];
  };

  const handleFetchParcelDetails = async (id: string) => {
    setIsLoading(true);
    const parcelDetails = await getParcelDetails(id);
    setParcels(parcelDetails);
    // console.log(parcelDetails)
    setIsLoading(false);
  };

  const handleDeleteParcel = async (id: string) => {
    setIsLoading(true);
    const { error } = await supabase.from("users_parcels_details").update({ is_deleted: true }).eq("tracking_id", id);
    if (error) {
      toast({
        title: "Delete Failed",
        description: "Error deleting parcel details." + error.message,
        variant: "destructive"
      });
      throw error;
    }
    const userDetails = await adminService.getUserDetails(parcels[0].uuid);
    if (userDetails?.email) {
      const actionEmail = getParcelActionNotification(userDetails?.first_name, userDetails?.last_name, "Deleted", id);
      await parcelService.sendEmail(userDetails.email, "Parcel Deleted", actionEmail);
    } else {
      console.warn(`Could not send delete notification for parcel ${id}: User email not found.`);
      toast({ title: "Warning", description: "Could not send delete notification: User email not found.", variant: "default" });
    }
    toast({
      title: "Delete Successful",
      description: "Parcel details deleted successfully.",
      variant: "default"
    });
    setIsLoading(false);
    window.location.reload();
  };

  // Mock data - replace with actual API call
  const searchParcel = (id: string) => {
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setParcels([
        {
          id: 1,
          tracking_id: "TRACK1234876546",
          uuid: "UUID-12345-ABCDE",
          dimensions: "45x35x10",
          package_weight: 2.5,
          package_content: "Electronics - Laptop",
          items_value: 1200,
          received_at: "New York Warehouse",
          received_on: "2024-03-15",
          shipping_address: "123 Main St, New York, NY 10001",
          vendor_tracking: "VND987654",
          delivered_date: null,
          bought_from: "Tech Store Ltd",
          est_due_date: "2024-03-20",
          status: "In Transit",
          duty: 50,
          invoice: "INV-2024-001",
          payment_date: "2024-03-16",
          payment_method: "Credit Card",
          shipment_price: 150,
          storage_fee: 20,
          handling_fee: 30,
          consolidation_fee: 25,
          insurance: 45,
          total_due: 320,
          is_insured: true,
          is_payed_for: true,
          is_dangerous_goods: false,
          dangerous_goods_price: 0,
          is_air_cargo: true,
          is_sea_cargo: false,
          is_discounted: true,
          discount_amount: 25,
          selected_shipping_method: "Express Air",
          has_insurance: true,
          country: "United States",
          consolidation_status: 1,
        },
      ]);
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Edit Parcel</h1>
        <p className="text-muted-foreground">Search for a parcel by Tracking ID, UUID or User ID to view and edit its details</p>
      </div>

      {/* Search Box */}
      <Card className={"animate-in fade-in slide-in-from-bottom duration-300"}>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Enter tracking ID, UUID, or user ID..."
                className="pl-9"
                onChange={(e) => {
                  // console.log(e.target.value)
                  setSearchId(e.target.value);
                }}
                onKeyUp={async (e: React.KeyboardEvent<HTMLInputElement>) => {
                  if (e.key === "Enter") {
                    // searchParcel((e.target as HTMLInputElement).value);
                    await handleFetchParcelDetails((e.target as HTMLInputElement).value.toString());
                  }
                }}
              />
            </div>
            <Button
              onClick={async () => {
                console.log(searchId);
                await handleFetchParcelDetails(searchId);
              }}
              disabled={isLoading}
            >
              Search Parcel
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Parcel Details */}

      {parcels && parcels.length === 0 && <div className="text-center">No Parcels Found</div>}

      {parcels &&
        parcels.length > 0 &&
        parcels.map((parcel: any) => (
          <Card key={parcel.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Package2 className="h-5 w-5" />
                  Parcel Details
                </CardTitle>
                <div className="flex gap-2">
                  <Badge variant={parcel.status === "In Transit" ? "default" : "secondary"}>{parcel.status}</Badge>
                  {parcel.is_dangerous_goods && (
                    <Badge variant="destructive" className="flex items-center gap-1">
                      <AlertTriangle className="h-3 w-3" />
                      Dangerous Goods
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Info */}
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-1">
                  <label className="text-sm text-muted-foreground">Tracking ID</label>
                  <p className="font-medium">{parcel.tracking_id}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm text-muted-foreground">UUID</label>
                  <p className="font-medium">{parcel.uuid}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm text-muted-foreground">Vendor Tracking</label>
                  <p className="font-medium">{parcel.vendor_tracking}</p>
                </div>
              </div>

              <Separator />

              {/* Package Details */}
              <div className="grid gap-4 md:grid-cols-3">
                <div className="flex items-center gap-2">
                  <Box className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Dimensions</p>
                    <p className="font-medium">{parcel.dimensions} cm</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Weight className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Weight</p>
                    <p className="font-medium">{parcel.package_weight} kg</p>
                  </div>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Content</p>
                  <p className="font-medium">{parcel.package_content}</p>
                </div>
              </div>

              <Separator />

              {/* Shipping Details */}
              <div className="space-y-4">
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
                  <div>
                    <p className="text-sm text-muted-foreground">Shipping Address</p>
                    <p className="font-medium">{parcel.shipping_address}</p>
                  </div>
                </div>
                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <p className="text-sm text-muted-foreground">Country</p>
                    <p className="font-medium">{parcel.country}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Shipping Method</p>
                    <p className="font-medium">{parcel.selected_shipping_method}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Bought From</p>
                    <p className="font-medium">{parcel.bought_from}</p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Financial Details */}
              <div className="space-y-4">
                <h3 className="font-semibold flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Financial Details
                </h3>
                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <p className="text-sm text-muted-foreground">Items Value</p>
                    <p className="font-medium">${parcel.items_value}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Shipment Price</p>
                    <p className="font-medium">${parcel.shipping_options[0].shippingFee}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Duty</p>
                    <p className="font-medium">${parcel.duty}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Storage Fee</p>
                    <p className="font-medium">${parcel.storage_fee ?? 0}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Handling Fee</p>
                    <p className="font-medium">${parcel.shipping_options[0].handlingFee}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Insurance</p>
                    <p className="font-medium">${parcel.shipping_options[0].insuranceFee}</p>
                  </div>
                  {parcel.is_discounted && (
                    <div>
                      <p className="text-sm text-muted-foreground">Discount</p>
                      <p className="font-medium text-green-600">-${parcel.discount_amount}</p>
                    </div>
                  )}
                  <div className="md:col-span-2">
                    <p className="text-sm font-semibold">Total Due</p>
                    <p className="text-lg font-bold">${parcel.total_due}</p>
                  </div>
                </div>
              </div>

              {/* Status Badges */}
              <div className="flex flex-wrap gap-2">
                {parcel.is_insured && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Shield className="h-3 w-3" />
                    Insured
                  </Badge>
                )}
                {parcel.is_payed_for && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <DollarSign className="h-3 w-3" />
                    Paid
                  </Badge>
                )}
                {parcel.is_air_cargo && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Truck className="h-3 w-3" />
                    Air Cargo
                  </Badge>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-4 pt-4">
                <a href={"/admin/edit-parcel/" + parcel.tracking_id}>
                  <Button>Edit Details</Button>
                </a>
                {!parcel.is_deleted || parcel.is_deleted === null ? (
                  <Button variant="outline" className="text-destructive" onClick={() => handleDeleteParcel(parcel.tracking_id)}>
                    Delete Parcel
                  </Button>
                ) : (
                  <Button variant="outline" className="text-destructive" disabled>
                    Marked as Deleted
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
    </div>
  );
};

export default EditParcelPage;
