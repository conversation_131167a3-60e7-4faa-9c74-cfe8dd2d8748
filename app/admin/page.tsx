"use client";

import { z } from "zod";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { AuthService } from "@/services/auth.service";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";

// Define the form schema
const formSchema = z.object({
  emailAddress: z.string().min(1, { message: "Email address is required" }).email({ message: "Invalid email address" }),
  password: z.string().min(1, { message: "Password is required" }),
});

// Define types for the form
type FormValues = z.infer<typeof formSchema>;

export default function AdminLogin() {
  const { toast } = useToast();
  const router = useRouter();
  const { redirectIfAdmin, loading } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      emailAddress: "",
      password: "",
    },
  });

  const onSubmit = async (data: FormValues) => {
    try {
      setIsLoading(true);
      const authService = new AuthService();
      const admin = await authService.adminLogin(data.emailAddress, data.password);

      toast({
        title: "Login Successful",
        description: `Welcome back, ${admin.email}!`,
        variant: "default",
      });

      setIsLoading(false);
      router.push("/admin/dashboard");
    } catch (error: any) {
      console.error("Login failed:", error);
      setIsLoading(false);

      toast({
        title: "Login Failed",
        description: error.message || "An error occurred. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Redirect if not admin
  useEffect(() => {
    redirectIfAdmin();
  }, [redirectIfAdmin]);

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="h-screen w-screen flex items-center justify-center bg-white transition-opacity duration-500 ease-in-out">
          <div className="text-lg font-medium text-gray-700 animate-pulse">Loading...</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-8 bg-white">
      <Card className="mx-auto w-[500px]">
        <CardHeader>
          <CardTitle className="text-2xl">Login</CardTitle>
          <CardDescription>Enter your email below to login to your account</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="emailAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} type="email" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="Enter your password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Loading..." : "Login"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
