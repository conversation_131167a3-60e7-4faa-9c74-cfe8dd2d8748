"use client";

import { z } from "zod";
import { useForm } from "react-hook-form";
import { useAuth } from "@/hooks/use-auth";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";
import { Package2, Truck } from "lucide-react";
import { Button } from "@/components/ui/button";
import { supabase } from "@/lib/supabase_client";
import { getEmailHtmlContent } from "@/lib/email";
import { Admin } from "@/data/models/admin.model";
import React, { useEffect, useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { convertToPascalCasing } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { zodResolver } from "@hookform/resolvers/zod";
import { AdminService } from "@/services/admin.service";
import { ParcelService } from "@/services/parcel.service";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ShippingMethod, ShippingMethodWithFees, FeeField } from "@/data/models/shipping_methods.model";

const parcelService = new ParcelService();

// Define the form schema
const formSchema = z
  .object({
    trackingId: z.string().min(1, { message: "Tracking ID is required" }),
    uid: z.string().min(1, { message: "UID is required" }),
    packageContent: z.string().min(1, { message: "Package content is required" }),
    shippingAddress: z.string().min(1, { message: "Shipping address is required" }),
    vendorTracking: z.string().min(1, { message: "Vendor tracking number is required" }),
    boughtFrom: z.string().min(1, { message: "Bought from is required" }),
    status: z.string().min(1, { message: "Status is required" }),
    packageWeight: z.string().min(1, { message: "Weight is required" }),
    length: z.string().min(1, { message: "Length is required" }),
    width: z.string().min(1, { message: "Width is required" }),
    height: z.string().min(1, { message: "Height is required" }),
    receivedAt: z.string().min(1, { message: "Received at is required" }),
    receivedOn: z.string().min(1, { message: "Received on is required" }),
    duty: z.string().min(1, { message: "Duty is required" }),
    thirdPartyTracking: z.string().optional(),
    isDangerous: z.boolean().optional(),
    isConsolidated: z.boolean().optional(),
    dangerousGoodsFee: z.string().optional(),
    selectedShippingMethods: z.array(z.object({
      id: z.number(),
      method_name: z.string(),
      handlingFee: z.string(),
      consolidationFee: z.string(),
      shippingFee: z.string(),
      insuranceFee: z.string(),
    }))
  })
  .refine(
    (data) => {
      if (data.isDangerous && !data.dangerousGoodsFee) {
        return false;
      }
      return true;
    },
    {
      message: "Dangerous goods fee is required since item is marked as dangerous",
      path: ["dangerousGoodsFee"], // path of error
    }
  );

const GenerateParcelForm = () => {
  const { user } = useAuth(true);
  const { toast } = useToast();
  const router = useRouter();
  const [uuid, setUuid] = useState("");
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [isDangerous, setIsDangerous] = useState(false);
  const [adminData, setAdminData] = useState<Admin | null>(null);
  const [shippingOptions, setShippingOptions] = useState<ShippingMethod[]>([]);
  const [selectedShippingMethods, setSelectedShippingMethods] = useState<ShippingMethodWithFees[]>([]);

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      trackingId: "",
      uid: "",
      packageContent: "",
      shippingAddress: "",
      vendorTracking: "",
      boughtFrom: "",
      status: "",
      packageWeight: "",
      length: "",
      width: "",
      height: "",
      receivedAt: "",
      receivedOn: "",
      duty: "",
      thirdPartyTracking: "",
      isDangerous: false,
      isConsolidated: false,
      dangerousGoodsFee: "",
      selectedShippingMethods: []
    },
  });

  const generateTrackingId = () => {
    const prefix = "PKG";
    const timestamp = new Date().toISOString().replace(/[-:]/g, "").slice(2, 14);
    const randomString = Math.random().toString(36).substring(2, 10).toUpperCase();
    return `${prefix}${timestamp}${randomString}`;
  };

  const updateTrackingId = () => {
    const newTrackingId = generateTrackingId();
    form.setValue("trackingId", newTrackingId);
  };

  const joinAddressParts = (parts: Array<any>) => {
    return parts.filter(Boolean).join(", ");
  };

  const fetchUserAddress = async (userId: string) => {
    const { data, error } = await supabase.from("users_address").select("uuid, FirstName, LastName, Address, City, StateProvince, PostalCode, DeliverTo").eq("user_id", userId).single();

    if (error) throw new Error(`Error fetching user address: ${error.message}`);
    if (!data) throw new Error("Address not found for the user");

    const { FirstName, LastName, Address, City, StateProvince, PostalCode, DeliverTo, uuid } = data;
    setUuid(uuid);
    return {
      formattedAddress: `${FirstName} ${LastName}, ${joinAddressParts([Address, City, StateProvince, PostalCode, DeliverTo])}`,
    };
  };

  const handleFetchAddress = async () => {
    setLoading(true);
    try {
      const { formattedAddress } = await fetchUserAddress(form.getValues("uid"));
      form.setValue("shippingAddress", formattedAddress);
    } catch (error) {
      console.error(`Error fetching address: ${error}`);
      toast({
        title: "Error",
        description: "Failed to fetch address. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const processFormData = (data: z.infer<typeof formSchema>) => {
    // Set fees for each shipping method
    const shippingMethods = selectedShippingMethods.map(method => ({
      id: method.id,
      method_name: method.method_name,
      handlingFee: parseFloat(method.handlingFee.toString() || ""),
      consolidationFee: parseFloat(method.consolidationFee.toString() || ""),
      shippingFee: parseFloat(method.shippingFee.toString() || ""),
      insuranceFee: parseFloat(method.insuranceFee.toString() || "")
    }));

    return {
      uid: data.uid,
      packageContent: data.packageContent,
      vendorTracking: data.vendorTracking,
      boughtFrom: data.boughtFrom,
      status: data.status,
      packageWeight: parseFloat(data.packageWeight),
      dimensions: `${data.length}cmx${data.width}cmx${data.height}cm`,
      isDangerousGoods: data.isDangerous || false,
      isConsolidated: data.isConsolidated || false,
      receivedAt: data.receivedAt,
      receivedOn: data.receivedOn,
      duty: parseFloat(data.duty || "0"),
      dangerousGoodsFee: parseFloat(data.dangerousGoodsFee || "0"),
      shippingOptions: shippingMethods,
      originCountry: adminData?.permissions.country_list[0],
    };
  };

  const generateParcel = async (formData: z.infer<typeof formSchema>): Promise<boolean> => {
    const processedData = processFormData(formData);
    
    const parcelData = {
      ...processedData,
      shippingAddress: formData.shippingAddress,
      trackingId: formData.trackingId,
      thirdPartyTracking: formData.thirdPartyTracking,
    };

    try {
      const userId = formData.uid;

      const adminService = new AdminService();
      const parcelService = new ParcelService();

      // Insert parcel data
      await parcelService.insertParcelData(parcelData, uuid, userId);

      // Fetch user details
      const userDetails = await adminService.getUserDetails(uuid);

      if (!userDetails.first_name || !userDetails.last_name || !userDetails.email) {
        throw new Error('User details are incomplete');
      }

      // Prepare email HTML
      const emailHtml = getEmailHtmlContent(
        userDetails.first_name,
        userDetails.last_name,
        parcelData.trackingId
      );

      // Reset form after successful submission
      form.reset();

      setSelectedShippingMethods([]); // Reset selected shipping methods

      // Send email to user
      await parcelService.sendEmail(userDetails.email, "YOUR PARCEL!", emailHtml);
      return true;
    } catch (error) {
      console.error(`Error generating parcel: ${error}`);
      return false;
    }
  };

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    if (selectedShippingMethods.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one shipping option.",
        variant: "destructive",
      });
      return;
    }

    setGenerating(true);
    try {
      const generated = await generateParcel(data);
      if (generated) {
        toast({
          title: "Success",
          description: "Parcel generated successfully!",
          variant: "default",
        });
      }
    } catch (error) {
      console.error(`Error generating parcel: ${error}`);
      toast({
        title: "Error",
        description: "Failed to generate parcel. Please try again.",
        variant: "destructive",
      });
    } finally {
      setGenerating(false);
    }
  };

  const handleShippingMethodChange = (method: ShippingMethod, isChecked: boolean): void => {
    if (isChecked) {
      setSelectedShippingMethods(prev => [...prev, {
        ...method,
        handlingFee: 0,
        consolidationFee: 0,
        shippingFee: 0,
        insuranceFee: 0,
        duty: 0
      }]);
    } else {
      setSelectedShippingMethods(prev => 
        prev.filter(selectedMethod => selectedMethod.id !== method.id)
      );
    }
  };

  const handleShippingFeeChange = (methodId: number, field: FeeField, value: string): void => {
    setSelectedShippingMethods(prev => 
      prev.map(method => 
        method.id === methodId 
          ? { ...method, [field]: value }
          : method
      )
    );
  };

useEffect(() => {
  // Helper function to get cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || null;
    }
    return null;
  };

  const authCookie = getCookie("_auth_admin");
  
  if (authCookie) {
    try {
      const parsedAuthData = JSON.parse(authCookie);
      setAdminData(parsedAuthData);
      
      // Get shipping options after admin data is set
      const getShippingOptions = async () => {
        try {
          const shippingOptions = await parcelService.getShippingMethods(
            parsedAuthData.permissions.country_list[0]
          );
          console.log("shipping_options: ", shippingOptions);
          setShippingOptions(shippingOptions);
        } catch (error) {
          console.error("Error loading shipping options:", error);
        }
      };
      
      getShippingOptions();
    } catch (error) {
      console.error("Error parsing admin cookie:", error);
      router.push("/admin");
    }
  } else {
    router.push("/admin");
  }
}, [router]);

  // Separate effect for tracking ID
  useEffect(() => {
    updateTrackingId();
  }, []);

  if (!adminData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="max-w-7xl mx-auto p-6">
          <div className="mb-8 text-start">
            <h1 className="text-3xl font-bold tracking-tight">Generate Parcel</h1>
            <p className="text-muted-foreground mt-2">Enter parcel details to generate shipping information</p>
          </div>

          <div className="grid lg:grid-cols-2 gap-6">
            {/* Parcel Information Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package2 className="h-5 w-5" />
                  Parcel Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="trackingId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tracking ID</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter tracking ID" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex gap-4">
                  <FormField
                    control={form.control}
                    name="uid"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormLabel>UID</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter UID" {...field} className="w-full" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button variant="secondary" className="mt-8 flex-none w-1/3" type="button" onClick={handleFetchAddress} disabled={loading}>
                    {loading ? "Loading..." : "Fetch Address"}
                  </Button>
                </div>

                <FormField
                  control={form.control}
                  name="packageContent"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Package Content</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Describe package contents" className="min-h-[100px]" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="shippingAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Shipping Address</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Enter complete shipping address" className="min-h-[100px]" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Shipping Details Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Shipping Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vendorTracking"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Vendor Tracking</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter vendor tracking number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="boughtFrom"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bought From</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter seller/store name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <FormControl>
                          <Select {...field} value={field.value || ""} onValueChange={field.onChange}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select shipping status" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Received">Received</SelectItem>
                              <SelectItem value="Processing">Processing</SelectItem>
                              <SelectItem value="In Progress">In progress</SelectItem>
                              <SelectItem value="Shipped">Shipped</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="packageWeight"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Package Weight (kg)</FormLabel>
                        <FormControl>
                          <Input type="number" placeholder="Enter weight in kg" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                    control={form.control}
                    name="thirdPartyTracking"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Third Party Tracking</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter third party tracking" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                <div>
                  <label className="text-sm font-medium mb-1 block">Dimensions (L x W x H in cm)</label>
                  <div className="grid grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="length"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input placeholder="Length (cm)" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="width"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input placeholder="Width (cm)" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="height"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input placeholder="Height (cm)" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="receivedAt"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Received At</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter location" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="receivedOn"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Received On</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="duty"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Duty</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter duty amount" {...field} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="dangerous"
                      checked={isDangerous}
                      onCheckedChange={(checked) => {
                        if (typeof checked === "boolean") {
                          setIsDangerous(checked);
                          form.setValue("isDangerous", checked);
                        }
                      }}
                    />
                    <label htmlFor="dangerous" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      Dangerous Goods
                    </label>
                  </div>
                  {isDangerous && (
                    <div className="flex items-center space-x-2">
                    <FormField
                      control={form.control}
                      name="dangerousGoodsFee"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Dangerous Goods Fee</FormLabel>
                          <FormControl>
                            <Input type="text" placeholder="Enter dangerous goods fee" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="mt-6">
            {/* Shipping Options Card */}
            <Card>
            <CardHeader>
              <CardTitle>Shipping Options</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4 mb-6">
                {shippingOptions.map((option) => (
                  <div className="flex items-center" key={option.id}>
                    <Checkbox
                      id={`shipping-option-${option.id}`}
                      checked={selectedShippingMethods.some(m => m.id === option.id)}
                      onCheckedChange={(isChecked: boolean) => 
                        handleShippingMethodChange(option, isChecked)
                      }
                    />
                    <label htmlFor={`shipping-option-${option.id}`} className="ml-2">
                      {convertToPascalCasing(option.method_name)}
                    </label>
                  </div>
                ))}
              </div>

              {selectedShippingMethods.map((method) => (
                <div key={method.id} className="mb-8">
                  <h3 className="text-lg font-semibold mb-4">
                    {convertToPascalCasing(method.method_name)} Fees
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <FormItem>
                      <FormLabel>Handling Fee</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter handling fee"
                          value={method.handlingFee}
                          onChange={(e) => 
                            handleShippingFeeChange(method.id, 'handlingFee', e.target.value)
                          }
                        />
                      </FormControl>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Consolidation Fee</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter consolidation fee"
                          value={method.consolidationFee}
                          onChange={(e) => 
                            handleShippingFeeChange(method.id, 'consolidationFee', e.target.value)
                          }
                        />
                      </FormControl>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Shipping Fee</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter shipping fee"
                          value={method.shippingFee}
                          onChange={(e) => 
                            handleShippingFeeChange(method.id, 'shippingFee', e.target.value)
                          }
                        />
                      </FormControl>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Insurance Amount</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter insurance amount"
                          value={method.insuranceFee}
                          onChange={(e) => 
                            handleShippingFeeChange(method.id, 'insuranceFee', e.target.value)
                          }
                        />
                      </FormControl>
                    </FormItem>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
          </div>

          <div className="mt-6 flex justify-end">
            <Button className="w-full sm:w-auto" type="submit" disabled={generating}>
              {generating ? "Generating..." : "Generate Parcel"}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
};

export default GenerateParcelForm;
