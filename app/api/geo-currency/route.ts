export const runtime = "edge";

import { NextRequest } from "next/server";

/**
 * Edge API: /api/geo-currency
 *
 * Purpose:
 * - Provide a minimal geo -> currency lookup using Vercel Edge GeoIP (NextRequest.geo)
 * - Fallback to common request headers (useful for local dev / testing via Vercel CLI)
 * - Return { countryCode, currency, source } where currency is an ISO 4217 code
 *   or null when the country is not supported by the inline mapping.
 *
 * Notes:
 * - Keep this file small and dependency-free so the edge bundle is tiny.
 * - The mapping is intentionally minimal and focused on currencies the app supports.
 * - Local dev: populate the `x-vercel-ip-country` header (or `x-country`) to emulate geo.
 */

/* Minimal ISO 3166-1 alpha-2 country -> ISO 4217 currency mapping.
   This list is intentionally bounded to avoid pulling large constants into the edge bundle.
   Extend as needed, but prefer keeping the edge mapping small for performance. */
const COUNTRY_CODE_TO_CURRENCY: Record<string, string> = {
  GB: "GBP",
  UK: "GBP", // defensive: some callers might use 'UK' though ISO code is GB
  GH: "GHS",
  NG: "NGN",
  US: "USD",
  ZA: "ZAR",
  EG: "EGP",
  KE: "KES",
  IN: "INR",
  ID: "IDR",
  KW: "KWD",
  LR: "LRD",
  MW: "MWK",
  MY: "MYR",
  NA: "NAD",
  PK: "PKR",
  PH: "PHP",
  RW: "RWF",
  SA: "SAR",
  SL: "SLL",
  SG: "SGD",
  BW: "BWP",
  TZ: "TZS",
  TH: "THB",
  AE: "AED",
  ZM: "ZMW",
  ZW: "ZWL",
  FR: "EUR",
  DE: "EUR",
  ES: "EUR",
  IT: "EUR",
  // Add additional mappings here when you need to support more countries.
};

type GeoCurrencyResponse = {
  countryCode: string | null;
  currency: string | null;
  source: "geoip" | "header" | "error";
};

function jsonResponse(payload: GeoCurrencyResponse, status = 200) {
  return new Response(JSON.stringify(payload), {
    status,
    headers: {
      "content-type": "application/json",
      // Allow clients to cache for the session; provider uses `cache: 'no-store'` when fetching.
      // Keeping no-cache here minimizes stale results when clients rely on live geo.
      "cache-control": "no-store",
    },
  });
}

export async function GET(req: NextRequest) {
  try {
    // Attempt to read runtime geo provided by Vercel Edge (NextRequest.geo).
    // In local dev this may be undefined / null.
    // @ts-ignore - some runtimes augment NextRequest with `geo`
    const runtimeGeo = (req as any).geo || null;
    let countryCode: string | null = null;
    let source: GeoCurrencyResponse["source"] = "geoip";

    if (runtimeGeo && runtimeGeo.country) {
      countryCode = String(runtimeGeo.country).toUpperCase();
    }

    // Fallback to headers for local dev and testing. Common headers:
    // - x-vercel-ip-country (Vercel)
    // - x-country (custom)
    if (!countryCode) {
      const hdr =
        req.headers.get("x-vercel-ip-country") || req.headers.get("x-country");
      if (hdr) {
        countryCode = String(hdr).toUpperCase();
        source = "header";
      }
    }

    // Normalize empty or invalid values to null
    if (!countryCode || typeof countryCode !== "string") {
      countryCode = null;
    }

    // Map to currency if known
    const currency = countryCode ? COUNTRY_CODE_TO_CURRENCY[countryCode] ?? null : null;

    return jsonResponse({
      countryCode,
      currency,
      source,
    });
  } catch (err) {
    // On any unexpected error return an explicit error response with null values.
    return jsonResponse(
      {
        countryCode: null,
        currency: null,
        source: "error",
      },
      500,
    );
  }
}
