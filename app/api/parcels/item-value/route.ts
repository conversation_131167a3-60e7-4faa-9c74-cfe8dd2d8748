import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import {
  canAccessParcel,
  createUnauthorizedResponse,
  createForbiddenResponse,
} from "@/lib/auth-helpers";

export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { parcelId, itemValue } = body;

    if (typeof parcelId !== "number" || typeof itemValue !== "number") {
      return NextResponse.json(
        { error: "parcelId and itemValue (both numbers) are required." },
        { status: 400 },
      );
    }

    // Validate user can access this parcel (user owns it or is admin)
    const { canAccess, error: authError } = await canAccessParcel(
      parcelId.toString(),
    );

    if (!canAccess) {
      if (authError === "Authentication required") {
        return createUnauthorizedResponse(authError);
      }
      return createForbiddenResponse(authError || "Access denied");
    }

    const supabase = await createClient();

    const { data, error } = await supabase
      .from("users_parcels_details")
      .update({ items_value: itemValue })
      .eq("id", parcelId)
      .select();

    if (error) {
      return NextResponse.json(
        { error: "Error updating item value.", details: error.message },
        { status: 500 },
      );
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: "No parcel found or updated." },
        { status: 404 },
      );
    }

    return NextResponse.json({ success: true, parcel: data[0] });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
