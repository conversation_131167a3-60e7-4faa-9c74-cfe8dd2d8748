import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import {
  validateUserOwnership,
  createUnauthorizedResponse,
  createForbiddenResponse,
} from "@/lib/auth-helpers";

/**
 * POST /api/parcels/insert
 * Body: {
 *   parcelData: object,
 *   userUuid: string,
 *   userId: string
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { parcelData, userUuid, userId } = body;

    if (!parcelData || !userUuid || !userId) {
      return NextResponse.json(
        { error: "parcelData, userUuid, and userId are required." },
        { status: 400 },
      );
    }

    // Validate user ownership - users can only create parcels for themselves
    const {
      canAccess,
      error: authError,
      isAdmin,
    } = await validateUserOwnership(userUuid);

    if (!canAccess) {
      if (authError === "Authentication required") {
        return createUnauthorizedResponse(authError);
      }
      return createForbiddenResponse(authError || "Access denied");
    }

    const supabase = await createClient();

    // Insert the parcel data
    const { data, error } = await supabase
      .from("users_parcels_details")
      .insert([{ ...parcelData, user_uuid: userUuid, user_id: userId }])
      .select();

    if (error) {
      return NextResponse.json(
        { error: "Error inserting parcel data.", details: error.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ data }, { status: 201 });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
