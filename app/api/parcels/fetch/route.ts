import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

/**
 * GET /api/parcels/fetch
 * Query params:
 *   - country: string (required)
 *   - page: number (default 1)
 *   - limit: number (default 10)
 *   - userId: string (optional, fallback to authenticated user)
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);

    const country = searchParams.get("country");
    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "10", 10);
    let userId = searchParams.get("userId");

    if (!country) {
      return NextResponse.json(
        { error: "country is required." },
        { status: 400 }
      );
    }

    // If userId is not provided, use the authenticated user
    if (!userId) {
      const user = await supabase.auth.getUser();
      if (!user.data.user) {
        return NextResponse.json(
          { error: "No authenticated user found." },
          { status: 401 }
        );
      }
      userId = user.data.user.id;
    }

    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data, error, count } = await supabase
      .from("users_parcels_details")
      .select(
        `
        *,
        consolidation_parcel_requests!left (
          status
        )
      `,
        { count: "exact" }
      )
      .eq("uuid", userId)
      .eq("country", country)
      .order("received_on", { ascending: false })
      .range(from, to);

    if (error) {
      return NextResponse.json(
        { error: "Error fetching parcels.", details: error.message },
        { status: 500 }
      );
    }

    // Attach consolidation_status to each parcel
    const parcels = (data || []).map((parcel: any) => ({
      ...parcel,
      consolidation_status: parcel.consolidation_parcel_requests?.[0]?.status,
    }));

    return NextResponse.json({
      parcels,
      total: count || 0,
      page,
      limit,
    });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 }
    );
  }
}
