import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { generateConsolidateParcelCode } from "@/lib/utils";
import { requireAdmin, createUnauthorizedResponse } from "@/lib/auth-helpers";

/**
 * POST /api/parcels/consolidation-request
 * Body: { parcelIds: number[], userId: number }
 * Returns: { success: boolean, error?: string }
 */
export async function POST(request: NextRequest) {
  try {
    // Require admin authentication for consolidation requests
    try {
      await requireAdmin();
    } catch (error) {
      return createUnauthorizedResponse("Admin access required");
    }

    const supabase = await createClient();
    const body = await request.json();
    const { parcelIds, userId } = body;

    if (!Array.isArray(parcelIds) || parcelIds.length === 0 || !userId) {
      return NextResponse.json(
        { success: false, error: "parcelIds (array) and userId are required." },
        { status: 400 },
      );
    }

    const uniqueCode = generateConsolidateParcelCode();
    const now = new Date().toISOString();

    // Prepare requests for each parcel
    const requests = parcelIds.map((parcelId: number) => ({
      parcel_id: parcelId,
      status: 1, // pending
      code: uniqueCode,
      user_id: userId,
      created_at: now,
      updated_at: now,
    }));

    const { data, error } = await supabase
      .from("consolidation_parcel_requests")
      .insert(requests)
      .select();

    if (error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ success: true });
  } catch (err: any) {
    return NextResponse.json(
      { success: false, error: err.message || "Server error." },
      { status: 500 },
    );
  }
}
