import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { ConsolidationRequest, SupabaseConsolidationRequest } from "@/data/models/consolidation_requests.models";
import { ParcelModel } from "@/data/models/parcel.model";

// Status mapping for human-readable status
const statusMap: { [key: number]: string } = {
  1: "Pending",
  2: "Approved",
  3: "Completed",
  4: "Rejected",
  5: "Cancelled",
};

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Fetch consolidation requests with related user and parcel details
    const { data: requests, error } = await supabase
      .from("consolidation_parcel_requests")
      .select(
        `
        id,
        code,
        status,
        created_at,
        user_id,
        mp_users:user_id!inner (
          first_name,
          last_name,
          email
        ),
        users_parcel_details:parcel_id (
          *
        )
      `
      )
      .order("created_at", { ascending: false });

    if (error) {
      return NextResponse.json(
        { error: "Error fetching requests.", details: error.message },
        { status: 500 }
      );
    }

    if (!requests) {
      return NextResponse.json({ data: [] });
    }

    // Group requests by code
    const groupedRequests: { [key: string]: ConsolidationRequest } = {};

    (requests as unknown as SupabaseConsolidationRequest[]).forEach((request) => {
      const code = request.code;
      if (!groupedRequests[code]) {
        groupedRequests[code] = {
          id: request.id,
          code: request.code,
          userId: request.user_id,
          userName: request.mp_users
            ? `${request.mp_users.first_name} ${request.mp_users.last_name}`
            : "Unknown",
          parcels: [],
          requestDate: request.created_at,
          status: statusMap[request.status] || "Unknown",
        };
      }
      groupedRequests[code].parcels.push(request.users_parcel_details as ParcelModel);
    });

    return NextResponse.json({ data: Object.values(groupedRequests) });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 }
    );
  }
}
