import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const originCountry = searchParams.get("originCountry");

    if (!originCountry) {
      return NextResponse.json(
        { error: "originCountry is required." },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from("mp_shipping_methods")
      .select("id, method_name")
      .eq("origin_country", originCountry);

    if (error) {
      return NextResponse.json(
        { error: "Error fetching shipping methods.", details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ shippingMethods: data || [] });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 }
    );
  }
}
