import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { MailpalletLocation } from "@/services/address.service";

export async function GET(request: NextRequest, { params }: { params: { uuid: string } }) {
  const supabase = await createClient();

  const user = await supabase.auth.getUser();
  if (!user || !user.data || !user.data.user) {
    return new NextResponse("Unauthorized", { status: 401 });
  }
  const { data, error } = await supabase.from("mailpallet_locations").select("*");
  if (error) {
    return new NextResponse("Error fetching locations", { status: 500 });
  }
  return NextResponse.json(data as MailpalletLocation[]);
}
