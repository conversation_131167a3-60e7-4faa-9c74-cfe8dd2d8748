import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { requireAdmin, createUnauthorizedResponse } from "@/lib/auth-helpers";

export async function GET(request: NextRequest) {
  try {
    // Require admin authentication for admin user lookup
    try {
      await requireAdmin();
    } catch (error) {
      return createUnauthorizedResponse("Admin access required");
    }

    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const email = decodeURIComponent(searchParams.get("email") || "");

    if (!email) {
      return NextResponse.json(
        { error: "Email is required." },
        { status: 400 },
      );
    }

    // Query the mp_admins table by email
    const { data, error } = await supabase
      .from("mp_admins")
      .select("*")
      .eq("email", email)
      .single();

    if (error) {
      return NextResponse.json(
        { error: "Error fetching admin.", details: error.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ data });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
