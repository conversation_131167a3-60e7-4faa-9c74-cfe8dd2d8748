import { createClient } from "@/lib/supabase/server";
import { NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const supabase = await createClient();
  const user = await supabase.auth.getUser();
  // Check if user is authenticated
  if (!user.data.user) {
    return new Response("Unauthorized", { status: 401 });
  }

  // Query mp_admins table to check if user is admin
  const { data: admin, error } = await supabase
    .from("mp_admins")
    .select("uuid")
    .eq("uuid", user.data.user.id)
    .single();

  if (error || !admin) {
    return new Response("Forbidden: Admins only", { status: 403 });
  }

  const { searchParams } = new URL(request.url);
  const uuid = searchParams.get("uuid");

  if (!uuid) {
    return new Response("Bad Request: uuid is required", { status: 400 });
  }

  const { data: userData, error: userError } = await supabase
    .from("mp_users")
    .select("*")
    .eq("uuid", uuid)
    .single();

  if (userError || !userData) {
    return new Response("User not found", { status: 404 });
  }

  return Response.json(userData);
}
