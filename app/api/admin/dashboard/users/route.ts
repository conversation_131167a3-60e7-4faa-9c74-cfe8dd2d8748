import { createClient } from "@/lib/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const supabase = await createClient();
  const user = await supabase.auth.getUser();

  // 1. Authenticate
  if (!user.data.user) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  // 2. Authorize (admin check)
  // mp_admins.id (UUID) matches auth.users.id (UUID)
  const { data: admin, error: adminError } = await supabase
    .from("mp_admins")
    .select("id")
    .eq("id", user.data.user.id)
    .single();

  if (adminError || !admin) {
    return new NextResponse("Forbidden: Admins only", { status: 403 });
  }

  // 3. Query and shape data
  const { data, error } = await supabase
    .from("users_address")
    .select("DeliverTo");

  if (error) {
    return new NextResponse(
      `Error fetching users per country: ${error.message}`,
      { status: 500 },
    );
  }

  const usersPerCountry = data.reduce(
    (acc: Record<string, number>, curr: { DeliverTo: string }) => {
      if (!acc[curr.DeliverTo]) {
        acc[curr.DeliverTo] = 1;
      } else {
        acc[curr.DeliverTo]++;
      }
      return acc;
    },
    {},
  );

  const result = Object.entries(usersPerCountry).map(
    ([country, user_count]) => ({
      country,
      user_count,
    }),
  );

  return NextResponse.json(result);
}
