import { createClient } from "@/lib/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const supabase = await createClient();
  const user = await supabase.auth.getUser();

  // 1. Authenticate
  if (!user.data.user) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  // 2. Authorize (admin check)
  // mp_admins.id (UUID) matches auth.users.id (UUID)
  const { data: admin, error: adminError } = await supabase
    .from("mp_admins")
    .select("id")
    .eq("id", user.data.user.id)
    .single();

  if (adminError || !admin) {
    return new NextResponse("Forbidden: Admins only", { status: 403 });
  }

  // 3. Query all required data in parallel
  let [
    { data: parcelsPerCountry, error: parcelsPerCountryError },
    { data: parcelsReceived, error: parcelsReceivedError },
    { data: parcelsProcessing, error: parcelsProcessingError },
    { data: parcelsInProgress, error: parcelsInProgressError },
    { data: parcelsShipped, error: parcelsShippedError },
    { data: recentParcels, error: recentParcelsError },
  ] = await Promise.all([
    supabase.from("users_parcels_details").select("country"),
    supabase
      .from("users_parcels_details")
      .select("id")
      .eq("status", "Received"),
    supabase
      .from("users_parcels_details")
      .select("id")
      .eq("status", "Processing"),
    supabase
      .from("users_parcels_details")
      .select("id")
      .eq("status", "In Progress"),
    supabase.from("users_parcels_details").select("id").eq("status", "Shipped"),
    supabase
      .from("users_parcels_details")
      .select("tracking_id, shipping_address, status, updated_at")
      .order("updated_at", { ascending: false })
      .limit(5),
  ]);

  // 4. Handle errors gracefully
  if (parcelsPerCountryError) parcelsPerCountry = [];
  if (parcelsReceivedError) parcelsReceived = [];
  if (parcelsProcessingError) parcelsProcessing = [];
  if (parcelsInProgressError) parcelsInProgress = [];
  if (parcelsShippedError) parcelsShipped = [];
  if (recentParcelsError) recentParcels = [];

  // 5. Shape the data
  const countryCount: Record<string, { country: string; total: number }> = {};
  parcelsPerCountry?.forEach((record: { country: string }) => {
    const country = record.country || "Unknown";
    if (!countryCount[country]) {
      countryCount[country] = { country, total: 0 };
    }
    countryCount[country].total += 1;
  });

  const shapedData = {
    "Parcels per country": Object.values(countryCount),
    "Parcels received": parcelsReceived?.length,
    "Parcels being processed": parcelsProcessing?.length,
    "Parcels in progress": parcelsInProgress?.length,
    "Parcels shipped": parcelsShipped?.length,
    "Recent parcels": recentParcels?.map((parcel: any) => ({
      id: parcel.tracking_id,
      destination: parcel.shipping_address,
      status: parcel.status,
      lastUpdate: parcel.updated_at?.toString() || null,
    })),
  };

  return NextResponse.json(shapedData);
}
