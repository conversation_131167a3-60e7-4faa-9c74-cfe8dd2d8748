import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { requireAdmin, createUnauthorizedResponse } from "@/lib/auth-helpers";

export async function POST(request: NextRequest) {
  try {
    // Require admin authentication for adding admin notes
    try {
      await requireAdmin();
    } catch (error) {
      return createUnauthorizedResponse("Admin access required");
    }

    const supabase = await createClient();
    const body = await request.json();
    const { orderId, adminNotes } = body;

    if (!orderId || typeof adminNotes !== "string") {
      return NextResponse.json(
        { error: "orderId and adminNotes are required." },
        { status: 400 },
      );
    }

    const { error } = await supabase
      .from("mp_ecommerce_orders")
      .update({
        admin_notes: adminNotes,
        updated_at: new Date().toISOString(),
      })
      .eq("id", orderId);

    if (error) {
      return NextResponse.json(
        { error: "Error adding admin note.", details: error.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ success: true });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
