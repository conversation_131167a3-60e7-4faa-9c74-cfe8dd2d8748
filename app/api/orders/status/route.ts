import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { requireAdmin, createUnauthorizedResponse } from "@/lib/auth-helpers";

// Helper to get the current timestamp in ISO format
function nowISO() {
  return new Date().toISOString();
}

export async function PATCH(request: NextRequest) {
  try {
    // Require admin authentication for order status updates
    try {
      await requireAdmin();
    } catch (error) {
      return createUnauthorizedResponse("Admin access required");
    }

    const supabase = await createClient();
    const body = await request.json();

    const { orderId, newStatus, notes, updatedBy } = body;

    if (!orderId || !newStatus) {
      return NextResponse.json(
        { error: "orderId and newStatus are required." },
        { status: 400 },
      );
    }

    // Fetch current order's status history
    const { data: currentOrder, error: fetchError } = await supabase
      .from("mp_ecommerce_orders")
      .select("status_history")
      .eq("id", orderId)
      .single();

    if (fetchError || !currentOrder) {
      return NextResponse.json(
        {
          error: "Error fetching current order.",
          details: fetchError?.message,
        },
        { status: 404 },
      );
    }

    // Create new status history entry
    const newStatusEntry = {
      status: newStatus,
      timestamp: nowISO(),
      notes: notes || undefined,
      updated_by: updatedBy || undefined,
    };

    // Update the status history array
    const updatedStatusHistory = [
      ...(currentOrder.status_history || []),
      newStatusEntry,
    ];

    // Prepare update data
    const updateData: any = {
      status: newStatus,
      status_history: updatedStatusHistory,
      updated_at: nowISO(),
    };

    // Set specific timestamp fields based on status
    switch (newStatus) {
      case "confirmed":
        updateData.confirmed_at = nowISO();
        break;
      case "shipped":
        updateData.shipped_at = nowISO();
        break;
      case "delivered":
        updateData.delivered_at = nowISO();
        break;
      case "cancelled":
        updateData.cancelled_at = nowISO();
        break;
    }

    const { error: updateError } = await supabase
      .from("mp_ecommerce_orders")
      .update(updateData)
      .eq("id", orderId);

    if (updateError) {
      return NextResponse.json(
        { error: "Error updating order status.", details: updateError.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ success: true });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
