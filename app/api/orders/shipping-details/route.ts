import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import {
  canAccessOrder,
  createUnauthorizedResponse,
  createForbiddenResponse,
} from "@/lib/auth-helpers";

// PATCH endpoint to update shipping details for an order
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { orderId, shippingDetails } = body;

    if (!orderId || !shippingDetails) {
      return NextResponse.json(
        { error: "orderId and shippingDetails are required." },
        { status: 400 },
      );
    }

    // Validate user can access this order (user owns it or is admin)
    const { canAccess, error: authError } = await canAccessOrder(orderId);

    if (!canAccess) {
      if (authError === "Authentication required") {
        return createUnauthorizedResponse(authError);
      }
      return createForbiddenResponse(authError || "Access denied");
    }

    const supabase = await createClient();

    // Fetch current shipping details
    const { data: currentOrder, error: fetchError } = await supabase
      .from("mp_ecommerce_orders")
      .select("shipping_details")
      .eq("id", orderId)
      .single();

    if (fetchError) {
      return NextResponse.json(
        { error: "Error fetching current order.", details: fetchError.message },
        { status: 500 },
      );
    }

    // Merge the existing shipping details with the new ones
    const updatedShippingDetails = {
      ...currentOrder?.shipping_details,
      ...shippingDetails,
    };

    const { error: updateError } = await supabase
      .from("mp_ecommerce_orders")
      .update({
        shipping_details: updatedShippingDetails,
        updated_at: new Date().toISOString(),
      })
      .eq("id", orderId);

    if (updateError) {
      return NextResponse.json(
        {
          error: "Error updating shipping details.",
          details: updateError.message,
        },
        { status: 500 },
      );
    }

    return NextResponse.json({ success: true });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
