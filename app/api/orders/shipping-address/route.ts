import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import {
  canAccessOrder,
  createUnauthorizedResponse,
  createForbiddenResponse,
} from "@/lib/auth-helpers";

// PATCH endpoint to modify shipping address for an order
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { orderId, shippingAddress } = body;

    if (!orderId || !shippingAddress) {
      return NextResponse.json(
        { error: "orderId and shippingAddress are required." },
        { status: 400 },
      );
    }

    // Validate user can access this order (user owns it or is admin)
    const { canAccess, error: authError } = await canAccessOrder(orderId);

    if (!canAccess) {
      if (authError === "Authentication required") {
        return createUnauthorizedResponse(authError);
      }
      return createForbiddenResponse(authError || "Access denied");
    }

    const supabase = await createClient();

    // Fetch current shipping address
    const { data: currentOrder, error: fetchError } = await supabase
      .from("mp_ecommerce_orders")
      .select("shipping_address")
      .eq("id", orderId)
      .single();

    if (fetchError) {
      return NextResponse.json(
        { error: "Error fetching current order.", details: fetchError.message },
        { status: 500 },
      );
    }

    // Merge the existing shipping address with the new one
    const updatedShippingAddress = {
      ...currentOrder?.shipping_address,
      ...shippingAddress,
    };

    // Update the order with the new shipping address
    const { error: updateError } = await supabase
      .from("mp_ecommerce_orders")
      .update({
        shipping_address: updatedShippingAddress,
        updated_at: new Date().toISOString(),
      })
      .eq("id", orderId);

    if (updateError) {
      return NextResponse.json(
        {
          error: "Error updating shipping address.",
          details: updateError.message,
        },
        { status: 500 },
      );
    }

    return NextResponse.json({ success: true });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
