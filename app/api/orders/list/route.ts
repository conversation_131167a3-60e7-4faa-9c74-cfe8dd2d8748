import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

/**
 * GET /api/orders/list
 * Query params:
 *   - status: string (optional)
 *   - searchTerm: string (optional)
 *   - page: number (default 1)
 *   - limit: number (default 10)
 *   - userId: string (optional)
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);

    const status = searchParams.get("status") || undefined;
    const searchTerm = searchParams.get("searchTerm") || undefined;
    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "10", 10);
    const userId = searchParams.get("userId") || undefined;

    const from = (page - 1) * limit;
    const to = from + limit - 1;

    let query = supabase
      .from("mp_ecommerce_orders")
      .select("*", { count: "exact" })
      .order("created_at", { ascending: false })
      .range(from, to);

    // Apply filters if provided
    if (userId) {
      query = query.eq("user_id", userId);
    }
    if (status) {
      query = query.eq("status", status);
    }
    if (searchTerm) {
      if (searchTerm.toUpperCase().startsWith("MP-")) {
        query = query.ilike("order_number", `%${searchTerm}%`);
      } else {
        if (userId) {
          query = query.ilike("order_number", `%${searchTerm}%`);
        } else {
          query = query.eq("user_id", searchTerm);
        }
      }
    }

    const { data, error, count } = await query;

    if (error) {
      return NextResponse.json(
        { error: "Error fetching orders.", details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      orders: data || [],
      total: count || 0,
      page,
      limit,
    });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 }
    );
  }
}
