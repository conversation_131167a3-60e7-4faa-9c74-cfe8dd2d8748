import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const orderId = decodeURIComponent(searchParams.get("orderId") || "");

    if (!orderId) {
      return NextResponse.json(
        { error: "orderId is required." },
        { status: 400 },
      );
    }

    const { data, error } = await supabase
      .from("mp_ecommerce_orders")
      .select("*")
      .eq("id", orderId)
      .single();

    if (error) {
      return NextResponse.json(
        { error: "Error fetching order by ID.", details: error.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ data });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
