import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

/**
 * PATCH /api/users/marketing-preference
 * Body: { uuid?: string, receiveMarketing: boolean }
 * If uuid is not provided, uses the currently authenticated user.
 */
export async function PATCH(request: NextRequest) {
  try {
    const supabase = await createClient();
    const body = await request.json();
    const { uuid, receiveMarketing } = body;

    if (typeof receiveMarketing !== "boolean") {
      return NextResponse.json(
        { error: "receiveMarketing (boolean) is required." },
        { status: 400 }
      );
    }

    // Determine user UUID: use provided or authenticated user
    let userUuid = uuid;
    if (!userUuid) {
      const { data: user, error: userError } = await supabase.auth.getUser();
      if (userError || !user?.user?.id) {
        return NextResponse.json(
          { error: "No authenticated user found." },
          { status: 401 }
        );
      }
      userUuid = user.user.id;
    }

    const { error } = await supabase
      .from("mp_users")
      .update({ receive_marketing: receiveMarketing })
      .eq("uuid", userUuid);

    if (error) {
      return NextResponse.json(
        { error: "Error updating marketing preference.", details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 }
    );
  }
}
