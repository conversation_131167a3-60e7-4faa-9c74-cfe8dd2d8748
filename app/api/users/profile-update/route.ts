import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

/**
 * PATCH /api/users/profile-update
 * Body: Partial user profile fields to update.
 * Requires the user to be authenticated.
 */
export async function PATCH(request: NextRequest) {
  try {
    const supabase = await createClient();
    const body = await request.json();

    // Get the currently authenticated user
    const { data: user, error: userError } = await supabase.auth.getUser();
    if (userError || !user?.user?.id) {
      return NextResponse.json(
        { error: "No authenticated user found." },
        { status: 401 }
      );
    }

    // Only allow updating allowed fields
    const allowedFields = [
      "first_name",
      "last_name",
      "phone_number",
      "receive_marketing",
      "signed_shipping_policy",
      "signed_privacy_policy",
      "country_code"
    ];
    const updateData: Record<string, any> = {};
    for (const key of allowedFields) {
      if (body[key] !== undefined) {
        updateData[key] = body[key];
      }
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: "No valid fields provided for update." },
        { status: 400 }
      );
    }

    const { error } = await supabase
      .from("mp_users")
      .update(updateData)
      .eq("uuid", user.user.id);

    if (error) {
      return NextResponse.json(
        { error: "Error updating user profile.", details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 }
    );
  }
}
