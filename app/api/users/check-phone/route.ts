import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const phoneNumber = decodeURIComponent(
      searchParams.get("phoneNumber") || "",
    );

    if (!phoneNumber) {
      return NextResponse.json(
        { error: "phoneNumber is required." },
        { status: 400 },
      );
    }

    const { data, error } = await supabase
      .from("mp_users")
      .select("phone_number")
      .eq("phone_number", phoneNumber)
      .limit(1);

    if (error) {
      return NextResponse.json(
        {
          error: "Error checking phone number existence.",
          details: error.message,
        },
        { status: 500 },
      );
    }

    const exists = data.length > 0;
    return NextResponse.json({ exists });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
