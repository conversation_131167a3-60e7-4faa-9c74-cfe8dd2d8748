import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

/**
 * GET /api/users/details-by-parcel
 * Query params:
 *   - parcelId: string (required, tracking ID)
 * Returns: User details (including address) for the user associated with the given parcel
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const parcelId = searchParams.get("parcelId");

    if (!parcelId) {
      return NextResponse.json(
        { error: "parcelId is required." },
        { status: 400 }
      );
    }

    // Fetch the parcel to get the user_id
    const { data: parcelData, error: parcelError } = await supabase
      .from("users_parcels_details")
      .select("user_id")
      .eq("tracking_id", parcelId)
      .single();

    if (parcelError || !parcelData?.user_id) {
      return NextResponse.json(
        { error: "Parcel not found or missing user_id.", details: parcelError?.message },
        { status: 404 }
      );
    }

    const userId = parcelData.user_id;

    // Fetch user data from mp_users
    const { data: userData, error: userError } = await supabase
      .from("mp_users")
      .select("*")
      .eq("id", userId)
      .single();

    if (userError || !userData) {
      return NextResponse.json(
        { error: "User not found.", details: userError?.message },
        { status: 404 }
      );
    }

    // Fetch address data from users_address
    const { data: addressData, error: addressError } = await supabase
      .from("users_address")
      .select("*")
      .eq("user_id", userId)
      .single();

    // Compose the response
    const userDetails = {
      id: userData.id,
      first_name: userData.first_name || "",
      last_name: userData.last_name || "",
      phone_number: userData.phone_number || "",
      email: userData.email || "",
      receive_marketing: userData.receive_marketing || false,
      signed_shipping_policy: userData.signed_shipping_policy || false,
      signed_privacy_policy: userData.signed_privacy_policy || false,
      country_code: userData.country_code || "",
      deliverTo: addressData?.DeliverTo || "",
      company: addressData?.Company || "",
      address: addressData?.Address || "",
      city: addressData?.City || "",
      stateProvince: addressData?.StateProvince || "",
      postalCode: addressData?.PostalCode || ""
    };

    return NextResponse.json({ data: userDetails });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 }
    );
  }
}
