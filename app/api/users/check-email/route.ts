import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

/**
 * GET /api/users/check-email?email=...
 * Returns { exists: boolean }
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const email = decodeURIComponent(searchParams.get("email") || "");

    if (!email) {
      return NextResponse.json(
        { error: "Email is required." },
        { status: 400 },
      );
    }

    const { data, error } = await supabase
      .from("mp_users")
      .select("email")
      .eq("email", email)
      .limit(1);

    if (error) {
      return NextResponse.json(
        { error: "Error checking email existence.", details: error.message },
        { status: 500 },
      );
    }

    const exists = Array.isArray(data) && data.length > 0;
    return NextResponse.json({ exists });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
