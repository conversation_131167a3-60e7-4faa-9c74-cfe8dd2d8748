import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

/**
 * GET /api/users/profile
 * Returns the profile of the currently authenticated user from the mp_users table.
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: user, error: userError } = await supabase.auth.getUser();

    if (userError || !user?.user) {
      return NextResponse.json(
        { error: "No authenticated user found." },
        { status: 401 }
      );
    }

    const { data, error } = await supabase
      .from("mp_users")
      .select("*")
      .eq("uuid", user.user.id)
      .single();

    if (error) {
      return NextResponse.json(
        { error: "Error fetching user data.", details: error.message },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: "User data not found." },
        { status: 404 }
      );
    }

    return NextResponse.json({ data });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 }
    );
  }
}


/**
 * POST /api/users/profile
 * Creates a new user in the mp_users table.
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Get the fields from the request body
    const body = await request.json();
    const {
      uuid,
      email,
      first_name,
      last_name,
      country_code,
      phone_number,
      receive_marketing,
      signed_shipping_policy,
      signed_privacy_policy
    } = body;

    // Validate required fields
    if (!uuid || !email || !first_name || !last_name || !phone_number) {
      return NextResponse.json(
        { error: "Missing required fields: uuid, email, first_name, last_name, phone_number" },
        { status: 400 }
      );
    }

    // Insert user details into mp_users table
    const { data, error } = await supabase
      .from("mp_users")
      .insert({
        uuid,
        email,
        first_name,
        last_name,
        country_code,
        phone_number,
        receive_marketing: receive_marketing || false,
        signed_shipping_policy: signed_shipping_policy || false,
        signed_privacy_policy: signed_privacy_policy || false
      })
      .select()
      .single();

    if (error) {
      return NextResponse.json(
        { error: "Error creating user.", details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ data }, { status: 201 });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 }
    );
  }
}