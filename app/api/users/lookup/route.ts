import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const email = decodeURIComponent(searchParams.get("email") || "");

    if (!email) {
      return NextResponse.json(
        { error: "Email is required." },
        { status: 400 },
      );
    }

    // Query the mp_users table by email
    const { data, error } = await supabase
      .from("mp_users")
      .select("email")
      .eq("email", email)
      .single();

    if (error) {
      return NextResponse.json(
        { error: "Error fetching user.", details: error.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ data });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
