import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedAdmin } from "@/lib/auth-helpers";

export async function GET(request: NextRequest) {
  try {
    // Use shared auth helper to obtain authenticated user + admin info
    const { user, admin, error } = await getAuthenticatedAdmin();

    // If no authenticated user, respond with 401 to be consistent with other endpoints
    if (error || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // If authenticated but not an admin, return empty result
    if (!admin) {
      return NextResponse.json({
        data: null,
        isAdmin: false,
      });
    }

    // Authenticated admin - return admin data
    return NextResponse.json({
      data: admin,
      isAdmin: true,
    });
  } catch (err: any) {
    console.error("Error checking admin status:", err);
    return NextResponse.json(
      { error: "Server error", details: err.message },
      { status: 500 },
    );
  }
}
