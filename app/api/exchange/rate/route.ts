import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const currency_code = decodeURIComponent(
      searchParams.get("currency_code") || "",
    );

    if (!currency_code) {
      return NextResponse.json(
        { error: "currency_code is required." },
        { status: 400 },
      );
    }

    const { data, error } = await supabase
      .from("mp_ecommerce_exchange_rates")
      .select("rate_per_gbp")
      .eq("currency_code", currency_code)
      .single();

    if (error) {
      return NextResponse.json(
        { error: "Error fetching exchange rate.", details: error.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ data });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
