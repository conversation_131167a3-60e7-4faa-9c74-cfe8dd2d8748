import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

/**
 * GET /api/products/check-sku?sku=0000001
 * Returns { exists: boolean }
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const sku = (searchParams.get("sku") || "").trim();

    if (!sku) {
      return NextResponse.json({ error: "SKU is required." }, { status: 400 });
    }

    // Validate format: exactly 7 digits
    if (!/^[0-9]{7}$/.test(sku)) {
      return NextResponse.json(
        { error: "SKU must be exactly 7 digits." },
        { status: 400 },
      );
    }

    const { data, error } = await supabase
      .from("mp_ecommerce_products")
      .select("sku")
      .eq("sku", sku)
      .limit(1);

    if (error) {
      console.error(error);
      return NextResponse.json(
        {
          error: "Error checking SKU existence.",
          details: error.message,
        },
        { status: 500 },
      );
    }

    const exists = Array.isArray(data) && data.length > 0;
    return NextResponse.json({ exists });
  } catch (err: any) {
    console.error(err);
    return NextResponse.json(
      { error: "Server error.", details: err?.message || String(err) },
      { status: 500 },
    );
  }
}
