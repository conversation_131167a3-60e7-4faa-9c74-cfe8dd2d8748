import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { requireAdmin, createUnauthorizedResponse } from "@/lib/auth-helpers";

export async function PATCH(request: NextRequest) {
  try {
    // Require admin authentication for product restoration
    try {
      await requireAdmin();
    } catch (error) {
      return createUnauthorizedResponse("Admin access required");
    }

    const supabase = await createClient();
    const body = await request.json();
    const { id } = body;

    if (!id) {
      return NextResponse.json(
        { error: "Product id is required." },
        { status: 400 },
      );
    }

    const { error } = await supabase
      .from("mp_ecommerce_products")
      .update({
        archived: false,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id);

    if (error) {
      return NextResponse.json(
        { error: "Error restoring product.", details: error.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ success: true });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
