import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

/**
 * GET /api/products/search
 * Query params:
 *   - name: string (required)
 *   - includeArchived: boolean (optional, default false)
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);

    const name = searchParams.get("name");
    const includeArchived = searchParams.get("includeArchived") === "true";

    if (!name) {
      return NextResponse.json(
        { error: "name is required." },
        { status: 400 }
      );
    }

    let query = supabase
      .from("mp_ecommerce_products")
      .select("*")
      .or(`title.ilike.%${name}%,description.ilike.%${name}%,tags.cs.["${name}"]`);

    if (!includeArchived) {
      query = query.eq("archived", false);
    }

    const { data, error } = await query;

    if (error) {
      return NextResponse.json(
        { error: "Error searching products by name.", details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ products: data || [] });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 }
    );
  }
}
