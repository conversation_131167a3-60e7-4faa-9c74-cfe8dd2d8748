import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { requireAdmin, createUnauthorizedResponse } from "@/lib/auth-helpers";

export async function GET(request: NextRequest) {
  try {
    // Require admin authentication for product variants access
    try {
      await requireAdmin();
    } catch (error) {
      return createUnauthorizedResponse("Admin access required");
    }

    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get("productId");

    if (!productId) {
      return NextResponse.json(
        { error: "productId is required." },
        { status: 400 },
      );
    }

    const { data, error } = await supabase
      .from("mp_ecommerce_products")
      .select("variants")
      .eq("id", productId)
      .single();

    if (error) {
      return NextResponse.json(
        { error: "Error fetching product variants.", details: error.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ variants: data || [] });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
