import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { requireAdmin, createUnauthorizedResponse } from "@/lib/auth-helpers";

export async function DELETE(request: NextRequest) {
  try {
    // Require admin authentication for product deletion
    try {
      await requireAdmin();
    } catch (error) {
      return createUnauthorizedResponse("Admin access required");
    }

    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const id = decodeURIComponent(searchParams.get("id") || "");

    if (!id) {
      return NextResponse.json(
        { error: "Product id is required." },
        { status: 400 },
      );
    }

    // Fetch product images for cleanup
    const { data: product, error: fetchError } = await supabase
      .from("mp_ecommerce_products")
      .select("primary_image, shared_images, variants")
      .eq("id", id)
      .single();

    if (fetchError) {
      return NextResponse.json(
        {
          error: "Error fetching product images.",
          details: fetchError.message,
        },
        { status: 500 },
      );
    }

    // Collect all image names to delete
    const imageNames: string[] = [];
    if (product?.primary_image?.name) {
      imageNames.push(product.primary_image.name);
    }
    if (Array.isArray(product?.shared_images)) {
      for (const img of product.shared_images) {
        if (img?.name) imageNames.push(img.name);
      }
    }
    // Collect variant images if present
    if (product?.variants?.variants) {
      for (const variant of product.variants.variants) {
        if (Array.isArray(variant.images)) {
          for (const img of variant.images) {
            if (img?.name) imageNames.push(img.name);
          }
        }
      }
    }

    // Delete images from storage
    if (imageNames.length > 0) {
      await supabase.storage.from("product-images").remove(imageNames);
    }

    // Delete product record
    const { error: deleteError } = await supabase
      .from("mp_ecommerce_products")
      .delete()
      .eq("id", id);

    if (deleteError) {
      return NextResponse.json(
        { error: "Error deleting product.", details: deleteError.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ success: true });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
