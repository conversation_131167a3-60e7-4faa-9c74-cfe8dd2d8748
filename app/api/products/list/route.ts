import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

/**
 * GET /api/products/list
 * Query params:
 *   - category: string (optional)
 *   - subcategory: string (optional)
 *   - type: "external" | "internal" (optional)
 *   - origin_location: "UK" | "Ghana" (optional)
 *   - condition: "New" | "Used" (optional)
 *   - limit: number (optional, default 10)
 *   - offset: number (optional, default 0)
 *   - includeArchived: boolean (optional, default false)
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);

    const category = searchParams.get("category") || undefined;
    const subcategory = searchParams.get("subcategory") || undefined;
    const type = searchParams.get("type") || undefined;
    const origin_location = searchParams.get("origin_location") || undefined;
    const condition = searchParams.get("condition") || undefined;
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = parseInt(searchParams.get("offset") || "0", 10);
    const includeArchived = searchParams.get("includeArchived") === "true";

    let query = supabase
      .from("mp_ecommerce_products")
      .select("*", { count: "exact" });

    // Filter out archived products by default unless includeArchived is true
    if (!includeArchived) {
      query = query.eq("archived", false);
    }

    if (category) {
      query = query.eq("category", category);
    }
    if (subcategory) {
      query = query.eq("subcategory", subcategory);
    }
    if (type) {
      query = query.eq("type", type);
    }
    if (origin_location) {
      query = query.eq("origin_location", origin_location);
    }
    if (condition) {
      query = query.eq("condition", condition);
    }
    if (limit) {
      query = query.limit(limit);
    }
    if (offset) {
      query = query.range(offset, offset + limit - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      return NextResponse.json(
        { error: "Error fetching products.", details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      data: data || [],
      count: count || 0,
      limit,
      offset,
    });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 }
    );
  }
}
