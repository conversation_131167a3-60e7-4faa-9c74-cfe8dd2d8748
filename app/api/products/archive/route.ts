import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { requireAdmin, createUnauthorizedResponse } from "@/lib/auth-helpers";

/**
 * PATCH /api/products/archive
 * Body: { id: string }
 * Archives a product by setting its `archived` field to true.
 */
export async function PATCH(request: NextRequest) {
  try {
    // Require admin authentication for product archiving
    try {
      await requireAdmin();
    } catch (error) {
      return createUnauthorizedResponse("Admin access required");
    }

    const supabase = await createClient();
    const body = await request.json();
    const { id } = body;

    if (!id) {
      return NextResponse.json(
        { error: "Product id is required." },
        { status: 400 },
      );
    }

    const { error } = await supabase
      .from("mp_ecommerce_products")
      .update({
        archived: true,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id);

    if (error) {
      return NextResponse.json(
        { error: "Error archiving product.", details: error.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ success: true });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
