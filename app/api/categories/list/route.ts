import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { requireAdmin, createUnauthorizedResponse } from "@/lib/auth-helpers";

export async function GET(request: NextRequest) {
  try {
    // Require admin authentication for fetching category list
    try {
      await requireAdmin();
    } catch (error) {
      return createUnauthorizedResponse("Admin access required");
    }

    const supabase = await createClient();

    const { data, error } = await supabase
      .from("mp_ecommerce_categories")
      .select("*");

    if (error) {
      return NextResponse.json(
        { error: "Error fetching categories.", details: error.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ data: data ?? [] });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
