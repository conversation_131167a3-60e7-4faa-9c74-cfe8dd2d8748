import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const categoryId = decodeURIComponent(searchParams.get("categoryId") || "");

    if (!categoryId) {
      return NextResponse.json(
        { error: "categoryId is required." },
        { status: 400 },
      );
    }

    const { data, error } = await supabase
      .from("mp_ecommerce_categories")
      .select("variant")
      .eq("id", categoryId)
      .single();

    if (error) {
      return NextResponse.json(
        {
          error: "Error fetching category variant attributes.",
          details: error.message,
        },
        { status: 500 },
      );
    }

    return NextResponse.json({ data: data?.variant ?? null });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
