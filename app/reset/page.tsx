"use client";

import { z } from "zod";
import Image from "next/image";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";
import Header from "@/components/index/header";
import { Button } from "@/components/ui/button";
import Branding from "@/components/app/branding";
import { zodResolver } from "@hookform/resolvers/zod";
import { AuthService } from "@/services/auth.service";
import { CheckCircle, ArrowRight } from "lucide-react";
import { ButtonLoading } from "@/components/app/button-loading";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { supabase } from "@/lib/supabase_client";

const formSchema = z
  .object({
    password: z.string().min(6, {
      message: "Password must be at least 6 characters",
    }),
    confirmPassword: z.string().min(6, {
      message: "Confirm password must be at least 6 characters",
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export default function Reset() {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [passwordResetSuccessful, setPasswordResetSuccessful] = useState(false);

  useEffect(() => {
    async function init() {
      const { data } = await supabase.auth.getUser();

      if (!data.user) {
        const code = new URLSearchParams(window.location.search).get("code");
        if (!code) {
          console.error("Missing code");
          return;
        }

        const { data: newSession, error: newSessionError } =
          await supabase.auth.exchangeCodeForSession(code);

        console.log("NEW SESSION DATA:", newSession.session);

        if (newSessionError) {
          console.log(newSessionError);
        }
      }
    }

    init();
  }, []);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsLoading(true);
    try {
      const authService = new AuthService();
      const result = await authService.resetUserPassword(values.password, values.confirmPassword);

      if (result === true) {
        setPasswordResetSuccessful(true);
        toast({
          title: "Success",
          description: "Password reset successful. Please log in with your new password.",
          variant: "default",
        });
        setTimeout(() => {
          router.push('/login');
        }, 3000);
      } else {
        toast({
          title: "Error",
          description: result as string,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error resetting password:", error);
      toast({
        title: "Error",
        description: "Failed to reset password. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (passwordResetSuccessful) {
    return <PasswordResetCompleted />;
  }

  return (
    <div className="h-screen w-screen">
      <Header />
      <div className="w-full h-[90%] lg:grid lg:grid-cols-2">
        {/* Left column with image - hidden on mobile */}
        <div className="hidden lg:block">
          <Branding />
        </div>

        {/* Right column with form */}
        <div className="flex flex-col items-center justify-center h-full p-4 lg:p-0">
          {/* Mobile logo and text */}
          <div className="mb-8 text-center lg:hidden">
            <Image src="/assets/imgs/logo.svg" alt="Logo" width={100} height={100} className="mx-auto mb-2" />
            <p className="text-lg font-semibold">Shop it, We Ship it</p>
          </div>

          <div className="w-full max-w-[350px] space-y-6">
            <div className="text-center space-y-2">
              <h1 className="text-2xl font-bold sm:text-3xl">Reset Password</h1>
              <p className="text-sm text-muted-foreground sm:text-base">Please choose secure password.</p>
            </div>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                {/* Password Fields */}
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="••••••••" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm Password</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="••••••••" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {!isLoading ? (
                  <Button type="submit" className="w-full">
                    Reset Password
                  </Button>
                ) : (
                  <ButtonLoading />
                )}
              </form>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
}

function PasswordResetCompleted() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-white text-slate-900 flex flex-col items-center justify-center p-4">
      <div className="w-40 h-12 mb-12">
        <Image src="/assets/imgs/logo.svg" alt="Company Logo" width={160} height={48} className="object-contain" priority />
      </div>

      <div className="relative w-full max-w-lg">
        {/* Background blur effects */}
        <div className="absolute top-0 -left-4 w-72 h-72 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute top-0 -right-4 w-72 h-72 bg-sky-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>

        {/* Content */}
        <div className="relative backdrop-blur-sm border border-slate-200 bg-white/80 rounded-3xl p-10 shadow-xl">
          <div className="flex items-center justify-center w-20 h-20 mx-auto mb-8 rounded-full bg-emerald-100">
            <CheckCircle className="text-emerald-600 h-10 w-10" />
          </div>

          <h1 className="text-4xl font-bold text-center mb-4">You're all set! 🎉</h1>

          <p className="text-slate-600 text-center mb-8">Your account password has been successfully reset.</p>

          <Button
            size="lg"
            onClick={() => router.push("/login")}
            className="w-full bg-slate-900 text-white hover:bg-slate-800 font-medium rounded-xl p-6 flex items-center justify-center gap-2 group"
          >
            Continue to Login
            <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      </div>
    </div>
  );
}