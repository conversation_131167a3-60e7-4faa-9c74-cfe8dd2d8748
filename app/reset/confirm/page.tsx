"use client";

import Image from "next/image";
import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import Header from "@/components/index/header";
import Branding from "@/components/app/branding";
import { supabase } from "@/lib/supabase_client";
import { EmailOtpType } from "@supabase/supabase-js";


function VerificationHandler() {
    const router = useRouter();
    const searchParams = useSearchParams();
  
    useEffect(() => {
      const token_hash = searchParams.get('token_hash');
      const type = searchParams.get('type') as EmailOtpType | null;
  
      const verifyOtp = async () => {
        if (token_hash && type) {
          const { error } = await supabase.auth.verifyOtp({
            type,
            token_hash,
          });
  
          if (error) {
            console.error("Verification error:", error.message);
            router.push("/reset/error");
            return;
          }
  
          // If verification successful, redirect to reset page
          router.push('/reset');
        } else {
          // If no token_hash or type, redirect to error
          router.push('/reset/error');
        }
      }
  
      verifyOtp();
    }, [searchParams, router]);
  
    return <p>Verifying your request...</p>;
  }

export default function ResetConfirm () {
  return (
    <div className="h-screen w-screen">
      <Header />
      <div className="w-full h-[90%] lg:grid lg:grid-cols-2">
        {/* Left column with image - hidden on mobile */}
        <div className="hidden lg:block">
          <Branding />
        </div>

        {/* Right column with form */}
        <div className="flex flex-col items-center justify-center h-full p-4 lg:p-0">
          {/* Mobile logo and text */}
          <div className="mb-8 text-center lg:hidden">
            <Image src="/assets/imgs/logo.svg" alt="Logo" width={100} height={100} className="mx-auto mb-2" />
            <p className="text-lg font-semibold">Shop it, We Ship it</p>
          </div>

          <div className="w-full max-w-[350px] space-y-6">
            <div className="flex items-center justify-center min-h-screen">
              <div className="text-center">
              <Suspense fallback={<p>Loading...</p>}>
                <VerificationHandler />
              </Suspense>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
