/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  env: {
    RESEND_API_KEY: process.env.RESEND_API_KEY,
  },
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'mailpallet-b85c2.web.app',
      },
      {
        protocol: 'https',
        hostname: 'mailpallet.com',
      },
    ],
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  webpack: (config, { isServer }) => {
    config.module = {
      ...config.module,
      rules: [
        ...config.module.rules,
        {
          test: /supabase.*\/functions.*\/.*\.(ts|js)$/,
          loader: 'ignore-loader',
        },
      ],
    };
    return config;
  },
};

export default nextConfig;